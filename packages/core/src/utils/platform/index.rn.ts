/**
 * React Native平台环境检测工具
 * 
 * 本文件提供React Native环境下的平台检测功能
 * 用于RN环境的特定处理
 */
import { taroEnv } from "../taroApi"
import { JDDevice } from '@jdreact/jdreact-core-lib'
import { Platform } from "react-native";

/**
 * 当前运行环境类型
 */
export const env: TaroGeneral.ENV_TYPE = taroEnv

export const isAndroid = Platform.OS === 'android';
export const isIOS = Platform.OS === 'ios';
/**
 * 是否为React Native环境
 * 在RN环境中固定为true
 */
export const isRN = true

// 判断是否为 PAD
function isTablet() {
    try {
        if (Platform.OS === 'ios') {
            if (Platform.isPad) {
                return true
            }
        }
        

        if (JDDevice?.isTabletDevice && typeof JDDevice?.isTabletDevice === 'function') {
            try {
                return JDDevice.isTabletDevice()
            } catch (e) {
                return false
            }
        }
    
        return false
    } catch (error) {
        return false
    }
}

export const isPad = isTablet()

