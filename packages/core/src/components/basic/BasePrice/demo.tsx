import React from 'react';
import { View } from '../view';
import { Text } from '../text';
import { BasePrice } from './index';

const HEADER_HEIGHT = 56;

export default function BasePriceDemo() {
  return (
    <View style={{ background: '#f7f8fa', minHeight: '100vh' }}>
      {/* 顶部标题栏，fixed定位 */}
      <View style={{ 
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 10,
        background: '#fff',
        padding: '0 20px',
        height: HEADER_HEIGHT,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
      }}>
        <Text style={{ 
          fontSize: 18, 
          fontWeight: 'bold',
          textAlign: 'center',
          lineHeight: HEADER_HEIGHT + 'px'
        }}>Price</Text>
      </View>

      {/* 内容区域，paddingTop避免被header遮挡 */}
      <View style={{ padding: '16px 20px', paddingTop: HEADER_HEIGHT + 16 }}>
        {/* 不保留小数 */}
        <Text style={{ 
          color: '#666', 
          fontSize: 16, 
          margin: '16px 0 12px 4px' 
        }}>不保留小数</Text>
        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <BasePrice 
            price={618} 
            symbolColor="#f2270c"
            integerColor="#f2270c"
            symbolFontSize={24}
            integerFontSize={32}
            decimalPlaces={0} 
          />
        </View>

        {/* 有人民币符号，无千位分隔 */}
        <Text style={{ 
          color: '#666', 
          fontSize: 16, 
          margin: '16px 0 12px 4px' 
        }}>有人民币符号，无千位分隔</Text>
        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <BasePrice 
            price={10010.01} 
            symbolColor="#f2270c"
            integerColor="#f2270c"
            decimalColor="#f2270c"
            symbolFontSize={24}
            integerFontSize={32}
            decimalFontSize={24}
          />
        </View>
        
        {/* 各种效果展示 */}
        <Text style={{ 
          color: '#666', 
          fontSize: 16, 
          margin: '24px 0 12px 4px' 
        }}>各种样式效果</Text>

        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>基础用法</Text>
          <BasePrice price={123.45} />
        </View>

        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>自定义颜色和字号</Text>
          <BasePrice
            price={12345.67}
            symbolColor="#0051cc"
            symbolFontSize={20}
            integerColor="#1a1a1a"
            integerFontSize={28}
            decimalColor="#fa2c19"
            decimalFontSize={16}
          />
        </View>

        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>删除线</Text>
          <BasePrice price={88.88} strikethrough />
        </View>

        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>自定义货币符号/无小数</Text>
          <BasePrice price={100} currencySymbol="$" showDecimal={false} />
        </View>

        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>小数部分更大</Text>
          <BasePrice price={0.99} integerFontSize={16} decimalFontSize={20} symbolFontSize={16} />
        </View>
      </View>
    </View>
  );
} 