import { CSSProperties } from 'react';
import { CustomTagProps } from '../CustomTag';

/**
 * 自定义标签列表属性接口
 */
export interface TaroCustomTagListProps {
  /** 标签列表 */
  tags: (CustomTagProps | string)[];
  /** 最大显示行数 */
  maxLines?: number;
  /** 行高 */
  lineHeight?: number;
  /** 标签间距 */
  gap?: number;
  /** 标签之间的垂直间距 */
  verticalGap?: number;
  /** 是否在不足容器宽度时拉伸填满容器 */
  stretch?: boolean;
  /** 截断模式: 显示省略号还是不显示 */
  truncateMode?: 'ellipsis' | 'none';
  /** 容器样式 */
  style?: CSSProperties;
  /** 标签列表容器样式 */
  tagsContainerStyle?: CSSProperties;
  /** 类名 */
  className?: string;
  /** 标签列表容器类名 */
  tagsContainerClassName?: string;
  /** 容器宽度 */
  width?: string | number;
}

/**
 * 兼容性导出类型
 */
export type CustomTagListProps = TaroCustomTagListProps; 