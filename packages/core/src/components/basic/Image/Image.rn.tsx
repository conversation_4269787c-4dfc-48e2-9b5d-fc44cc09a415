import { Image as RNImage } from "react-native";
import { JDImage } from '@jdreact/jdreact-core-lib';
import withClassName from "../utils/withClassName.rn";
import { useScreen } from "../../../hooks/useScreen";

function Image(props) {
    const { scaleStyle } = useScreen();
    let { rn, src, mode, defaultSource, onError, onLoad, style } = props;

    // 对style应用自动缩放（只处理对象类型的style）
    const scaledStyle = style && typeof style === 'object' && !Array.isArray(style) 
        ? scaleStyle(style as Record<string, any>) 
        : style;

    // 图片的截取设置
    const ModeList = { 'scaleToFill': 'contain', 'aspectFill': 'cover', 'aspectFit': 'stretch' };
    mode = ModeList[mode] || 'cover';

    if (rn) {
        // @ts-ignore
        return <RNImage resizeMode={mode} source={typeof src == 'string' && src?.indexOf?.('http') == 0 ? { uri: src } : src} style={scaledStyle as any} defaultSource={defaultSource} onError={onError} onLoad={onLoad} />
    }
    return <JDImage resizeMode={mode} source={typeof src == 'string' && src?.indexOf?.('http') == 0 ? { uri: src } : src} style={scaledStyle} defaultSource={defaultSource} onError={onError} onLoad={onLoad} />
}

export default withClassName()(Image) 