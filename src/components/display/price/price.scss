.price {
  // display: inline-flex;
  align-items: baseline;
  flex-direction: row;
  position: relative;


  &__current {
    font-weight: bold;
    display: flex;
    height: 100%;
    align-items: flex-end;
    
    // Default type specific styles
    &--default {
      color: #FF0000; // Red for current price
      font-size: 24px; // Larger font for current price
      margin-right: 8px;
    }
    // Discount type specific styles
    &--discount {
      color: #FFFFFF; // White text on discount bg
      font-size: 20px; // Adjust as needed
      z-index: 1; // Ensure text is above background elements
    }
  }

  &__original {
    display: flex;
    align-items: flex-end;
    margin-left: 2px;
    
    // Default type specific styles
    &--default {
      color: #888888; // Grey for original price
      font-size: 16px; // Smaller font for original price
      text-decoration: none;
    }
    // Discount type specific styles
    &--discount {
      color: rgba(255, 255, 255, 0.9); // Slightly transparent white
      font-size: 14px; // Adjust as needed
      margin-left: 8px;
      z-index: 1;
    }
    &--strikethrough {
      text-decoration: line-through;
    }
    
  }
  
  // Styles for the discount type specifically
  &__discount {
    position: relative;
    display: flex;

    &-background {
      border-radius: 5px;
      overflow: hidden;
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      z-index: 0;
    }
    
    
   &--absolute-container {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      right: 0;
      width: 100%;
      height: 100%;
      align-items: "baseline";
      flex-direction: row;
      z-index: 1;
      display: flex;
    }

    &--text-container {
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-direction: row;
    }

    &-current-container,
    &-original-container {
      display: flex;
      align-items: baseline;
    }

    // 左右偏移量通过内联样式的paddingLeft和paddingRight实现
  }
} 