module.exports = {
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
  plugins: ['taro-rn-best-practices'],
  extends: [
    'plugin:react/recommended',
    'plugin:taro-rn-best-practices/recommended',
  ],
  settings: {
    react: {
      version: 'detect',
    },
  },
  rules: {
    // 可以在这里覆盖或添加额外的规则
  },
};