import React from 'react';
import { MarketingFloor } from './index';

export default () => {
  // 模拟商品数据
  const products = [
    {
      id: '1',
      title: '百亿补贴 | 苹果iPhone 14',
      imageUrl: 'https://img.example.com/product1.jpg',
      description: '128GB 暗夜紫 官方标配',
      currentPrice: 5999,
      originalPrice: 6799,
      tags: ['限时', '热卖']
    },
    {
      id: '2',
      title: '百亿补贴 | 华为Mate 60 Pro',
      imageUrl: 'https://img.example.com/product2.jpg',
      description: '256GB 墨绿色 官方标配',
      currentPrice: 6999,
      originalPrice: 7699,
      tags: ['爆款', '直降']
    },
    {
      id: '3',
      title: '百亿补贴 | 小米14 Ultra',
      imageUrl: 'https://img.example.com/product3.jpg',
      description: '512GB 陶瓷黑 官方标配',
      currentPrice: 5999,
      originalPrice: 6499,
      tags: ['新品', '送礼']
    },
    {
      id: '4',
      title: '百亿补贴 | OPPO Find X7',
      imageUrl: 'https://img.example.com/product4.jpg',
      description: '256GB 冰晶白 官方标配',
      currentPrice: 4999,
      originalPrice: 5499,
      tags: ['限量', '抢购']
    },
    {
      id: '5',
      title: '百亿补贴 | vivo X100 Pro',
      imageUrl: 'https://img.example.com/product5.jpg',
      description: '512GB 钛银黑 官方标配',
      currentPrice: 5599,
      originalPrice: 6099,
      tags: ['新品', '热销']
    },
    {
      id: '6',
      title: '百亿补贴 | 三星Galaxy S24',
      imageUrl: 'https://img.example.com/product6.jpg',
      description: '256GB 幻影银 官方标配',
      currentPrice: 6999,
      originalPrice: 7899,
      tags: ['秒杀', '直降']
    },
    {
      id: '7',
      title: '百亿补贴 | 荣耀Magic6 Pro',
      imageUrl: 'https://img.example.com/product7.jpg',
      description: '512GB 翡翠绿 官方标配',
      currentPrice: 5299,
      originalPrice: 5999,
      tags: ['限时', '热卖']
    },
    {
      id: '8',
      title: '百亿补贴 | 一加12',
      imageUrl: 'https://img.example.com/product8.jpg',
      description: '256GB 玄武黑 官方标配',
      currentPrice: 4599,
      originalPrice: 4999,
      tags: ['爆款', '优惠']
    }
  ];

  const handleProductClick = (product: any, index: number) => {
    console.log('点击了商品:', product.title, '索引:', index);
  };

  const handleMoreClick = () => {
    console.log('点击了查看更多');
  };

  return (
    <div style={{ padding: '20px', maxWidth: '1200px', margin: '0 auto' }}>
      <h2 style={{ marginBottom: '20px' }}>营销楼层自动轮播示例</h2>
      
      <MarketingFloor
        subsidyImageUrl="https://img.example.com/subsidy-logo.png"
        officialSubsidyText="官方补贴"
        bubbleImageUrl="https://img.example.com/bubble.png"
        moreText="查看更多 >"
        products={products}
        autoplayInterval={3000}
        onProductClick={handleProductClick}
        onMoreClick={handleMoreClick}
        onCarouselChange={(index) => console.log('轮播切换到:', index)}
      />
    </div>
  );
}; 