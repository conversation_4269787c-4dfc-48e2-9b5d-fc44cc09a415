.marketing-floor {
  border-radius: 12px;
  overflow: hidden;
  width: 100%;

  &__header {
    // 头部已移至Header组件，这里只保留class名称用于连接
  }

  &__content {
    padding: 12px;
    padding-left: 8px;
    padding-right: 8px;
    position: relative;
  }

  &__panel-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 0;
  }

  &__carousel {
    height: 260px;
    position: relative;
    width: 100%;
  }

  &__carousel-item {
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    transition: opacity 0.5s, transform 0.5s;
    
    &--active {
      opacity: 1;
      transform: translateY(0);
      z-index: 2;
    }
    
    &--inactive {
      opacity: 0;
      transform: translateY(50px);
      z-index: 1;
    }
    
    &--slide-in {
      opacity: 1;
      transform: translateY(0);
      z-index: 2;
    }
    
    &--slide-out {
      opacity: 0;
      transform: translateY(-50px);
      z-index: 1;
    }
  }

  &__products {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    margin: 0;
    margin-left: -8px;
    margin-right: -8px;
  }

  &__product-item {
    box-sizing: border-box;
    margin-bottom: 16px;
    padding: 0;
    padding-left: 8px;
    padding-right: 8px;
  }
} 