import { ShopProductCardProps } from './types';
import { calculateShopHeaderHeight } from '../ShopHeader/utils';
import { calculateHorizontalListHeight } from '../HorizontalList/utils';

/**
 * 计算ShopProductCard组件的高度
 * @param props ShopProductCard的props
 * @returns 计算后的高度（像素）
 */
export const calculateShopProductCardHeight = (props: ShopProductCardProps): number => {
  const {
    shopInfo,
    productList,
    contentSpacing = 8,
    padding = 12,
    marginBottom = 8,
  } = props;

  // 1. 计算ShopHeader的高度
  const shopHeaderHeight = calculateShopHeaderHeight(shopInfo);
  // 2. 计算HorizontalList的高度（如果有商品数据）
  let horizontalListHeight = 0;
  if (productList && productList.items && productList.items.length > 0) {
    horizontalListHeight = calculateHorizontalListHeight(productList);
  }

  // 3. 计算内边距
  const { topPadding, bottomPadding } = getPaddingValues(padding);

  // 4. 计算总高度
  let totalHeight = shopHeaderHeight + topPadding + bottomPadding;

  // 如果有商品列表，加上商品列表高度和中间间距
  if (horizontalListHeight > 0) {
    totalHeight += horizontalListHeight + contentSpacing;
  }

  // 5. 加上marginBottom
  totalHeight += marginBottom;
  console.log('shopHeaderHeight====>',horizontalListHeight,totalHeight);

  return totalHeight;
};

/**
 * 从padding配置中提取上下内边距值
 * @param padding 内边距配置
 * @returns 上下内边距值
 */
function getPaddingValues(padding: number | { top?: number; right?: number; bottom?: number; left?: number }) {
  if (typeof padding === 'number') {
    return {
      topPadding: padding,
      bottomPadding: padding,
    };
  }
  
  return {
    topPadding: padding?.top || 0,
    bottomPadding: padding?.bottom || 0,
  };
} 