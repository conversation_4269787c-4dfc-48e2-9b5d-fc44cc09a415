# MarketingFloor 营销楼层

## 组件描述
MarketingFloor是一个用于展示促销商品的楼层组件，包括头部的"百亿补贴"和"官方补贴"标识，以及商品列表区域。支持多组商品的自动轮播展示，每组固定显示4个商品。组件根据平台使用不同的实现方式，确保良好的跨平台体验。

## 使用示例
```tsx
import { MarketingFloor } from '@/businessComponents';

export default () => {
  // 商品数据
  const products = [
    {
      id: '1',
      title: '百亿补贴 | 苹果iPhone 14',
      imageUrl: 'https://img.example.com/product1.jpg',
      description: '128GB 暗夜紫 官方标配',
      currentPrice: 5999,
      originalPrice: 6799,
    },
    // ... 更多商品数据
  ];

  return (
    <MarketingFloor
      subsidyImageUrl="https://img.example.com/subsidy-logo.png"
      bubbleImageUrl="https://img.example.com/bubble.png"
      moreText="查看更多 >"
      products={products}
      autoplayInterval={3000}
      onProductClick={(product, index) => console.log('点击商品', product.title)}
      onMoreClick={() => console.log('点击查看更多')}
    />
  );
};
```

## Props
| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| subsidyImageUrl | string | 否 | - | 顶部左侧百亿补贴图片地址 |
| subsidyImageWidth | number | 否 | 120 | 顶部左侧百亿补贴图片宽度 |
| subsidyImageHeight | number | 否 | 36 | 顶部左侧百亿补贴图片高度 |
| bubbleImageUrl | string | 否 | - | 顶部左侧气泡背景图片地址 |
| showMoreLink | boolean | 否 | true | 顶部右侧是否显示查看更多 |
| moreText | string | 否 | '查看更多 >' | 顶部右侧查看更多文案 |
| onMoreClick | () => void | 否 | - | 顶部右侧查看更多点击事件 |
| products | ProductCardProps[] | 是 | - | 商品数据列表，将严格按照4个一组展示，不足4个时不显示 |
| autoplayInterval | number | 否 | 5000 | 自动轮播间隔(毫秒)，0表示不自动轮播 |
| headerBackgroundColor | string | 否 | '#FFF5F5' | 顶部背景色 |
| headerTextColor | string | 否 | '#333333' | 顶部文字颜色 |
| headerFontSize | number | 否 | 14 | 顶部文字大小 |
| contentBackgroundColor | string | 否 | '#FFFFFF' | 底部背景色 |
| onCarouselChange | (index: number) => void | 否 | - | 轮播切换回调函数 |
| className | string | 否 | - | 自定义根元素类名 (Taro/Web) |
| style | React.CSSProperties | 否 | - | 自定义根元素内联样式 (Taro/Web) |
| containerStyle | ViewStyle | 否 | - | 自定义根元素样式 (RN) |
| onProductClick | (product: ProductCardProps, index: number) => void | 否 | - | 商品卡片点击事件 |

## 轮播规则
- 商品严格按照4个一组展示，不足4个时不显示组件
- 商品数量超过4个时，多余部分按照4个一组进行自动轮播
- 不满4个倍数的部分将被舍弃，例如7个商品只会显示前4个

## 平台差异
- Web平台：
  - 使用SCSS样式和className实现样式
  - 使用Animated.View实现上下滑动渐变动画效果
  - 同时维护两组产品视图，交替显示实现平滑过渡

- RN平台：
  - 使用StyleSheet和原生样式
  - 使用LayoutAnimation实现简单的切换动画
  - 只维护一组产品视图，通过自动布局更新实现切换效果
  - 对Android平台额外启用了LayoutAnimation支持

## 动画实现对比
- Web/Taro：使用两个ProductGroup组件交替显示，通过Animated.Value控制不透明度和位移
- RN：使用LayoutAnimation.configureNext在状态更新前配置动画，切换时自动应用过渡效果

## 注意事项
- 商品数据必须包含ProductCardProps所需的所有属性
- 商品数量不足4个时，组件不会显示
- 自定义图片时请确保图片尺寸合适
- 若需要支持点击事件，请提供对应的回调函数
- 在Android平台上，某些老旧设备可能不支持LayoutAnimation效果

## 更新日志
- v1.0.0 (2024-xx-xx): 首次发布
- v1.1.0 (2024-xx-xx): 添加了专门的RN实现，使用LayoutAnimation优化性能 