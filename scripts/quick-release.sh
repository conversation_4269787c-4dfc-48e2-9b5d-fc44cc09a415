#!/bin/bash

# 快速发布脚本 - 适用于日常开发的快速发布
# 使用方法: ./scripts/quick-release.sh [patch|minor|major]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取版本类型参数
VERSION_TYPE=${1:-patch}

echo -e "${BLUE}⚡ 快速发布模式${NC}"
echo -e "${GREEN}📋 版本类型: $VERSION_TYPE${NC}"
echo ""

# 设置 Node.js 路径
export PATH="/usr/local/n/versions/node/20.18.0/bin:$PATH"

# 1. 检查是否有未提交的更改
if [[ -n $(git status --porcelain) ]]; then
  echo -e "${YELLOW}📝 发现未提交的更改，是否先提交? (y/N)${NC}"
  read -r response
  if [[ "$response" =~ ^[Yy]$ ]]; then
    echo -e "${GREEN}💾 提交更改...${NC}"
    git add .
    echo "请输入提交信息:"
    read -r commit_message
    git commit -m "$commit_message"
  else
    echo -e "${RED}❌ 请先提交更改后再发布${NC}"
    exit 1
  fi
fi

# 2. 快速检查和构建
echo -e "${GREEN}🔍 快速检查...${NC}"
npx yarn install --frozen-lockfile

echo -e "${GREEN}🔨 构建所有包...${NC}"
npx yarn build

echo -e "${GREEN}🧪 运行测试...${NC}"
npx yarn test || {
  echo -e "${RED}❌ 测试失败，是否继续发布? (y/N)${NC}"
  read -r response
  if [[ ! "$response" =~ ^[Yy]$ ]]; then
    exit 1
  fi
}

# 3. 检查变更的包
echo -e "${GREEN}📋 检查变更的包...${NC}"
CHANGED_PACKAGES=$(npx lerna changed --loglevel silent || echo "")
if [ -z "$CHANGED_PACKAGES" ]; then
  echo -e "${YELLOW}⚠️  没有检测到包变更，强制发布所有包? (y/N)${NC}"
  read -r response
  if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}🚫 发布已取消${NC}"
    exit 0
  fi
  FORCE_PUBLISH="--force-publish"
else
  echo -e "${GREEN}📦 将要发布的包:${NC}"
  echo "$CHANGED_PACKAGES" | sed 's/^/  - /'
  FORCE_PUBLISH=""
fi

# 4. 确认发布
echo ""
echo -e "${YELLOW}🚀 准备发布 $VERSION_TYPE 版本，是否继续? (Y/n)${NC}"
read -r response
if [[ "$response" =~ ^[Nn]$ ]]; then
  echo -e "${BLUE}🚫 发布已取消${NC}"
  exit 0
fi

# 5. 执行发布
echo -e "${GREEN}📈 升级版本并发布...${NC}"
npx lerna version $VERSION_TYPE --conventional-commits --yes $FORCE_PUBLISH

echo -e "${GREEN}📤 发布到 npm...${NC}"
npx lerna publish from-git --yes

# 6. 推送到远程
echo -e "${GREEN}🏷️  推送到远程仓库...${NC}"
git push --follow-tags

# 7. 完成
echo ""
echo -e "${GREEN}✅ 快速发布完成！${NC}"
echo -e "${BLUE}🎉 版本 $VERSION_TYPE 已成功发布${NC}"

# 8. 显示下一步建议
echo ""
echo -e "${BLUE}💡 下一步建议:${NC}"
echo -e "${GREEN}  📋 检查发布状态: npm view @jd/lifeui-core${NC}"
echo -e "${GREEN}  🔍 验证安装: npm install @jd/lifeui-core@latest${NC}"
echo -e "${GREEN}  📚 更新文档: 更新 README 和 CHANGELOG${NC}"
