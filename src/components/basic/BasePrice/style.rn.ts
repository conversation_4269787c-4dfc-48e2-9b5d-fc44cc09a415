import { StyleSheet, TextStyle, ViewStyle } from 'react-native';
import { BasePriceProps } from './types'; // 导入 props 类型以获取动态值

// 辅助函数，将 props 中的样式相关值合并到 RN StyleSheet 中
// 注意：这里只是一个示例性的实现方式，具体实现可能需要根据项目的基础组件和样式策略调整
const createPriceStyles = (props: Pick<BasePriceProps, 
  'symbolColor' | 'symbolFontSize' | 'symbolTextStyle' |
  'integerColor' | 'integerFontSize' | 'integerTextStyle' |
  'decimalColor' | 'decimalFontSize' | 'decimalTextStyle' |
  'strikethrough' | 'containerStyle'>
) => {
  const { 
    symbolColor, symbolFontSize, symbolTextStyle,
    integerColor, integerFontSize, integerTextStyle,
    decimalColor, decimalFontSize, decimalTextStyle,
    strikethrough, containerStyle
  } = props;

  const commonTextDecoration: TextStyle = {};
  if (strikethrough) {
    commonTextDecoration.textDecorationLine = 'line-through';
    // commonTextDecoration.textDecorationColor = decimalColor || integerColor || symbolColor || '#000'; // 可选：删除线颜色
    // commonTextDecoration.textDecorationStyle = 'solid'; // 可选
  }

  return StyleSheet.create({
    // 对应 .base-price
    basePrice: {
      flexDirection: 'row',     // 对应 SCSS: display: inline-flex
      alignItems: 'baseline',   // 对应 SCSS: align-items: baseline
      ...containerStyle, // Merge containerStyle from props
      // lineHeight: 1, // RN 中 Text 组件通常不需要 lineHeight:1 来辅助对齐，但可根据实际测试添加
    } as ViewStyle, // 根元素是 View

    // 对应 .base-price--strikethrough (这个修饰符在 RN 中通过直接修改子元素 style 实现)
    // strikethroughModifier: commonTextDecoration, // 如果父元素 Text 应用删除线

    // 对应 .base-price__symbol
    symbol: {
      color: symbolColor,
      fontSize: symbolFontSize,
      ...commonTextDecoration, // 删除线应用于每个 Text 片段
      ...symbolTextStyle, // Merge symbolTextStyle from props
      // marginRight: 2, // 示例：如果 SCSS 中有固定间距
    } as TextStyle,

    // 对应 .base-price__integer
    integer: {
      color: integerColor,
      fontSize: integerFontSize,
      ...commonTextDecoration,
      ...integerTextStyle, // Merge integerTextStyle from props
    } as TextStyle,

    // 对应 .base-price__decimal-dot
    decimalDot: {
      color: decimalColor || integerColor, // 小数点颜色通常跟随小数或整数部分
      fontSize: decimalFontSize || integerFontSize, // 字体大小也类似
      ...commonTextDecoration,
      ...(decimalTextStyle || integerTextStyle || symbolTextStyle) // Fallback, or make it more specific
    } as TextStyle,

    // 对应 .base-price__decimal
    decimal: {
      color: decimalColor,
      fontSize: decimalFontSize,
      ...commonTextDecoration,
    } as TextStyle,
  });
};

export default createPriceStyles;
// 或者，如果组件内部直接消费 styles 对象：
// export const styles = createPriceStyles(defaultProps); // 需要定义 defaultProps
// 但更灵活的方式是组件在渲染时调用 createPriceStyles(props) 