# Diamond 组件

## 介绍

Diamond 组件用于展示可选择的带图标和文本的方块，通常用于选项列表，如商品规格选择等场景。

## 引入

```js
import { Diamond } from '@/pages/components/Diamond';
```

## 代码演示

### 基础用法

```jsx
<Diamond 
  width={80}
  imageSize={60}
  imageSrc="https://example.com/image.png"
  text="钻石1"
  selected={selected === 0}
/>
```

### 带渐变背景的钻石选项

```jsx
<Diamond 
  width={80}
  imageSize={60}
  imageSrc="https://example.com/image.png"
  text="带渐变1"
  selected={selected === 1}
  textSelectedBackgroundStartColor="#FF0000"
  textSelectedBackgroundEndColor="#FF9500"
  imageSelectedBorder={true}
  imageSelectedBorderColor="#FF0000"
/>
```

### 禁用状态

```jsx
<Diamond 
  width={80}
  imageSize={60}
  imageSrc="https://example.com/image.png"
  text="禁用选项"
  disabled={true}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| width | 组件宽度 | `number` | - |
| height | 组件高度 | `number` | - |
| paddingHorizontal | 水平内边距 | `number` | - |
| selected | 是否选中 | `boolean` | `false` |
| disabled | 是否禁用 | `boolean` | `false` |
| imageSize | 图片大小 | `number` | - |
| imageSrc | 图片源 | `string` | - |
| imageSelectedSrc | 选中状态图片源 | `string` | 同 imageSrc |
| imageSelectedSize | 选中状态图片大小 | `number` | 同 imageSize |
| imageSelectedBorder | 选中状态是否显示边框 | `boolean` | `false` |
| imageSelectedBorderColor | 选中状态边框颜色 | `string` | - |
| imageSelectedBorderRadius | 选中状态边框圆角 | `number` | - |
| textColor | 文字颜色 | `string` | `#1A1A1A` |
| textSelectedColor | 选中状态文字颜色 | `string` | `#FFF` |
| text | 文字内容 | `string` | - |
| textHeight | 文字区域高度 | `number` | 自适应 |
| textFontSize | 文字大小 | `number` | `12` |
| textSelectedFontSize | 选中状态文字大小 | `number` | 同 textFontSize |
| textSelectedBackgroundRadius | 选中状态文字背景圆角 | `number` | `4` |
| textSelectedBackgroundColor | 选中状态文字背景颜色 | `string` | - |
| textSelectedBackgroundStartColor | 选中状态文字背景渐变开始颜色 | `string` | - |
| textSelectedBackgroundEndColor | 选中状态文字背景渐变结束颜色 | `string` | - |
| textSelectedBackgroundPadding | 选中状态文字背景内边距 | `number` | `6` |
