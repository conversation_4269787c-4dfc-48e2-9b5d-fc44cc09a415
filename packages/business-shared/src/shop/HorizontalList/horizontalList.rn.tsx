import React from 'react';
import { FlatList, StyleSheet } from 'react-native';
import { View } from '../../../../core/src/components/basic/View';
import { CouponCard } from '../CouponCard';
import { HorizontalListProps, ListItemType } from './types';
import { ProductCard } from '../../product/ProductCard';

/**
 * 横向滚动列表组件 - React Native 实现
 */
const HorizontalList: React.FC<HorizontalListProps> = ({
  items = [],
  showScrollbar = false,
  itemSpacing = 12,
  horizontalPadding = 16,
  itemWidth = 'auto',
  renderItem: customRenderItem,
  emptyComponent,
  onEndReached,
  onScroll,
  onItemClick,
  onViewableItemsChanged,
  containerStyle,
  listStyle,
}) => {
  // 内部渲染列表项
  const renderItemInternal = ({ item, index }: { item: ListItemType; index: number }) => {
    const itemStyleObj = {
      marginRight: itemSpacing,
      width: typeof itemWidth === 'number' ? itemWidth : undefined,
    };

    // 如果提供了自定义渲染函数，则使用它
    if (customRenderItem) {
      const customResult = customRenderItem(item, index);
      // 确保返回的是React元素或null
      const content = React.isValidElement(customResult) ? customResult : null;
      
      return (
        <View style={StyleSheet.flatten([styles.item, itemStyleObj])}>
          {content}
        </View>
      );
    }

    // 根据项目类型渲染不同组件
    if (item.type === 'coupon') {
      // 兜底金额样式配置
      const defaultAmountStyle = {
        titleColor: '#8F4100',
        amountColor: '#8F4100',
        conditionColor: '#8F4100',
        amountFontSize: 26, 
        amountSymbolFontSize: 14,
        currencySymbol: '¥',
      };

      return (
        <View style={StyleSheet.flatten([styles.item, itemStyleObj])}>
          <CouponCard 
            {...defaultAmountStyle} 
            {...item.data} 
            onClick={() => {
              if (onItemClick) {
                onItemClick(item, index);
              }
            }}
          />
        </View>
      );
    }

    if (item.type === 'product') {
      // 兜底价格样式配置
      const defaultPriceStyle = {
        currencySymbol: '¥',
        // 主要价格样式
        currentPriceColor: '#FF0400',
        currentPriceFontSize: 14, 
        currentPriceSymbolFontSize: 10,
        currentPriceDecimalFontSize: 14, // 添加小数部分字体大小，与整数部分一致
        // 次要价格样式
        originalPriceColor: '#C2C4CC',
        originalPriceFontSize: 11,
        originalPriceSymbolFontSize: 11,
        originalPriceDecimalFontSize: 11, // 添加小数部分字体大小，与整数部分一致
        strikethrough: true,
      };
      
      return (
        <View style={StyleSheet.flatten([styles.item, itemStyleObj])}>
          <ProductCard 
            {...defaultPriceStyle} 
            {...item.data} 
            onClick={() => {
              if (onItemClick) {
                onItemClick(item, index);
              }
            }}
          />
        </View>
      );
    }

    return null;
  };

  // 返回列表项的key
  const keyExtractor = (_: any, index: number) => `item-${index}`;

  // 如果列表为空，显示空状态
  if (items.length === 0 && emptyComponent) {
    return (
      <View style={StyleSheet.flatten([styles.container, containerStyle])}>
        {emptyComponent}
      </View>
    );
  }

  // 列表头部组件，提供左边距
  const ListHeaderComponent = () => (
    <View style={{ width: horizontalPadding }} />
  );

  // 列表尾部组件，提供右边距
  const ListFooterComponent = () => (
    <View style={{ width: horizontalPadding }} />
  );

  // 列表项之间的分隔组件
  const ItemSeparatorComponent = () => (
    <View style={{ width: itemSpacing }} />
  );

  return (
    <View style={StyleSheet.flatten([styles.container, containerStyle])}>
      <FlatList
        data={items}
        renderItem={renderItemInternal}
        keyExtractor={keyExtractor}
        horizontal={true}
        showsHorizontalScrollIndicator={showScrollbar}
        onEndReached={onEndReached}
        onScroll={onScroll}
        onViewableItemsChanged={onViewableItemsChanged}
        onEndReachedThreshold={0.1}
        ListHeaderComponent={ListHeaderComponent}
        ListFooterComponent={ListFooterComponent}
        style={StyleSheet.flatten([styles.list, listStyle])}
        contentContainerStyle={styles.content}
        windowSize={2}
        maxToRenderPerBatch={3}
        initialNumToRender={4}
        removeClippedSubviews={true}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    // backgroundColor: 'red',
    // height: 110,
  },
  list: {
    flexGrow: 0,
  },
  content: {
    alignItems: 'flex-start',
  },
  item: {
    flexShrink: 0,
  },
});

export default HorizontalList; 