const stylelint = require('stylelint');

const ruleName = 'rn/no-unsupported-styles';
const messages = stylelint.utils.ruleMessages(ruleName, {
  noFixed: '禁止使用 position: fixed（React Native 不支持）',
  noUnsupported: (prop) => `禁止使用 React Native 不支持的样式属性: "${prop}"`,
  requireFlexDirection: 'display: flex 时必须显式声明 flex-direction',
  // onlyClassSelector: '只允许类选择器，禁止组合选择器、伪类、伪元素',
  onlyAllowedPosition: 'position 只允许 relative 或 absolute',
  noBackgroundImage: 'React Native 不支持 background-image',
});

// 按官方文档整理的 RN 支持的样式属性白名单
const allowedProps = [
  // Text
  'color', 'font-family', 'font-size', 'font-style', 'font-weight', 'line-height', 'text-align', 'text-decoration-line', 'text-shadow-color', 'text-shadow-offset', 'text-shadow-radius', 'include-font-padding', 'text-align-vertical', 'font-variant', 'letter-spacing', 'text-decoration-color', 'text-decoration-style', 'writing-direction',
  // Dimension
  'width', 'min-width', 'max-width', 'height', 'min-height', 'max-height',
  // Positioning
  'position', 'top', 'right', 'bottom', 'left', 'z-index',
  // Margin
  'margin', 'margin-horizontal', 'margin-vertical', 'margin-top', 'margin-right', 'margin-bottom', 'margin-left',
  // Padding
  'padding', 'padding-horizontal', 'padding-vertical', 'padding-top', 'padding-right', 'padding-bottom', 'padding-left',
  // Border
  'border-style', 'border-width', 'border-top-width', 'border-right-width', 'border-bottom-width', 'border-left-width',
  'border-color', 'border-top-color', 'border-right-color', 'border-bottom-color', 'border-left-color',
  'border-radius', 'border-top-left-radius', 'border-top-right-radius', 'border-bottom-left-radius', 'border-bottom-right-radius',
  'shadow-color', 'shadow-offset', 'shadow-radius', 'shadow-opacity',
  // Background
  'background-color',
  // Transform
  'transform', 'transform-matrix', 'backface-visibility',
  // Flexbox
  'flex', 'flex-grow', 'flex-shrink', 'flex-basis', 'flex-direction', 'flex-wrap', 'justify-content', 'align-items', 'align-self',
  // Other
  'opacity', 'overflow', 'elevation', 'resizeMode', 'overlayColor', 'tintColor',
];

// 不支持的属性黑名单补充
const forbiddenProps = [
  'float',
  'background-image',
  'box-shadow',
  'border-bottom-style', 'border-top-style', 'border-left-style', 'border-right-style',
  'border-image', 'border-collapse', 'border-spacing',
  'background',
];

// 只允许单一类选择器，如 .block、.block__elem
function isSingleClassSelector(selector) {
  // 只允许 .className 或 .className__elem
  return /^\.[a-zA-Z0-9_-]+$/.test(selector.trim());
}

module.exports = stylelint.createPlugin(ruleName, function () {
  return function (root, result) {
    root.walkRules(rule => {
      // 选择器校验：只允许单一类选择器
      // if (!isSingleClassSelector(rule.selector)) {
      //   stylelint.utils.report({
      //     message: messages.onlyClassSelector,
      //     node: rule,
      //     result,
      //     ruleName,
      //   });
      // }

      let hasDisplayFlex = false;
      let hasFlexDirection = false;

      rule.walkDecls(decl => {
        // 禁止黑名单属性
        if (forbiddenProps.includes(decl.prop)) {
          stylelint.utils.report({
            message: messages.noUnsupported(decl.prop),
            node: decl,
            result,
            ruleName,
          });
        }

        // 属性白名单校验
        if (!allowedProps.includes(decl.prop)) {
          stylelint.utils.report({
            message: messages.noUnsupported(decl.prop),
            node: decl,
            result,
            ruleName,
          });
        }

        // 禁止 background-image
        if (decl.prop === 'background-image') {
          stylelint.utils.report({
            message: messages.noBackgroundImage,
            node: decl,
            result,
            ruleName,
          });
        }

        // position 只允许 relative/absolute
        if (decl.prop === 'position' && !['relative', 'absolute'].includes(decl.value)) {
          stylelint.utils.report({
            message: messages.onlyAllowedPosition,
            node: decl,
            result,
            ruleName,
          });
        }

        // 禁止 position: fixed
        if (decl.prop === 'position' && decl.value === 'fixed') {
          stylelint.utils.report({
            message: messages.noFixed,
            node: decl,
            result,
            ruleName,
          });
        }

        // display: flex 检查
        if (decl.prop === 'display' && decl.value === 'flex') {
          hasDisplayFlex = true;
        }
        if (decl.prop === 'flexDirection' || decl.prop === 'flex-direction') {
          hasFlexDirection = true;
        }

        // 禁止 margin、padding 等属性多值写法
        if (decl.prop === 'margin' || decl.prop === 'padding') {
          // 检查值是否包含空格（即多值）
          if (typeof decl.value === 'string' && decl.value.trim().split(/\s+/).length > 1) {
            stylelint.utils.report({
              message: `${decl.prop} 只允许单值，React Native 不支持多值写法`,
              node: decl,
              result,
              ruleName,
            });
          }
        }
      });

      // display: flex 必须有 flex-direction
      if (hasDisplayFlex && !hasFlexDirection) {
        stylelint.utils.report({
          message: messages.requireFlexDirection,
          node: rule,
          result,
          ruleName,
        });
      }
    });
  };
});

module.exports.ruleName = ruleName;
module.exports.messages = messages; 