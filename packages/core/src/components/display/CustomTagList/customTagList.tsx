import React, { useRef, useState, useEffect, memo } from 'react';
import { View } from '../../basic/View';
import { CustomTag } from '../CustomTag';
import { TagType } from '../CustomTag/types';
import { TaroCustomTagListProps } from './types';
import './customTagList.scss';

/**
 * 估算文本宽度的函数（字符数乘以平均字符宽度）
 * 这是一个近似计算，用于替代DOM测量
 */
const estimateTextWidth = (text: string, fontSize = 14) => {
  // 平均字符宽度估算值（中文字符约为fontSize，英文字符约为fontSize/2）
  const charWidth = fontSize * 0.7;

  // 标签内部padding和边框的估计值
  const tagPadding = 16;

  // 根据文本长度估算宽度
  return text.length * charWidth + tagPadding;
};

/**
 * 自定义标签列表组件
 * 可以展示多个标签，支持设置显示行数，超过行数后截断
 */
const CustomTagList: React.FC<TaroCustomTagListProps> = ({
  tags = [],
  maxLines = 1,
  lineHeight = 32,
  gap = 8,
  verticalGap = 0,
  stretch = false,
  truncateMode = 'ellipsis',
  style,
  className = '',
  tagsContainerStyle,
  tagsContainerClassName = '',
  width,
}) => {
  const containerRef = useRef<any>(null);
  const [visibleTags, setVisibleTags] = useState<typeof tags>(tags);
  const [showEllipsis, setShowEllipsis] = useState(false);
  const [containerWidth, setContainerWidth] = useState<number>(typeof width === 'number' ? width : 300); // 默认宽度

  // 在容器宽度改变或初始化时设置宽度
  useEffect(() => {
    if (typeof width === 'number') {
      setContainerWidth(width);
    }
  }, [width]);

  // 计算可见标签，根据maxLines和容器宽度
  useEffect(() => {
    if (maxLines <= 0) return;

    const calculateVisibleTags = () => {
      // 如果没有设置最大行数，显示所有标签
      if (!maxLines || maxLines === Infinity) {
        setVisibleTags(tags);
        setShowEllipsis(false);
        return;
      }

      // 计算每个标签实际占用的空间
      let currentRowWidth = 0;
      let currentRow = 1;
      let lastVisibleIndex = -1;

      // 遍历所有标签，计算能显示多少个
      for (let i = 0; i < tags.length; i++) {
        // 获取标签文本
        const tag = tags[i];
        const text = typeof tag === 'string' ? tag : (tag as any).text || '标签';

        // 估算标签宽度
        const tagWidth = estimateTextWidth(text) + gap; // 标签宽度 + 间距

        // 如果当前行放不下这个标签，换行
        if (currentRowWidth + tagWidth > containerWidth && currentRowWidth > 0) {
          currentRow++;
          currentRowWidth = tagWidth;
        } else {
          currentRowWidth += tagWidth;
        }

        // 如果超过最大行数，停止添加
        if (currentRow > maxLines) {
          break;
        }

        lastVisibleIndex = i;
      }

      // 如果所有标签都可见，不显示省略号
      if (lastVisibleIndex >= tags.length - 1) {
        setVisibleTags(tags);
        setShowEllipsis(false);
      } else {
        // 否则，显示可见的标签和省略号
        setVisibleTags(tags.slice(0, lastVisibleIndex + 1));
        setShowEllipsis(true);
      }
    };

    // 初始计算
    calculateVisibleTags();
  }, [tags, maxLines, gap, containerWidth]);

  // 构建容器样式
  const containerStyle = {
    ...style,
    width: width,
  };

  // 构建标签容器样式
  const tagContainerStyle = {
    ...tagsContainerStyle,
    gap: `${verticalGap}px ${gap}px`,
  };

  // 构建类名
  const containerClassName = [
    'custom-tag-list',
    className
  ].filter(Boolean).join(' ');

  const tagContainerClassName = [
    'custom-tag-list__container',
    tagsContainerClassName
  ].filter(Boolean).join(' ');

  return (
    <View
      className={containerClassName}
      style={containerStyle}
      ref={containerRef}
    >
      <View
        className={tagContainerClassName}
        style={tagContainerStyle}
      >
        {visibleTags.map((tag, index) => (
          <View
            key={index}
            className={`custom-tag-list__tag-wrapper ${stretch ? 'custom-tag-list__tag-wrapper--stretch' : ''}`}
          >
            {typeof tag === 'string' ? (
              <CustomTag type={TagType.TEXT} text={tag} />
            ) : (
              <CustomTag type={(tag as any).type || TagType.TEXT} {...(tag as any)} />
            )}
          </View>
        ))}

        {showEllipsis && truncateMode === 'ellipsis' && (
          <View className="custom-tag-list__tag-wrapper">
            <CustomTag type={TagType.TEXT} text="..." />
          </View>
        )}
      </View>
    </View>
  );
};

export default memo(CustomTagList); 