{"name": "@jd/lifeui-core", "version": "1.0.0", "description": "本地生活基础UI组件库", "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/types/index.d.ts", "react-native": "src/index.ts", "scripts": {"dev": "vite --config vite.config.dev.js", "build": "vite build", "build:multi": "node ../../scripts/build-multiplatform.js .", "build:rn": "node ../../scripts/build-multiplatform.js . rn", "build:h5": "node ../../scripts/build-multiplatform.js . h5", "build:weapp": "node ../../scripts/build-multiplatform.js . weapp", "test": "jest", "lint": "eslint src"}, "files": ["dist", "src"], "dependencies": {}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-native": "^0.72.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-native": "^0.72.0"}}