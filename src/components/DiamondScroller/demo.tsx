import React, { useState } from 'react';
import { View } from '@tarojs/components';
import { DiamondScroller } from './index';

/**
 * DiamondScroller 组件示例
 */
const DiamondScrollerDemo: React.FC = () => {
  const [selectedId, setSelectedId] = useState<string>('');
  
  // 示例数据
  const items = [
    {
      id: '1',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '附近美食',
    },
    {
      id: '2',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '休闲玩乐',
    },
    {
      id: '3',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '丽人美发',
    },
    {
      id: '4',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '医疗医美',
    },
    {
      id: '5',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '运动健身',
    },
    {
      id: '6',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '饮品甜点',
    },
    {
      id: '7',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '足疗按摩',
    },
    {
      id: '8',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '洗浴汗蒸',
    },
    {
      id: '9',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '美睫美甲',
    },
    {
      id: '10',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: '口腔牙科',
    },
  ];

  // 点击处理函数
  const handleItemClick = (item, index, selected) => {
    console.log('点击了', item.text, '索引:', index, '选中状态:', selected);
    setSelectedId(selected ? item.id : '');
  };

  return (
    <View style={{ paddingTop: 10, paddingBottom: 10 }}>
      <View style={{ marginBottom: 20 }}>
        <View style={{ marginBottom: 10, paddingLeft: 10, fontWeight: 'bold' }}>单行模式</View>
        <DiamondScroller 
          items={items} 
          selectedId={selectedId}
          rows={1}
          onItemClick={handleItemClick}
        />
      </View>
      
      <View>
        <View style={{ marginBottom: 10, paddingLeft: 10, fontWeight: 'bold' }}>双行模式</View>
        <DiamondScroller 
          items={items} 
          selectedId={selectedId}
          rows={2}
          onItemClick={handleItemClick}
        />
      </View>
    </View>
  );
};

export default DiamondScrollerDemo;
