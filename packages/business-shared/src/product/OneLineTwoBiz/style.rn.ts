import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'column',
    width: 176,
    height: 252,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: '#fff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
    position: 'relative',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    borderTopLeftRadius: 8,
    borderTopRightRadius: 8,
    overflow: 'hidden',
  },
  distance: {
    position: 'absolute',
    left: 8,
    bottom: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: '#ffffff',
    fontSize: 12,
    padding: 4,
    paddingHorizontal: 6,
    borderRadius: 4,
  },
  content: {
    paddingHorizontal: 8,
    // paddingVertical: 7,
    display: 'flex',
    flexDirection: 'column',
    // gap: 6,
  },
  titleContainer: {
    marginBottom: 4,
  },
  priceSalesContainer: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  monthlySales: {
    fontSize: 12,
    color: '#999999',
  },
  discountLabel: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#FF1530',
    color: '#ffffff',
    fontSize: 12,
    padding: 2,
    paddingHorizontal: 8,
    borderBottomLeftRadius: 8,
  },
});
