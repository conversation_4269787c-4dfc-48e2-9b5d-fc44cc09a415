import { Image as RNImage, StyleSheet } from "react-native";
import { JDImage } from '@jdreact/jdreact-core-lib';
import { useMemo } from "react";
import withClassName from "../utils/withClassName.rn";

function Image(props) {

    let { rn, src, mode, defaultSource, onError, onLoad, style } = props;

    // 图片的截取设置
    const ModeList = { 'scaleToFill': 'contain', 'aspectFill': 'cover', 'aspectFit': 'stretch' };
    mode = ModeList[mode] || 'cover';
    const mStyle = useMemo(() => ({ resizeMode: mode }), [mode])


    // mode的对应方式
    const styleObj = [style, mStyle as any]

    if (rn) {
        return <RNImage source={typeof src == 'string' && src?.indexOf?.('http') == 0 ? { uri: src } : src} style={styleObj as any} defaultSource={defaultSource} onError={onError} onLoad={onLoad} />
    }
    return <JDImage source={typeof src == 'string' && src?.indexOf?.('http') == 0 ? { uri: src } : src} style={styleObj} defaultSource={defaultSource} onError={onError} onLoad={onLoad} />
}

export default withClassName()(Image)