{"name": "@jd/lifeui-business-travel", "version": "1.0.0", "description": "本地生活旅游业务组件库", "main": "dist/index.cjs.js", "module": "dist/index.es.js", "types": "dist/types/index.d.ts", "react-native": "src/index.ts", "scripts": {"dev": "vite --config vite.config.dev.js", "build": "vite build", "test": "jest", "lint": "eslint src"}, "files": ["dist", "src"], "dependencies": {"@jd/lifeui-core": "^1.0.0", "@jd/lifeui-business-shared": "^1.0.0"}, "devDependencies": {"@jd/lifeui-core": "^1.0.0", "@jd/lifeui-business-shared": "^1.0.0", "@types/react": "^18.0.0", "@types/react-native": "^0.72.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-native": "^0.72.0"}}