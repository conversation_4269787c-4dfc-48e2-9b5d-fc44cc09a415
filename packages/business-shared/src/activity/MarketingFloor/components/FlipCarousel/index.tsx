import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import {View} from '../../../../../../core/src/components/basic/View';
import { FlipCarouselProps } from './types';
import './index.scss';

/**
 * 3D翻转轮播组件
 * 支持自动轮播和手动滑动切换
 */
const FlipCarousel: React.FC<FlipCarouselProps> = ({
  data,
  itemsPerPage = 4,
  autoPlayInterval = 3000,
  animationDuration = 500,
  renderItem,
  pageHeight = 100,
  style,
  contentContainerStyle,
  pageContentStyle,
  onPageChange,
}) => {
  // 当前页码状态
  const [currentPage, setCurrentPage] = useState(0);
  // 动画状态 - 使用ref而非state，避免不必要的重新渲染
  const isFlippingRef = useRef(false);
  // 定时器引用
  const timerRef = useRef<any>();
  // 动画元素引用
  const frontPageRef = useRef<any>(null);
  const backPageRef = useRef<any>(null);

  // 计算总页数
  const totalPages = Math.ceil(data.length / itemsPerPage);

  /**
   * 获取当前页的数据
   */
  const getCurrentPageData = () => {
    const start = currentPage * itemsPerPage;
    const end = start + itemsPerPage;
    return data.slice(start, end);
  };

  /**
   * 获取下一页的数据
   */
  const getNextPageData = () => {
    const nextPage = (currentPage + 1) % totalPages;
    const start = nextPage * itemsPerPage;
    const end = start + itemsPerPage;
    return data.slice(start, end);
  };

  /**
   * 执行翻页动画
   */
  const flipPage = () => {
    if (isFlippingRef.current || totalPages <= 1) return;

    isFlippingRef.current = true;

    // 设置背面页的初始状态
    if (backPageRef.current) {
      backPageRef.current.style.transform = `rotateX(90deg) translateY(${pageHeight / 2}px)`;
      backPageRef.current.style.opacity = '1';
    }

    // 设置正面页的初始状态
    if (frontPageRef.current) {
      frontPageRef.current.style.transform = 'rotateX(0deg) translateY(0px)';
      frontPageRef.current.style.opacity = '1';
    }

    // 添加动画类
    setTimeout(() => {
      if (frontPageRef.current) {
        frontPageRef.current.style.transition = `transform ${animationDuration}ms`;
        frontPageRef.current.style.transform = `rotateX(-90deg) translateY(0) scaleY(0)`;
      }

      if (backPageRef.current) {
        backPageRef.current.style.transition = `transform ${animationDuration}ms`;
        backPageRef.current.style.transform = 'rotateX(0deg) translateY(0px)';
      }

      // 动画结束后切换页面
      setTimeout(() => {
        isFlippingRef.current = false;
        const nextPage = (currentPage + 1) % totalPages;
        setCurrentPage(nextPage);
        
        // 触发翻页完成回调
        if (onPageChange) {
          const start = nextPage * itemsPerPage;
          const end = start + itemsPerPage;
          onPageChange(data.slice(start, end));
        }
      }, animationDuration);
    }, 50);
  };

  // 重置动画状态
  const resetAnimation = () => {
    // nextTick(() => {
      if (frontPageRef.current) {
        frontPageRef.current.style.transition = 'none';
        frontPageRef.current.style.transform = 'rotateX(0deg) translateY(0px)';
      }

      if (backPageRef.current) {
        backPageRef.current.style.transition = 'none';
        backPageRef.current.style.transform = `rotateX(90deg) translateY(${pageHeight / 2}px)`;
      }
    // });
  };

  // 监听当前页变化，重置动画
  useLayoutEffect(() => {
    resetAnimation();
  }, [currentPage]);

  /**
   * 自动轮播效果
   */
  useEffect(() => {
    if (autoPlayInterval && totalPages > 1) {
      timerRef.current = setTimeout(() => {
        flipPage();
      }, autoPlayInterval);

      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }
      };
    }
  }, [currentPage, totalPages, autoPlayInterval]);

  // 组件卸载时清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  // 当前页数据
  const currentData = getCurrentPageData();
  // 下一页数据
  const nextData = getNextPageData();

  return (
    <View className="flip-carousel__container" style={style}>
      <View 
        className="flip-carousel__content" 
        style={{ 
          height: pageHeight,
          ...contentContainerStyle 
        }}
      >
        {/* 当前页 */}
        <View
          ref={frontPageRef}
          className="flip-carousel__page flip-carousel__front-page"
          style={{
            transformOrigin: 'top center',
            transform: 'rotateX(0deg) translateY(0px)',
            backfaceVisibility: 'hidden',
            opacity: 1,
            transition: 'none',
          }}
        >
          <View 
            className="flip-carousel__page-content" 
            style={pageContentStyle}
          >
            {currentData.map((item, index) => (
              renderItem(item, currentPage * itemsPerPage + index)
            ))}
          </View>
        </View>

        {/* 下一页（背面） */}
        <View
          ref={backPageRef}
          className="flip-carousel__page flip-carousel__back-page"
          style={{
            transform: `rotateX(90deg) translateY(${pageHeight / 2}px)`,
            backfaceVisibility: 'hidden',
            transition: 'none',
          }}
        >
          <View 
            className="flip-carousel__page-content"
            style={pageContentStyle}
          >
            {nextData.map((item, index) => (
              renderItem(item, ((currentPage + 1) % totalPages) * itemsPerPage + index)
            ))}
          </View>
        </View>
      </View>

      {/* 分页指示器 */}
      {/* {totalPages > 1 && (
        <View className="flip-carousel__pagination">
          {Array.from({ length: totalPages }).map((_, index) => (
            <View
              key={index}
              className={`flip-carousel__pagination-dot ${
                index === currentPage % totalPages ? 'flip-carousel__pagination-dot--active' : ''
              }`}
            />
          ))}
        </View>
      )} */}
    </View>
  );
};

export default FlipCarousel;