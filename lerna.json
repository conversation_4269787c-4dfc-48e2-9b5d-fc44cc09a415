{"packages": ["packages/*"], "version": "independent", "npmClient": "yarn", "command": {"publish": {"conventionalCommits": true, "message": "chore(release): publish", "registry": "https://registry.npmjs.org/", "ignoreChanges": ["**/*.md", "**/*.test.ts", "**/*.test.tsx", "**/test/**", "**/tests/**"]}, "version": {"allowBranch": ["master", "main", "release/*"], "conventionalCommits": true, "message": "chore(release): version packages"}, "build": {"ignore": ["docs", "examples"]}}}