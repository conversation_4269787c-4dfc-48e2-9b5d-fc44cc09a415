import React from 'react';
import View from '../../basic/View';
import Image from '../../basic/Image';
import { BasePrice } from '../../basic/BasePrice';
import { PriceProps } from './types';
import {Background} from '../../basic/Background';
import './price.scss';

/**
 * 高阶价格组件
 * 支持类型： 可自定义背景图
 * 
 * @param {object} props - 组件属性
 * @param {number} props.currentPrice - 当前价格｜ 必须
 * @param {number} props.originalPrice - 原价 
 * @param {PriceType} props.type - 价格展示样式
 * @param {string} props.currencySymbol - 货币符号
 * @param {number} props.decimalPlaces - 小数位数
 * @param {boolean} props.strikethrough - 是否显示删除线
 * @param {number}  props.priceTagWidth - 价格标签宽度 | 宽度不够是只显示现价
 * 
 * 
 * --------------------------
 * @param {string} [props.currentPriceColor] - 当前价格数字的颜色
 * @param {number} [props.currentPriceFontSize] - 当前价格的字体大小
 * @param {number} [props.currentPriceSymbolFontSize] - 当前价格货币符号的字体大小
 * @param {number} [props.currentPriceDecimalFontSize] - 当前价格小数部分的字体大小
 * 
 * --------------------------
 * @param {string} [props.originalPriceColor] - 原价数字的颜色
 * @param {number} [props.originalPriceFontSize] - 原价的字体大小
 * @param {number} [props.originalPriceSymbolFontSize] - 原价货币符号的字体大小
 * @param {number} [props.originalPriceDecimalFontSize] - 原价小数部分的字体大小
 * 
 * --------------------------
 * @param {string} [props.discountBackgroundImageSrc] - [Discount Type Only] 背景图片 URL
 * @param {number} [props.discountBackgroundImageWidth] - [Discount Type Only] 背景图片宽度
 * @param {number} [props.discountBackgroundImageHeight] - [Discount Type Only] 背景图片高度
 * @param {number} [props.discountCurrentPriceLeftOffset] - [Discount Type Only] 当前价格（左侧价格）距离背景左边的间距
 * @param {number} [props.discountOriginalPriceRightOffset] - [Discount Type Only] 原价（右侧价格）距离背景右边的间距
 * 
 * --------------------------
 * @param {string} [props.className] - 自定义根元素类名 (Taro/Web)
 * @param {object} [props.style] - 自定义根元素内联样式 (Taro/Web)
 * @param {ViewStyle} [props.containerStyle] - 自定义根元素样式 (RN)
 * 
 */
const Price: React.FC<PriceProps> = ({
  currentPrice,
  originalPrice,
  type = 'default',
  currencySymbol = '¥',
  decimalPlaces = 2,
  strikethrough = true,
  priceTagWidth,
  currentPriceColor,
  currentPriceFontSize,
  currentPriceSymbolFontSize,
  currentPriceDecimalFontSize,
  originalPriceColor,
  originalPriceFontSize,
  originalPriceSymbolFontSize,
  originalPriceDecimalFontSize,
  discountBackgroundImageSrc,
  discountBackgroundImageWidth,
  discountBackgroundImageHeight,
  discountCurrentPriceLeftOffset = 4,
  discountOriginalPriceRightOffset = 4,
  className,
  style,
}) => {
  // 构建类名
  const rootClassName = [
    'price',
    `price__${type}`,
    className
  ].filter(Boolean).join(' ');


  // 默认样式配置
  const defaultStylesConfig = {
    default: {
      current: {
        color: '#FF0000', // 红色
        fontSize: 24,
        symbolFontSize: 20,
        decimalFontSize: 18,
      },
      original: {
        color: '#999999', // 灰色
        fontSize: 18,
        symbolFontSize: 14,
        decimalFontSize: 14,
      }
    },
    discount: {
      current: {
        color: '#FFFFFF', // 白色
        fontSize: 24,
        symbolFontSize: 18,
        decimalFontSize: 18,
      },
      original: {
        color: '#FF0000', // 红色
        fontSize: 18,
        symbolFontSize: 14,
        decimalFontSize: 14,
      }
    }
  };

  // 当前样式主题
  const theme = defaultStylesConfig[type];

  // 构建当前价格样式
  const currentPriceBaseProps = {
    price: currentPrice,
    currencySymbol,
    decimalPlaces,
    symbolColor: currentPriceColor || theme.current.color,
    symbolFontSize: currentPriceSymbolFontSize || theme.current.symbolFontSize,
    integerColor: currentPriceColor || theme.current.color,
    integerFontSize: currentPriceFontSize || theme.current.fontSize,
    decimalColor: currentPriceColor || theme.current.color,
    decimalFontSize: currentPriceDecimalFontSize || theme.current.decimalFontSize,
  };

  // 构建原价样式
  const originalPriceBaseProps = originalPrice ? {
    price: originalPrice,
    currencySymbol,
    decimalPlaces,
    symbolColor: originalPriceColor || theme.original.color,
    symbolFontSize: originalPriceSymbolFontSize || theme.original.symbolFontSize,
    integerColor: originalPriceColor || theme.original.color,
    integerFontSize: originalPriceFontSize || theme.original.fontSize,
    decimalColor: originalPriceColor || theme.original.color,
    decimalFontSize: originalPriceDecimalFontSize || theme.original.decimalFontSize,
    strikethrough: strikethrough || type === 'discount',
  } : null;

  // 构建根样式
  const rootWebStyle: React.CSSProperties = {
    ...style,
    width: priceTagWidth,
    display: 'flex',
    flexWrap: 'wrap',
    overflow: 'hidden',
    alignItems: 'baseline',
    height: (currentPriceFontSize + 9) || (theme.current.fontSize + 10),
  };

  /**
   * 
   */
  if (type === 'discount') {
    const discountRootStyle: React.CSSProperties = {
      ...rootWebStyle,
      position: 'relative',
      width: discountBackgroundImageWidth,
      height: discountBackgroundImageHeight,
      // display: rootWebStyle.display || 'inline-block',
    };

    return (
      <View className={rootClassName} style={discountRootStyle}>
        {/**
         * background image
         */}
        {discountBackgroundImageSrc && (
          <View className="price__discount-background">
            <Image
              src={discountBackgroundImageSrc}
              mode='aspectFit'
              style={{
                width: discountBackgroundImageWidth ? discountBackgroundImageWidth : '100%',
                height: discountBackgroundImageHeight ? discountBackgroundImageHeight : '100%'
              }}
            />
            {/* <Background 
              backgroundWidth={discountBackgroundImageWidth}
              backgroundHeight={discountBackgroundImageHeight}
              backgroundImageURL={discountBackgroundImageSrc}
              backgroundBorderRadius={10}
            /> */}
          </View>
        )}
        <View className='price__discount--absolute-container'>
          <View
            className='price__discount--text-container'
            style={{justifyContent: originalPriceBaseProps ?  'space-between' : 'flex-start'}}
          >
            <View
              className="price__discount-current-container"
              style={{
                left: discountCurrentPriceLeftOffset,
              }}
            >
              <BasePrice {...currentPriceBaseProps} />
            </View>
            {originalPriceBaseProps && (
              <View
                className="price__discount-original-container"
                style={{
                  right: discountOriginalPriceRightOffset,
                }}
              >
                <BasePrice {...originalPriceBaseProps} />
              </View>
            )}
          </View>
        </View>
      </View>
    );
  }

  // Default type rendering
  return (
    <View className={rootClassName} style={rootWebStyle}>
        <View className="price__current" style={{flexShrink: 0}}>
          <BasePrice {...currentPriceBaseProps} />
        </View>
        {originalPriceBaseProps && (
          <View className="price__original" style={{flexShrink: 0, alignItems: 'flex-end'}}>
            <BasePrice {...originalPriceBaseProps} />
          </View>
        )}
    </View>
  );
};

export default Price; 