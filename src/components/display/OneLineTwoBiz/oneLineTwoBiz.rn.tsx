import React from 'react';
import * as R from 'ramda';
import { StyleProp, ViewStyle } from 'react-native';
import { ShopImageTag } from '../../../components/ShopImageTag';
import { Title } from '../title';
import { Price } from '../price';
import View from '../../basic/View';
import Text from '../../basic/Text';
import { OneLineTwoBizProps } from './types';
import styles from './style.rn';
import { CustomTag } from '../../CustomTag';
import { TagType } from '../../CustomTag/types';

/**
 * 一行两业务卡片组件（React Native版）
 */
interface RNProps extends Omit<OneLineTwoBizProps, 'style'> {
  style?: StyleProp<ViewStyle>;
}

const OneLineTwoBiz: React.FC<RNProps> = (props) => {
  const {
    // 整体商卡参数
    style,
    onClick,

    // 商品图片参数
    imageSrc,
    imageSize = 142,
    imageTag,
    distance,

    // 产品名称参数
    productName,
    productNameFontSize = 14,
    productNameLines = 1,
    productNameEllipsis = 'ellipsis',
    productNameTag,
    productNameStyle,

    // 商品价格参数
    currentPrice,
    originalPrice,
    priceType = 'default',
    currentPriceColor = '#FF1530',
    originalPriceColor = '#999999',
    currentPriceFontSize = 20,
    originalPriceFontSize = 14,
    currencySymbol = '¥',
    currentPriceSymbolFontSize,
    originalPriceSymbolFontSize,
    currentPriceDecimalFontSize,
    originalPriceDecimalFontSize,
    strikethrough = true,

    // 销量信息
    monthlySales,
    monthlySalesLabel = '月售',

    // 价格折扣信息
    discountPrefixIcon,
    discountText,
  } = props;

  // 处理点击事件
  const handleClick = R.defaultTo(() => { }, onClick);

  // 处理标签数据，确保是数组类型
  const formatTags = R.ifElse(
    R.isNil,
    R.always([]),
    R.ifElse(
      Array.isArray,
      R.identity,
      R.of
    )
  );

  console.log(discountText, discountPrefixIcon, 'discountPrefixIcondiscountPrefixIcon')

  // 渲染视图
  return (
    <View
      style={[styles.container, style]}
      onClick={handleClick}
    >
      <View style={styles.imageContainer}>
        {/* 商品图 */}
        <ShopImageTag
          src={imageSrc}
          size={imageSize}
          tag={imageTag}
        />
      </View>

      <View style={styles.content}>
        {/* 产品名称 */}
        {productName && (
          <View style={styles.titleContainer}>
            <Title
              fontSize={productNameFontSize}
              lines={productNameLines}
              ellipsis={productNameEllipsis}
              tags={formatTags(productNameTag)}
              style={productNameStyle}
            >
              {productName}
            </Title>
          </View>
        )}

        {/* 价格和销量信息 */}
        <View style={styles.priceSalesContainer}>
          {/* 价格信息 */}
          <View style={styles.priceContainer}>
            <Price
              currentPrice={currentPrice}
              originalPrice={originalPrice}
              type={priceType}
              currentPriceColor={currentPriceColor}
              originalPriceColor={originalPriceColor}
              currentPriceFontSize={currentPriceFontSize}
              originalPriceFontSize={originalPriceFontSize}
              currencySymbol={currencySymbol}
              currentPriceSymbolFontSize={currentPriceSymbolFontSize}
              originalPriceSymbolFontSize={originalPriceSymbolFontSize}
              currentPriceDecimalFontSize={currentPriceDecimalFontSize}
              originalPriceDecimalFontSize={originalPriceDecimalFontSize}
              strikethrough={strikethrough}
            />
          </View>

          {/* 月销量信息 */}
          {monthlySales && (
            <Text style={styles.monthlySales}>
              {monthlySalesLabel}{monthlySales}
            </Text>
          )}
        </View>


        {/* 折扣标签 */}
        {
          (discountPrefixIcon && discountText) && <CustomTag
            type={TagType.ICON_PREFIX}
            text={discountText}
            iconSrc={discountPrefixIcon.imgUrl}
            iconStyle={{
              width: discountPrefixIcon.width,
              height: discountPrefixIcon.height,
              marginRight: 4
            }}
            style={{
              backgroundColor: '#FFF3F5',
              // @ts-ignore
              paddingHorizontal: 4,
              paddingVertical: 3,
              borderRadius: 4,
              marginTop: -5
            }}

            // backgroundColor={discountLabelBgColor}
            textStyle={{ color: '#FF0F23' }}
          />
        }

      </View>
    </View>
  );
};

export default OneLineTwoBiz;
