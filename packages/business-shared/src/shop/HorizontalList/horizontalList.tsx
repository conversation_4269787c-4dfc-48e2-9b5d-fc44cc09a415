import React from 'react';
import { View } from '../../../../core/src/components/basic/View';
import { ScrollView } from '../../../../core/src/components/layout/ScrollView';
import { CouponCard } from '../CouponCard';
import { ProductCard } from '../../product/ProductCard';
import { HorizontalListProps, ListItemType } from './types';
import './horizontalList.scss';

/**
 * 横向滚动列表组件 - Taro/Web 实现
 */
const HorizontalList: React.FC<HorizontalListProps> = ({
  items = [],
  showScrollbar = false,
  itemSpacing = 12,
  horizontalPadding = 16,
  itemWidth = 'auto',
  renderItem,
  emptyComponent,
  onEndReached,
  onScroll,
  onItemClick,
  onViewableItemsChanged,
  className = '',
  style,
  containerStyle,
}) => {
  // 合并样式
  const listStyle = {
    paddingLeft: horizontalPadding,
    paddingRight: horizontalPadding,
    ...style,
  };

  // 列表项样式
  const itemStyle = {
    width: typeof itemWidth === 'number' ? `${itemWidth}px` : itemWidth,
    marginRight: itemSpacing,
  };

  // 渲染列表项
  const renderListItem = (item: ListItemType, index: number) => {
    // 处理点击事件
    const handleItemClick = () => {
      if (onItemClick) {
        onItemClick(item, index);
      }
    };

    // 如果提供了自定义渲染函数，则使用它
    if (renderItem) {
      const customContent = renderItem(item, index);
      return (
        <View 
          key={`custom-${index}`} 
          className="horizontal-list__item" 
          style={itemStyle}
          onClick={onItemClick ? handleItemClick : undefined}
        >
          {customContent}
        </View>
      );
    }

    // 根据项目类型渲染不同组件
    if (item.type === 'coupon') {
      return (
        <View 
          key={`coupon-${index}`} 
          className="horizontal-list__item" 
          style={itemStyle}
          onClick={onItemClick ? handleItemClick : undefined}
        >
          <CouponCard {...item.data} />
        </View>
      );
    }

    if (item.type === 'product') {
      return (
        <View 
          key={`product-${index}`} 
          className="horizontal-list__item" 
          style={itemStyle}
          onClick={onItemClick ? handleItemClick : undefined}
        >
          <ProductCard {...item.data} />
        </View>
      );
    }

    return null;
  };

  // 处理滚动结束事件
  const handleScrollToEnd = (e: any) => {
    if (onEndReached) {
      const { scrollLeft, scrollWidth, clientWidth } = e.detail;
      // 检查是否滚动到末尾（考虑一些容差）
      if (scrollWidth - (scrollLeft + clientWidth) < 5) {
        onEndReached();
      }
    }
  };

  // 处理滚动事件
  const handleScroll = (e: any) => {
    if (onScroll) {
      onScroll(e);
    }
  };

  // 如果列表为空，显示空状态
  if (items.length === 0 && emptyComponent) {
    return (
      <View className={`horizontal-list ${className}`}>
        {emptyComponent}
      </View>
    );
  }

  return (
    <View className={`horizontal-list ${className}`}>
      <ScrollView
        className={`horizontal-list__scroll-view ${!showScrollbar ? 'horizontal-list__scroll-view--hide-scrollbar' : ''}`}
        style={listStyle}
        scrollX
        scrollWithAnimation
        onScroll={handleScroll}
        onScrollToLower={handleScrollToEnd}
      >
        <View className="horizontal-list__content">
          {items.map(renderListItem)}
          {/* 添加一个空的元素作为末尾间距，避免最后一个元素紧贴边缘 */}
          <View className="horizontal-list__spacer" style={{ width: horizontalPadding }} />
        </View>
      </ScrollView>
    </View>
  );
};

export default HorizontalList; 