import React from 'react';
import { View } from '@tarojs/components';
import { ShopImageTag } from './index';
import { CustomTag } from '../CustomTag';
import { TagType } from '../CustomTag/types';

const ShopImageTagDemo: React.FC = () => {
  return (
    <View style={{ padding: '20px' }}>
      <View style={{ marginBottom: '16px' }}>
        <h3>基础图片标签</h3>
        <ShopImageTag 
          src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
          size={100}
          tag={{ 
            tagComponent: <CustomTag 
              type={TagType.TEXT}
              text="热卖" 
              backgroundColor="#ff4d4f"
              textStyle={{ color: '#fff' }}
            />,
            position: 'topLeft'
          }}
        />
      </View>
      
      <View style={{ marginBottom: '16px' }}>
        <h3>多个标签位置示例</h3>
        <ShopImageTag 
          src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
          size={120}
          tag={[
            { 
              tagComponent: <CustomTag 
                type={TagType.TEXT}
                text="限时" 
                backgroundColor="#ff4d4f"
                textStyle={{ color: '#fff' }}
              />,
              position: 'topLeft'
            },
            { 
              tagComponent: <CustomTag 
                type={TagType.TEXT}
                text="新品" 
                backgroundColor="#52c41a"
                textStyle={{ color: '#fff' }}
              />,
              position: 'topRight'
            },
            { 
              tagComponent: <CustomTag 
                type={TagType.TEXT}
                text="优惠" 
                backgroundColor="#faad14"
                textStyle={{ color: '#fff' }}
              />,
              position: 'bottomLeft'
            },
            { 
              tagComponent: <CustomTag 
                type={TagType.TEXT}
                text="爆款" 
                backgroundColor="#1890ff"
                textStyle={{ color: '#fff' }}
              />,
              position: 'bottomRight'
            }
          ]}
        />
      </View>
      
      <View style={{ marginBottom: '16px' }}>
        <h3>图标标签示例</h3>
        <ShopImageTag 
          src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
          size={100}
          tag={{
            tagComponent: <CustomTag 
              type={TagType.ICON_PREFIX}
              text="官方" 
              prefixIcon="https://img12.360buyimg.com/imagetools/jfs/t1/180776/26/8417/1985/60c33124E7e26e4a7/05d5c0d0df6e8a6a.png"
              backgroundColor="#fff0f0"
              textStyle={{ color: '#e93b3d' }}
            />,
            position: 'bottom'
          }}
        />
      </View>
      
      <View style={{ marginBottom: '16px' }}>
        <h3>更多标签类型示例</h3>
        <View style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-around' }}>
          <ShopImageTag 
            src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
            size={80}
            tag={{
              tagComponent: <CustomTag 
                type={TagType.IMAGE}
                tagImage="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
              />,
              position: 'topLeft'
            }}
          />
          
          <ShopImageTag 
            src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
            size={80}
            tag={{
              tagComponent: <CustomTag 
                type={TagType.ICON_SUFFIX}
                text="超值" 
                suffixIcon="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
                backgroundColor="#e6f7ff"
                textStyle={{ color: '#1890ff' }}
              />,
              position: 'topRight'
            }}
          />
        </View>
      </View>
      
    </View>
  );
};

export default ShopImageTagDemo;
