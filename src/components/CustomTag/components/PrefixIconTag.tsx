import React from 'react';
import View from '../../basic/View';
import Text from '../../basic/Text';
import Image from '../../basic/Image';
import { CSSProperties } from 'react';
import { TagType } from '../types';

/**
 * 前置图标标签属性接口
 */
export interface PrefixIconTagProps {
  /** 图标URL */
  iconSrc: string;
  /** 图标样式 */
  iconStyle?: CSSProperties;
  /** 标签内容类型 */
  contentType: 'text' | 'image';
  /** 文本内容 (contentType为text时必填) */
  text?: string;
  /** 文本样式 */
  textStyle?: CSSProperties;
  /** 图片URL (contentType为image时必填) */
  tagImage?: string;
  /** 图片样式 */
  tagImageStyle?: CSSProperties;
  /** 标签类名 */
  className?: string;
  /** 标签样式 */
  style?: CSSProperties;
}

/**
 * 前置图标标签组件
 * @param props 组件属性
 */
const PrefixIconTag: React.FC<PrefixIconTagProps> = ({
  iconSrc,
  iconStyle,
  contentType,
  text = '',
  textStyle,
  tagImage,
  tagImageStyle,
  className,
  style,
  ...props
}) => {
  // 使用函数式编程构建类名
  const tagClassName = [
    'custom-tag',
    `custom-tag--${TagType.ICON_PREFIX}`,
    className
  ].filter(Boolean).join(' ');

  // 使用函数式条件渲染内容
  const renderContent = () => {
    if (contentType === 'image' && tagImage) {
      return (
        <Image
          className="custom-tag__image"
          style={tagImageStyle}
          src={tagImage}
          mode="aspectFit"
        />
      );
    }
    
    return (
      <Text
        className="custom-tag__text"
        style={textStyle}
      >
        {text}
      </Text>
    );
  };

  return (
    <View
      className={tagClassName}
      style={style}
      {...props}
    >
      <Image
        className="custom-tag__icon custom-tag__icon--prefix"
        style={iconStyle}
        src={iconSrc}
        mode="aspectFit"
      />
      {renderContent()}
    </View>
  );
};

export default PrefixIconTag;
