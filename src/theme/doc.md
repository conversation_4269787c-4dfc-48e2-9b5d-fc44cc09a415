# 主题接入与开发说明

## 1. 主题能力简介

本主题系统基于 React Context 实现，支持全局主题变量（如主色、背景色、文字色等），支持明暗模式切换，支持动态切换主题，适配 Web/H5、RN、小程序等多端。

- 支持全局主题色、背景色、文字色等变量
- 支持明暗模式（light/dark）
- 支持动态切换主题
- 支持自定义扩展主题变量
- 组件可通过 `useTheme` 响应式获取主题

---

## 2. 快速接入

### 2.1 在入口文件包裹 ThemeProvider

**推荐在 `app.tsx` 或 `app.rn.tsx` 最外层包裹 ThemeProvider：**

```tsx
import ThemeProvider from '@/uicomponents/src/theme/ThemeProvider';

function App(props) {
  // ...
  return (
    <ThemeProvider mode="light"> {/* mode 可为 'light' | 'dark'，可动态切换 */}
      {props.children}
    </ThemeProvider>
  );
}
```

### 2.2 组件内获取主题变量

```tsx
import { useTheme } from '@/uicomponents/src/theme/ThemeProvider';

const MyComponent = () => {
  const { theme, mode, setMode } = useTheme();
  return <View style={{ backgroundColor: theme.colors.primary }}>Hello</View>;
};
```

---

## 3. 动态切换主题

你可以通过 `setMode('dark')` 或 `setMode('light')` 动态切换主题：

```tsx
const { setMode } = useTheme();
<Button onClick={() => setMode('dark')}>切换暗黑</Button>
```

也可以在外部通过 props 控制：

```tsx
const [themeMode, setThemeMode] = useState<'light' | 'dark'>('light');
<ThemeProvider mode={themeMode}>
  ...
</ThemeProvider>
```

---

## 4. 自动跟随系统主题（Web/H5）

在入口文件监听系统主题变化，自动切换：

```tsx
const [themeMode, setThemeMode] = useState<'light' | 'dark'>('light');
useEffect(() => {
  if (typeof window !== 'undefined' && window.matchMedia) {
    const mql = window.matchMedia('(prefers-color-scheme: dark)');
    setThemeMode(mql.matches ? 'dark' : 'light');
    const handler = (e) => setThemeMode(e.matches ? 'dark' : 'light');
    mql.addEventListener('change', handler);
    return () => mql.removeEventListener('change', handler);
  }
}, []);
<ThemeProvider mode={themeMode}>...</ThemeProvider>
```

---

## 5. 扩展主题变量

编辑 `src/uicomponents/src/theme/theme.ts`，可自定义更多主题变量：

```ts
export const defaultTheme = {
  light: {
    colors: {
      primary: '#1677ff',
      background: '#ffffff',
      text: '#222222',
      // 新增变量
      border: '#e5e5e5',
    },
    // ...
  },
  dark: {
    colors: {
      primary: '#177ddc',
      background: '#181818',
      text: '#eeeeee',
      border: '#333333',
    },
    // ...
  },
};
```

组件内即可通过 `theme.colors.border` 获取。

---

## 6. 组件开发最佳实践

- 组件内通过 `useTheme` 获取主题变量，样式响应式绑定 theme
- 不要硬编码颜色，全部用 theme 变量
- 支持 theme 变量的动态变化（如 mode 切换）
- 主题变量命名建议统一（如 primary、background、text、border 等）
- 主题 Provider 只包裹一次，放在应用最外层

---

## 7. 注意事项

- ThemeProvider/useTheme 的 import 路径必须统一，不能有多份 context
- 只装一份 React，避免多份 context 问题
- RN/小程序端如需适配系统主题，需用各自 API 监听并 setMode

---

## 8. 参考
- 主题类型定义：`src/uicomponents/src/theme/types.ts`
- 主题变量定义：`src/uicomponents/src/theme/theme.ts`
- Provider实现：`src/uicomponents/src/theme/ThemeProvider/index.tsx` 