import React from 'react';
import { View, Text, ScrollView, StyleSheet, Platform } from 'react-native';
import { Title } from './index';
import { CustomTagProps } from '../../CustomTag/types';

// 示例标签数据
const tags: CustomTagProps[] = [
  {
    text: '新品',
    backgroundColor: '#ff4d4f',
    textStyle: { color: '#fff' }
  },
  {
    text: '热销',
    backgroundColor: '#1890ff',
    textStyle: { color: '#fff' }
  },
];

const promotionTags: CustomTagProps[] = [
  {
    text: '促销',
    backgroundColor: '#ff6b00',
    textStyle: { color: '#fff' }
  },
  {
    text: '限时',
    backgroundColor: '#52c41a',
    textStyle: { color: '#fff' }
  },
];

export default function TitleDemo() {
  const HEADER_HEIGHT = 56;
  return (
    <View style={styles.container}>
      {/* 顶部标题栏，绝对定位 */}
      <View style={[styles.header, { height: HEADER_HEIGHT }]}> 
        <Text style={[styles.headerTitle, { lineHeight: HEADER_HEIGHT }]}>Title</Text>
      </View>
      <ScrollView
        style={[styles.scrollView, { marginTop: HEADER_HEIGHT }]}
        contentContainerStyle={styles.content}
      >
        {/* 基础标题展示 */}
        <Text style={styles.sectionTitle}>基础标题展示</Text>
        <View style={styles.card}>
          <Title fontSize={24} style={{ color: '#1890ff' }}>一级标题</Title>
          <Title fontSize={20} style={{ color: '#333333', marginTop: 12 }}>二级标题</Title>
          <Title fontSize={18} style={{ color: '#52c41a', marginTop: 12 }}>三级标题</Title>
          <Title fontSize={16} style={{ color: '#faad14', marginTop: 12 }}>四级标题</Title>
          <Title fontSize={14} style={{ color: '#f5222d', marginTop: 12 }}>五级标题</Title>
          <Title fontSize={12} style={{ marginTop: 12 }}>六级标题</Title>
        </View>
        
        {/* 对齐方式 */}
        <Text style={styles.sectionTitle}>对齐方式</Text>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>不同文本对齐方式</Text>
          <Title fontSize={20} textAlign="left" style={{ color: '#666666' }}>左对齐标题</Title>
          <Title fontSize={20} textAlign="center" style={{ color: '#666666', marginTop: 12 }}>居中对齐标题</Title>
          <Title fontSize={20} textAlign="right" style={{ color: '#666666', marginTop: 12 }}>右对齐标题</Title>
        </View>
        
        {/* 带标签的标题 */}
        <Text style={styles.sectionTitle}>带标签的标题</Text>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>标准标签</Text>
          <Title 
            fontSize={30}
            tags={tags}
          >
            新品热销商品标题新品热销商品标题新品热销商品标题新品热销商品标题
          </Title>
        </View>
        
        <View style={styles.card}>
          <Text style={styles.cardTitle}>促销标签</Text>
          <Title 
            fontSize={20}
            tags={promotionTags}
            tagGap={12}
            tagTitleGap={16}
          >
            促销活动标题
          </Title>
        </View>
        
        <View style={styles.card}>
          <Text style={styles.cardTitle}>多标签</Text>
          <Title 
            fontSize={18}
            tags={[
              ...tags,
              {
                text: '限量',
                backgroundColor: '#722ed1',
                textStyle: { color: '#fff' }
              }
            ]}
          >
            多标签标题
          </Title>
        </View>
        
        {/* 行数限制展示 */}
        <Text style={styles.sectionTitle}>行数限制展示</Text>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>单行省略</Text>
          <Title 
            fontSize={20}
            lines={1}
            style={{ width: '100%' }}
          >
            单行标题，超出部分会被截断显示省略号。这是一个很长很长很长很长很长很长很长很长很长很长很长很长的标题。
          </Title>
        </View>
        
        <View style={styles.card}>
          <Text style={styles.cardTitle}>两行省略</Text>
          <Title 
            fontSize={20}
            lines={2}
            ellipsis="ellipsis"
            style={{ width: '100%' }}
          >
            两行标题，超出部分会被截断显示省略号。这是一个很长很长很长很长很长很长很长很长很长很长很长很长的标题，为了测试多行省略效果，我们需要确保内容足够长。
          </Title>
        </View>
        
        <View style={styles.card}>
          <Text style={styles.cardTitle}>三行截断</Text>
          <Title 
            fontSize={20}
            lines={3}
            ellipsis="clip"
            style={{ width: '100%' }}
          >
            三行标题，超出部分会被直接截断不显示省略号。这是一个很长很长很长很长很长很长很长很长很长很长很长很长的标题，为了测试多行省略效果，我们需要确保内容足够长，让它能够显示三行以上。
          </Title>
        </View>
        
        {/* 自定义样式 */}
        <Text style={styles.sectionTitle}>自定义样式</Text>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>自定义颜色和加粗</Text>
          <Title 
            fontSize={24}
            style={{ 
              color: '#FF6B00',
              fontWeight: 'bold',
            }}
          >
            自定义样式标题
          </Title>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f8fa',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    paddingTop: Platform.OS === 'android' ? 0 : 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    marginBottom: 12,
    marginLeft: 4,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 14,
    color: '#999',
    marginBottom: 12,
  },
}); 