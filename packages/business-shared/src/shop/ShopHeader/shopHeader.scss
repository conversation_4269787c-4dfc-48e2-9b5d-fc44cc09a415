.shop-header {
  flex-direction: row;
  display: flex;
  
  &__image-container {
    margin-right: 12px;
    flex-shrink: 0;
  }
  
  &__image {
    width: 100%;
    height: 100%;
    border-radius: 8px;
    object-fit: cover;
  }
  
  &__content {
    flex: 1;
    flex-direction: column;
    overflow: hidden;
    display: flex;
  }
  
  &__title-row,
  &__info-row {
    display: flex;
    width: 100%;
    flex-direction: row;
    // min-height: 22px;
  }
  
  &__title-row {
    // margin-bottom: 4px;
  }
  
  // &__slot-left,
  // &__slot-right {
  //   display: flex;
  //   align-items: center;
  // }
  &__slot-left {
    flex: 1;
    flex-direction: row;
    align-items: center;
  }
  
  &__slot-right {
    flex-shrink: 0;
    flex-direction: row;
    align-items: center;
  }
  
  // 新增的样式用于嵌套的内容项
  &__item {
    // flex: 1;
    flex-direction: row;
    // align-items: center;
    margin-right: 4px;
    flex-shrink: 0;
  }
  
  // 为标题内容添加专用样式
  &__titleitem {
    flex-direction: row;
    // align-items: center;
    margin-right: 4px;
    flex-shrink: 0;
    padding-right: 4px;
  }
  
  // // 确保Title组件在左侧有足够的最小宽度
  // :global(.title-component) {
  //   min-width: 260px;
  //   flex-shrink: 0;
  // }
} 