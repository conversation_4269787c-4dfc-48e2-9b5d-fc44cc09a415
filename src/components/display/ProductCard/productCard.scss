/* 使用BEM命名规范 */
.product-card {
  /* 卡片容器样式 */
  display: flex;
  flex-direction: column;
  width: auto;
  // height: 110px;

  &__image-container {
    /* 图片容器，保持宽高比 */
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 6px;
  }

  &__content {
    /* 内容区域样式 */
    display: flex;
    flex-direction: column;
  }

  &__shop-name-container {
    /* 门店名称容器样式 */
    margin-top: 3px;
    color: #CA1010;
    font-size: 11px;
    // background-color: yellowgreen;
  }

  &__product-name-container {
    /* 商品名称容器样式 */
    color: #505259;
    margin-top: -2px;
  }



  &__price-container {
    /* 价格容器样式 */
    display: flex;
    align-items: center;
  }
}