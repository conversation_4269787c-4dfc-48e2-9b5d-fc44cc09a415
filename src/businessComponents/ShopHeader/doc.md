# ShopHeader 店铺头部信息组件

用于展示店铺基本信息的头部组件，支持自定义布局、多行信息展示，适用于电商、外卖等场景的店铺卡片。

## 组件特性

- 支持左侧店铺图片展示
- 支持多行信息内容配置
- 支持标题和标签两种内容类型
- 支持行高和行间距自定义
- 支持左右两侧内容灵活布局
- 跨平台兼容（Taro/Web、React Native）

## 使用方法

```tsx
import { ShopHeader } from '@uicomponent/businessComponents';
import { ContentType } from '@uicomponent/businessComponents/ShopHeader/types';

// 基础用法
<ShopHeader 
  imageUrl="https://img.example.com/shop/logo.png"
  lines={[
    {
      left: [
        {
          type: ContentType.TITLE,
          text: "品牌官方旗舰店",
          props: {
            fontSize: 18,
            style: { color: '#333' }
          }
        }
      ],
      right: [
        {
          type: ContentType.TAG,
          text: "官方认证",
          props: {
            backgroundColor: '#f0f9ff',
            textStyle: { color: '#1890ff' }
          }
        }
      ]
    },
    {
      left: [
        {
          type: ContentType.TAG,
          text: "优惠券",
          props: {
            showBorder: true,
            borderColor: '#e93b3d',
            textStyle: { color: '#e93b3d' }
          }
        }
      ]
    }
  ]}
/>

// 完整餐饮店铺示例
<ShopHeader 
  imageUrl="https://img.example.com/shop/restaurant.png"
  imageWidth={72}
  imageHeight={72}
  lines={[
    {
      left: [
        {
          type: ContentType.TITLE,
          text: "下酒 (林肯公园店)",
          props: {
            fontSize: 20,
            style: { color: '#333', fontWeight: 'bold' }
          }
        }
      ],
      right: [
        {
          type: ContentType.TAG,
          text: '已收藏',
          props: {
            backgroundColor: '#fff9f0',
            textStyle: { color: '#996633' }
          }
        }
      ]
    },
    {
      left: [
        {
          type: ContentType.TAG,
          text: '4.9分',  
          props: {
            backgroundColor: '#fff0f0',
            textStyle: { color: '#e93b3d', fontWeight: 'bold' }
          }
        },
        {
          type: ContentType.TAG,
          text: '2372条评价',
          props: {
            backgroundColor: '#f5f5f5',
            textStyle: { color: '#999' }
          }
        }
      ],
      right: [
        {
          type: ContentType.TAG,
          text: '营业至1:00',
          props: {
            backgroundColor: '#fff9f0',
            textStyle: { color: '#996633' }
          }
        }
      ]
    }
  ]}
  lineSpacing={6}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| imageUrl | 店铺logo/图片地址 | string | - |
| imageWidth | 图片宽度 | number | 72 |
| imageHeight | 图片高度 | number | 72 |
| lines | 信息行配置列表 | LineConfig[] | [] |
| lineSpacing | 通用行间距 | number | 7 |
| lineHeight | 通用行高 | number | - |
| className | 自定义根元素类名 (Taro/Web) | string | - |
| style | 自定义根元素内联样式 | React.CSSProperties \| ViewStyle | - |
| imageStyle | 自定义图片样式 | ImageStyle | - |
| onClick | 点击事件 | () => void | - |

### LineConfig 类型

```ts
interface LineConfig {
  /** 左侧内容数组 */
  left?: Content[];
  /** 右侧内容数组 */
  right?: Content[];
  /** 行高 */
  height?: number;
}
```

### Content 类型

组件支持两种内容类型：标题（TITLE）和标签（TAG），结构如下：

```ts
// 统一内容类型
type Content = TitleContent | TagContent | React.ReactNode;

// 基础内容接口
interface BaseContent {
  /** 内容类型 */
  type: ContentType;
  /** 文本内容 */
  text: string;
  /** 自定义类名 */
  className?: string;
}
```

#### TitleContent 类型
标题内容配置：

```ts
interface TitleContent extends BaseContent {
  type: ContentType.TITLE;
  /** 字体大小 */
  fontSize?: number;
  /** 文本对齐方式 */
  textAlign?: 'left' | 'center' | 'right';
  /** 行数限制 */
  lines?: number;
  /** 标签列表 */
  tags?: Omit<TagContent, 'type'>[]; // 支持在标题中嵌入标签
  /** 样式对象 */
  style?: {
    /** 文本颜色 */
    color?: string;
    /** 字体权重 */
    fontWeight?: string | number;
    /** 字体族 */
    fontFamily?: string;
    /** 左边距 */
    marginLeft?: number;
    /** 其他样式属性 */
    [key: string]: any;
  };
  /** 其他Title组件属性 */
  props?: { /* 其他标题组件属性 */ };
}
```

#### TagContent 类型
标签内容配置：

```ts
interface TagContent extends BaseContent {
  type: ContentType.TAG;
  /** 标签背景色 */
  backgroundColor?: string;
  /** 是否显示边框 */
  showBorder?: boolean;
  /** 边框颜色 */
  borderColor?: string;
  /** 图标URL */
  icon?: string;
  /** 标签图片URL */
  tagImage?: string;
  /** 文本样式 */
  textStyle?: {
    /** 文本颜色 */
    color?: string;
    /** 字体大小 */
    fontSize?: number;
    /** 字体权重 */
    fontWeight?: string | number;
    /** 字体族 */
    fontFamily?: string;
    /** 其他文本样式属性 */
    [key: string]: any;
  };
  /** 容器样式 */
  style?: {
    /** 左边距 */
    marginLeft?: number;
    /** 其他样式属性 */
    [key: string]: any;
  };
  /** 其他标签组件属性 */
  props?: { /* 其他标签组件属性 */ };
}
```

### 使用示例

以下是几种常见用法示例：

1. **基础标题和标签：**

```tsx
<ShopHeader 
  imageUrl="https://img.example.com/shop/logo.png"
  lines={[
    {
      left: [
        {
          type: ContentType.TITLE,
          text: "店铺名称",
          fontSize: 16,
          style: { color: '#333' }
        }
      ]
    }
  ]}
/>
```

2. **带样式的标签：**

```tsx
<ShopHeader 
  imageUrl="https://img.example.com/shop/logo.png"
  lines={[
    {
      right: [
        {
          type: ContentType.TAG,
          text: "优惠券",
          backgroundColor: "#f5f5f5",
          textStyle: { color: '#e93b3d' },
          showBorder: true,
          borderColor: "#e93b3d"
        }
      ]
    }
  ]}
/>
```

3. **带标签的标题：**

```tsx
<ShopHeader 
  imageUrl="https://img.example.com/shop/logo.png"
  lines={[
    {
      left: [
        {
          type: ContentType.TITLE,
          text: "品牌连锁店",
          tags: [
            {
              text: "品牌",
              backgroundColor: "#ff4d4f",
              textStyle: { color: "#fff" }
            }
          ]
        }
      ]
    }
  ]}
/>
```

## 平台差异

### Taro/Web 平台
- 使用 CSS 类名控制样式
- 使用 flex 布局实现左右内容布局
- 支持自定义 className 属性

### React Native 平台
- 使用 StyleSheet 控制样式
- 使用 flex 布局实现左右内容布局
- 使用 containerStyle 替代 className

## 布局说明

组件采用弹性布局：
- 左侧图片区域固定大小，不可压缩
- 右侧内容区域采用纵向排列的多行结构
- 每行内容支持左右两侧布局，左侧自适应宽度，右侧固定宽度

## 更新日志

### 1.0.0
- 首次发布
- 支持多行信息展示
- 支持标题和标签两种内容类型
- 支持Taro/RN跨平台 

## 新版使用示例

组件现在支持更简洁的内容配置方式：

```tsx
import React from 'react';
import { ShopHeader } from '@/uicomponents';
import { ContentType } from '@/uicomponents/src/businessComponents/ShopHeader/types';

export default () => {
  return (
    <ShopHeader 
      imageUrl="https://img.example.com/shop/logo.png"
      imageWidth={72}
      imageHeight={72}
      lines={[
        {
          left: [
            {
              type: ContentType.TITLE,
              text: "下酒 (林肯公园店)",
              fontSize: 20,
              style: {
                color: '#333',
                fontWeight: 'bold'
              }
            }
          ],
          right: [
            {
              type: ContentType.TAG,
              text: '已收藏',
              backgroundColor: '#fff9f0',
              textStyle: {
                color: '#996633'
              }
            }
          ]
        },
        {
          left: [
            {
              type: ContentType.TAG,
              text: '4.9分',
              backgroundColor: '#fff0f0',
              textStyle: {
                color: '#e93b3d',
                fontWeight: 'bold'
              }
            },
            {
              type: ContentType.TAG,
              text: '2372条评价',
              backgroundColor: '#f5f5f5',
              textStyle: {
                color: '#999'
              }
            }
          ],
          right: [
            {
              type: ContentType.TAG,
              text: '营业至1:00',
              backgroundColor: '#fff9f0',
              textStyle: {
                color: '#996633'
              }
            }
          ]
        }
      ]}
      lineSpacing={6}
    />
  );
};
```

### 新版内容类型

组件支持两种内容类型：标题（TITLE）和标签（TAG），更扁平化的结构方便使用：

```ts
// 标题内容
interface TitleContent {
  type: ContentType.TITLE;
  text: string;
  fontSize?: number;
  textAlign?: 'left' | 'center' | 'right';
  lines?: number;
  tags?: Omit<TagContent, 'type'>[]; // 支持在标题中嵌入标签
  style?: {
    color?: string;
    fontWeight?: string | number;
    fontFamily?: string;
    // 其他样式...
  };
  className?: string;
  props?: { /* 其他Title组件属性 */ };
}

// 标签内容
interface TagContent {
  type: ContentType.TAG;
  text: string;
  backgroundColor?: string;
  showBorder?: boolean;
  borderColor?: string;
  icon?: string;
  tagImage?: string;
  textStyle?: {
    color?: string;
    fontSize?: number;
    fontWeight?: string | number;
    fontFamily?: string;
    // 其他文本样式...
  };
  style?: {
    marginLeft?: number;
    // 其他容器样式...
  };
  className?: string;
  props?: { /* 其他标签组件属性 */ };
}
```

### 带标签的标题示例

标题组件可以包含多个标签，标签使用与独立标签相同的配置结构：

```tsx
import React from 'react';
import { ShopHeader } from '@/uicomponents';
import { ContentType } from '@/uicomponents/src/businessComponents/ShopHeader/types';

const TaggedTitleExample = () => (
  <ShopHeader 
    imageUrl="https://img.example.com/shop/logo.png"
    lines={[
      {
        left: [
          {
            type: ContentType.TITLE,
            text: "品牌连锁店",
            fontSize: 18,
            tags: [
              {
                text: "品牌",
                backgroundColor: "#ff4d4f",
                textStyle: { color: "#fff" }
              },
              {
                text: "连锁",
                backgroundColor: "#1890ff",
                textStyle: { color: "#fff" }
              }
            ],
            style: {
              color: '#333',
              fontWeight: 'bold'
            }
          }
        ]
      }
    ]}
  />
);
``` 