import React, { CSSProperties } from 'react';
import { TagProps } from '../../ShopImageTag/types';

export interface ProductCardProps {
  // 整体商卡参数
  className?: string;
  style?: CSSProperties;
  onClick?: () => void;

  // 价格间距参数
  discountCurrentPriceLeftOffset?: number;
  discountOriginalPriceRightOffset?: number;

  // 商品图片参数
  imageSrc: string;
  imageSize?: number;
  imageTag?: TagProps | TagProps[];

  // 门店名称参数
  shopName: string;
  shopNameFontSize?: number;
  shopNameLines?: number;
  shopNameEllipsis?: 'ellipsis' | 'fade';
  shopNameTag?: TagProps | TagProps[];
  shopNameStyle?: CSSProperties;

  // 商品名称参数
  productName: string;
  productNameFontSize?: number;
  productNameLines?: number;
  productNameEllipsis?: 'ellipsis' | 'fade';
  productNameTag?: TagProps | TagProps[];
  productNameStyle?: CSSProperties;

  // 商品价格参数
  currentPrice?: number;
  originalPrice?: number;
  priceType?: 'default' | 'discount';
  discountBackgroundImageSrc?: string;
  discountBackgroundImageWidth?: number;
  discountBackgroundImageHeight?: number;
  currentPriceColor?: string;
  originalPriceColor?: string;
  currentPriceFontSize?: number;
  originalPriceFontSize?: number;
  currencySymbol?: string;
  currentPriceSymbolFontSize?: number;
  originalPriceSymbolFontSize?: number;
  currentPriceDecimalFontSize?: number;
  originalPriceDecimalFontSize?: number;
  strikethrough?: boolean;

  // 价格折扣信息
  discountPrefixIcon: any;
  discountText?: string;
}
