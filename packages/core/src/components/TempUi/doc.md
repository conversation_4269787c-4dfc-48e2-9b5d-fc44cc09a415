# TempUI 组件

一个最基础的视图组件，用于快速创建新组件。

## 使用方式

```tsx
import { TempUI } from '@uicomponents/core';

// 基础用法
<TempUI>内容</TempUI>

// 自定义样式
<TempUI className="custom-class" style={{ color: 'red' }}>
  自定义样式
</TempUI>
```

## Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| className | 自定义类名 | string | - |
| style | 自定义样式 | CSSProperties | - |
| children | 子元素 | ReactNode | - |

## 平台差异

| 平台 | 实现方式 |
| --- | --- |
| Web | 使用 View 组件 |
| React Native | 使用 View 组件 |

## 注意事项

1. 这是一个最基础的组件，可以根据需要扩展
2. 组件使用 flex 布局，默认水平居中
3. 支持所有 View 组件的属性 