import React from 'react';
import { ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { ProductCardProps } from '../../product/ProductCard/types';

export interface MarketingFloorProps {
  /** 顶部左侧百亿补贴图片地址 */
  subsidyImageUrl?: string;
  /** 顶部左侧百亿补贴图片宽度 */
  subsidyImageWidth?: number;
  /** 顶部左侧百亿补贴图片高度 */
  subsidyImageHeight?: number;
  /** 背景图片 */
  panelBgImage?: string;
  /** 背景图片宽度 */
  panelBgImageWidth?: number;
  /** 背景图片高度 */
  panelBgImageHeight?: number;

  // 右侧更多
  rightIconColor?: string;



  /** 顶部右侧是否显示查看更多 */
  showMoreLink?: boolean;
  /** 顶部右侧查看更多文案 */
  moreText?: string;
  /** 顶部右侧查看更多点击事件 */
  onMoreClick?: () => void;
  /** 商品数据列表，将严格按照4个一组展示，不足4个倍数的部分将被舍弃 */
  products: ProductCardProps[];
  /** 自动轮播间隔(毫秒)，0表示不自动轮播 */
  autoplayInterval?: number;
  /** 顶部背景色 */
  headerBackgroundColor?: string;
  /** 顶部文字颜色 */
  headerTextColor?: string;
  /** 顶部文字大小 */
  headerFontSize?: number;
  /** 底部背景色 */
  contentBackgroundColor?: string;
  /** 轮播切换回调函数 */
  onCarouselChange?: (index: number) => void;
  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
  /** 自定义根元素内联样式 (Taro/Web) */
  style?: React.CSSProperties;
  /** 自定义根元素样式 (RN) */
  containerStyle?: ViewStyle;
  /** 商品卡片点击事件 */
  onProductClick?: (product: ProductCardProps, index: number) => void;
} 