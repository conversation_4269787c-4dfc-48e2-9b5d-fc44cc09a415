.demo-container {
  position: relative;
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background-color: #f7f8fa;
}

.demo-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 96px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.demo-header-title {
  font-size: 36px;
  font-weight: bold;
  text-align: center;
}

.demo-scroll-view {
  flex: 1;
  padding-top: 96px;
}

.demo-content {
  padding: 32px;
  padding-bottom: 80px;
}

.demo-section-title {
  font-size: 32px;
  color: #666;
  margin-top: 32px;
  margin-bottom: 24px;
  margin-left: 8px;
}

.demo-card {
  background-color: #fff;
  border-radius: 24px;
  padding: 40px;
  margin-bottom: 32px;
}

.demo-card-title {
  font-size: 28px;
  color: #999;
  margin-bottom: 24px;
} 