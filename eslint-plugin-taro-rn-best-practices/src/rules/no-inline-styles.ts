import { ESLintUtils } from '@typescript-eslint/utils';

const createRule = ESLintUtils.RuleCreator(
  (name) => `https://docs.taro.zone/docs/react-native-remind#${name}`
);

export default createRule({
  name: 'no-inline-styles',
  meta: {
    type: 'suggestion',
    docs: {
      description: 'Disallow inline styles, encourage using StyleSheet.',
      recommended: 'warn',
    },
    messages: {
      noInlineStyles: 'Inline styles are discouraged. Use StyleSheet.create() for better performance.',
    },
    schema: [],
    fixable: 'code',
  },
  defaultOptions: [],
  create(context) {
    return {
      JSXAttribute(node) {
        // Only check style attributes
        if (node.name.name !== 'style') return;
        
        // Check if the style is an inline object
        if (
          node.value &&
          node.value.type === 'JSXExpressionContainer' &&
          node.value.expression.type === 'ObjectExpression'
        ) {
          context.report({
            node,
            messageId: 'noInlineStyles',
          });
        }
      },
    };
  },
});