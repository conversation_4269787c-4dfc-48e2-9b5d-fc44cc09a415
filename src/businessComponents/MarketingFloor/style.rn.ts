import { StyleSheet } from 'react-native';

const styles = StyleSheet.create({
  container: {
    borderRadius: 12,
    overflow: 'hidden',
    height: 172,
    position: 'relative',
    marginLeft: 8,
    marginRight: 8,
  },
  header: {
    // 头部已移至Header组件，这里只保留引用
  },
  content: {
    // padding: 12,
    paddingHorizontal: 9,
    position: 'relative',
    marginTop: 9,
  },
  carousel: {
    // height: 260,
    position: 'relative',
    width: '100%',
  },
  carouselItem: {
    position: 'absolute',
    left: 0,
    top: 0,
    width: '100%',
  },
  products: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginHorizontal: -8,
  },
  productItem: {
    marginBottom: 16,
  },
  // 翻转轮播样式
  flipCarousel: {
    width: '100%',
    height: 'auto',
  },
  flipCarouselContent: {
    width: '100%',
    position: 'relative',
  },
  pageContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    // paddingHorizontal: 9,
  }
});

export default styles; 