import { ESLintUtils } from '@typescript-eslint/utils';

const createRule = ESLintUtils.RuleCreator(
  (name) => `https://docs.taro.zone/docs/react-native-remind#${name}`
);

export default createRule({
  name: 'require-image-size',
  meta: {
    type: 'problem',
    docs: {
      description: 'Require Image components to have explicit width and height styles.',
      recommended: 'error',
    },
    messages: {
      requireImageSize: 'Image components must have width and height specified in style prop',
    },
    schema: [],
  },
  defaultOptions: [],
  create(context) {
    return {
      JSXElement(node) {
        // Only check Image components
        if (
          node.openingElement.name.type !== 'JSXIdentifier' ||
          node.openingElement.name.name !== 'Image'
        ) {
          return;
        }

        // Find style attribute
        const styleAttr = node.openingElement.attributes.find(
          (attr):
            attr is import('@typescript-eslint/types/dist/generated/ast-spec').JSXAttribute &
            { name: import('@typescript-eslint/types/dist/generated/ast-spec').JSXIdentifier } =>
            attr.type === 'JSXAttribute' &&
            attr.name.type === 'JSXIdentifier' && // Ensure attr.name is JSXIdentifier
            attr.name.name === 'style'
        );

        // If style attribute is not found, or it has no value, or its value is not a JSXExpressionContainer
        if (!styleAttr || !styleAttr.value || styleAttr.value.type !== 'JSXExpressionContainer') {
          context.report({
            node,
            messageId: 'requireImageSize',
          });
          return;
        }

        // Check if style has width and height
        let hasWidth = false;
        let hasHeight = false;

        const expression = styleAttr.value.expression;

        // Handle object expression style
        if (expression.type === 'ObjectExpression') {
          for (const prop of expression.properties) {
            if (prop.type === 'Property' && prop.key.type === 'Identifier') {
              if (prop.key.name === 'width') hasWidth = true;
              if (prop.key.name === 'height') hasHeight = true;
            }
          }
        }
        // Handle style reference (e.g., style={styles.container})
        else if (expression.type === 'Identifier' || expression.type === 'MemberExpression') {
          // We can't easily check referenced styles statically, so we'll assume it's correct for now
          // or a more sophisticated analysis (type-aware) would be needed.
          return;
        } else {
          // Other types of style assignments (e.g. style={getStyles()}) are not checked here
          // or might be an error depending on desired strictness.
          context.report({
            node,
            messageId: 'requireImageSize',
          });
          return;
        }

        if (!hasWidth || !hasHeight) {
          context.report({
            node,
            messageId: 'requireImageSize',
          });
        }
      },
    };
  },
});