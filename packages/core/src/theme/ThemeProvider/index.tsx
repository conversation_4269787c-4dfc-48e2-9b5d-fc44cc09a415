import React, { createContext, useContext, useState, useMemo, useEffect } from 'react';
import { defaultTheme } from '../theme';
import type { ThemeMode, Theme, ThemeContextType } from '../types';

const ThemeContext = createContext<ThemeContextType | null>(null);

interface ThemeProviderProps {
  mode?: ThemeMode;
  children: React.ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ mode = 'light', children }) => {
  const [themeMode, setThemeMode] = useState<ThemeMode>(mode);

  // 外部mode变化时自动同步内部themeMode
  useEffect(() => {
    setThemeMode(mode);
  }, [mode]);

  const theme = useMemo<Theme>(() => defaultTheme[themeMode], [themeMode]);
  console.log('theme111', theme)
  const contextValue = useMemo<ThemeContextType>(
    () => ({ theme, mode: themeMode, setMode: setThemeMode }),
    [theme, themeMode]
  );

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) throw new Error('useTheme must be used within a ThemeProvider');
  return context;
};

export default ThemeProvider; 