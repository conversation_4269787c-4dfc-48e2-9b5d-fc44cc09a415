import React from 'react';
import { ViewStyle } from 'react-native'; // TextStyle might not be needed at this level if BasePrice handles it

export type PriceType = 'default' | 'discount';

export interface PriceProps {
  /** 当前价格 */
  currentPrice: number;
  /** 原价，如果提供则显示原价 */
  originalPrice?: number;
  /** 价格展示样式类型 */
  type?: PriceType;
  /** 货币符号 */
  currencySymbol?: string;
  /** 小数位数 */
  decimalPlaces?: 0 | 1 | 2;
  /** 是否给原价添加删除线 (主要影响 default 类型下的 originalPrice，discount 类型下通常默认有) */
  strikethrough?: boolean;
  /** 指定标签宽度 */
  priceTagWidth?: number;
  
  /** 当前价格数字的颜色 */
  currentPriceColor?: string;
  /** 当前价格的字体大小 */
  currentPriceFontSize?: number;
  /** 当前价格货币符号的字体大小 */
  currentPriceSymbolFontSize?: number;
  /** 当前价格小数部分的字体大小 */
  currentPriceDecimalFontSize?: number;
  
  /** 原价数字的颜色 */
  originalPriceColor?: string;
  /** 原价的字体大小 */
  originalPriceFontSize?: number;
  /** 原价货币符号的字体大小 */
  originalPriceSymbolFontSize?: number;
  /** 原价小数部分的字体大小 */
  originalPriceDecimalFontSize?: number;
  
  /** [Discount Type Only] 背景图片 URL */
  discountBackgroundImageSrc?: string;
  /** [Discount Type Only] 背景图片宽度 */
  discountBackgroundImageWidth?: number;
  /** [Discount Type Only] 背景图片高度 */
  discountBackgroundImageHeight?: number;
  /** [Discount Type Only] 当前价格（左侧价格）距离背景左边的间距 */
  discountCurrentPriceLeftOffset?: number;
  /** [Discount Type Only] 原价（右侧价格）距离背景右边的间距 */
  discountOriginalPriceRightOffset?: number;

  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
  /** 自定义根元素内联样式 (Taro/Web) */
  style?: React.CSSProperties;
  /** 自定义根元素样式 (RN) */
  containerStyle?: ViewStyle;
} 