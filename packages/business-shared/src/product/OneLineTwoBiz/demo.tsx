import React from 'react';
import { View } from '../../../../core/src/components/basic/View';
import OneLineTwoBiz from './oneLineTwoBiz';

/**
 * OneLineTwoBiz组件示例
 */
const Demo: React.FC = () => {
  return (
    <View style={{ padding: 16, backgroundColor: '#f5f5f5' }}>
      <View style={{ marginBottom: 16 }}>
        <OneLineTwoBiz
          imageSrc="https://img10.360buyimg.com/imagetools/jfs/t1/203818/18/35983/134596/65e47fe8F58e88b44/0917b04b2bd0d6db.jpg"
          distance="3.5km"
          productName="壹品白鹅 品牌纯色+跳"
          currentPrice={48}
          originalPrice={98}
          monthlySales="200+"
          discountLabel="3.5折热销中"
          onClick={() => { console.log('点击了美甲卡片'); }}
        />
      </View>

      <View style={{ marginBottom: 16 }}>
        <OneLineTwoBiz
          imageSrc="https://img10.360buyimg.com/imagetools/jfs/t1/213288/14/35655/115563/65e47fe8Fc1f05e10/a8cc9e8c6235b9a9.jpg"
          distance="1.2km"
          productName="法式光疗美甲 清新春夏款"
          currentPrice={88}
          originalPrice={168}
          monthlySales="150+"
          discountLabel="5.2折特惠"
          onClick={() => { console.log('点击了美甲卡片'); }}
        />
      </View>

      <View>
        <OneLineTwoBiz
          imageSrc="https://img10.360buyimg.com/imagetools/jfs/t1/223166/40/35425/89653/65e47fe8F40ae5c0b/9e95bcb55142aeb6.jpg"
          distance="0.8km"
          productName="时尚水钻美甲 日系风格"
          currentPrice={128}
          originalPrice={198}
          monthlySales="80+"
          onClick={() => { console.log('点击了美甲卡片'); }}
        />
      </View>
    </View>
  );
};

export default Demo;
