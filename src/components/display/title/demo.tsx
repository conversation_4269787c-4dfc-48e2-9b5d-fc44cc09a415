import React from 'react';
import { View, Text, ScrollView } from '@tarojs/components';
import { Title } from './index';
import { CustomTagProps } from '../../CustomTag/types';
import './demo.scss';

// 示例标签数据
const tags: CustomTagProps[] = [
  {
    text: '新品',
    backgroundColor: '#ff4d4f',
    textStyle: { color: '#fff' }
  },
  {
    text: '热销',
    backgroundColor: '#1890ff',
    textStyle: { color: '#fff' }
  },
];

const promotionTags: CustomTagProps[] = [
  {
    text: '促销',
    backgroundColor: '#ff6b00',
    textStyle: { color: '#fff' }
  },
  {
    text: '限时',
    backgroundColor: '#52c41a',
    textStyle: { color: '#fff' }
  },
];

export default function TitleDemo() {
  return (
    <View className="demo-container">
      {/* 顶部标题栏 */}
      <View className="demo-header">
        <Text className="demo-header-title">Title</Text>
      </View>
      <ScrollView
        className="demo-scroll-view"
        scrollY
      >
        <View className="demo-content">
          {/* 基础标题展示 */}
          <Text className="demo-section-title">基础标题展示</Text>
          <View className="demo-card">
            <Title fontSize={24} style={{ color: '#1890ff' }}>一级标题</Title>
            <Title fontSize={20} style={{ color: '#333333', marginTop: '12px' }}>二级标题</Title>
            <Title fontSize={18} style={{ color: '#52c41a', marginTop: '12px' }}>三级标题</Title>
            <Title fontSize={16} style={{ color: '#faad14', marginTop: '12px' }}>四级标题</Title>
            <Title fontSize={14} style={{ color: '#f5222d', marginTop: '12px' }}>五级标题</Title>
            <Title fontSize={12} style={{ marginTop: '12px' }}>六级标题</Title>
          </View>
          
          {/* 对齐方式 */}
          <Text className="demo-section-title">对齐方式</Text>
          <View className="demo-card">
            <Text className="demo-card-title">不同文本对齐方式</Text>
            <Title fontSize={20} textAlign="left" style={{ color: '#666666' }}>左对齐标题</Title>
            <Title fontSize={20} textAlign="center" style={{ color: '#666666', marginTop: '12px' }}>居中对齐标题</Title>
            <Title fontSize={20} textAlign="right" style={{ color: '#666666', marginTop: '12px' }}>右对齐标题</Title>
          </View>
          
          {/* 带标签的标题 */}
          <Text className="demo-section-title">带标签的标题</Text>
          <View className="demo-card">
            <Text className="demo-card-title">标准标签</Text>
            <Title 
              fontSize={24}
              tags={tags}
            >
              新品热销商品标题
            </Title>
          </View>
          
          <View className="demo-card">
            <Text className="demo-card-title">促销标签</Text>
            <Title 
              fontSize={20}
              tags={promotionTags}
              tagGap={12}
              tagTitleGap={16}
            >
              促销活动标题
            </Title>
          </View>
          
          <View className="demo-card">
            <Text className="demo-card-title">多标签</Text>
            <Title 
              fontSize={18}
              tags={[
                ...tags,
                {
                  text: '限量',
                  backgroundColor: '#722ed1',
                  textStyle: { color: '#fff' }
                }
              ]}
              wrap
              tagContainerWidth={500}
            >
              多标签换行展示的标题
            </Title>
          </View>
          
          {/* 行数限制展示 */}
          <Text className="demo-section-title">行数限制展示</Text>
          <View className="demo-card">
            <Text className="demo-card-title">单行省略</Text>
            <Title 
              fontSize={20}
              lines={1}
              style={{ width: '100%' }}
            >
              单行标题，超出部分会被截断显示省略号。这是一个很长很长很长很长很长很长很长很长很长很长很长很长的标题。
            </Title>
          </View>
          
          <View className="demo-card">
            <Text className="demo-card-title">两行省略</Text>
            <Title 
              fontSize={20}
              lines={2}
              ellipsis="ellipsis"
              style={{ width: '100%' }}
            >
              两行标题，超出部分会被截断显示省略号。这是一个很长很长很长很长很长很长很长很长很长很长很长很长的标题，为了测试多行省略效果，我们需要确保内容足够长。
            </Title>
          </View>
          
          <View className="demo-card">
            <Text className="demo-card-title">三行截断</Text>
            <Title 
              fontSize={20}
              lines={3}
              ellipsis="clip"
              style={{ width: '100%' }}
            >
              三行标题，超出部分会被直接截断不显示省略号。这是一个很长很长很长很长很长很长很长很长很长很长很长很长的标题，为了测试多行省略效果，我们需要确保内容足够长，让它能够显示三行以上。
            </Title>
          </View>
          
          <View className="demo-card">
            <Text className="demo-card-title">渐变隐藏</Text>
            <Title 
              fontSize={20}
              lines={2}
              ellipsis="fade"
              style={{ width: '100%' }}
            >
              两行标题，超出部分会使用渐变效果隐藏。这是一个很长很长很长很长很长很长很长很长很长很长很长很长的标题，为了测试渐变效果，我们需要确保内容足够长。
            </Title>
          </View>
          
          {/* 自定义样式 */}
          <Text className="demo-section-title">自定义样式</Text>
          <View className="demo-card">
            <Text className="demo-card-title">自定义样式</Text>
            <Title 
              fontSize={24}
              style={{ 
                color: '#FF6B00',
                fontWeight: 'bold',
                textShadow: '0 2px 4px rgba(255, 107, 0, 0.2)'
              }}
            >
              自定义样式标题
            </Title>
          </View>
          
          <View className="demo-card">
            <Text className="demo-card-title">渐变文字</Text>
            <Title 
              fontSize={20}
              style={{ 
                background: 'linear-gradient(90deg, #FF6B00 0%, #FF9500 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent'
              }}
            >
              渐变文字标题
            </Title>
          </View>
        </View>
      </ScrollView>
    </View>
  );
} 