import React from 'react';
import { View } from '@tarojs/components';
import CustomTag from './customTag';
import { TagType } from './types';

const CustomTagDemo: React.FC = () => {
  return (
    <View style={{ padding: '20px' }}>
      <View style={{ marginBottom: '16px' }}>
        <h3>纯文本标签 (TEXT)</h3>
        <CustomTag 
          type={TagType.TEXT}
          text="基础标签" 
        />
      </View>
      
      <View style={{ marginBottom: '16px' }}>
        <h3>自定义颜色文本标签</h3>
        <CustomTag 
          type={TagType.TEXT}
          text="促销标签" 
          backgroundColor="#ff4d4f"
          textStyle={{ color: '#fff' }}
        />
      </View>
      
      <View style={{ marginBottom: '16px' }}>
        <h3>带边框文本标签</h3>
        <CustomTag 
          type={TagType.TEXT}
          text="会员专享" 
          showBorder
          borderColor="#faad14"
          textStyle={{ color: '#faad14' }}
        />
      </View>

      <View style={{ marginBottom: '16px' }}>
        <h3>纯图片标签 (IMAGE)</h3>
        <CustomTag 
          type={TagType.IMAGE}
          tagImage="https://img12.360buyimg.com/imagetools/jfs/t1/196920/7/13405/5323/60f66654E7eebb47c/33f5f1b29f39cf2d.png"
        />
      </View>

      <View style={{ marginBottom: '16px' }}>
        <h3>前置图标标签 (ICON_PREFIX)</h3>
        <CustomTag 
          type={TagType.ICON_PREFIX}
          text="新品" 
          prefixIcon="https://img12.360buyimg.com/imagetools/jfs/t1/196920/7/13405/5323/60f66654E7eebb47c/33f5f1b29f39cf2d.png"
          backgroundColor="#f0f9ff"
          textStyle={{ color: '#1890ff' }}
        />
      </View>

      <View style={{ marginBottom: '16px' }}>
        <h3>后置图标标签 (ICON_SUFFIX)</h3>
        <CustomTag 
          type={TagType.ICON_SUFFIX}
          text="限时促销" 
          suffixIcon="https://img12.360buyimg.com/imagetools/jfs/t1/196743/13/13312/6193/60f6640cE5bcf3fd2/0d3af0bf6e062eba.png"
          showBorder
          backgroundColor="#fff0f0"
          textStyle={{ color: '#e93b3d' }}
        />
      </View>

      <View style={{ marginBottom: '16px' }}>
        <h3>前置图标+图片标签</h3>
        <CustomTag 
          type={TagType.ICON_PREFIX}
          prefixIcon="https://img12.360buyimg.com/imagetools/jfs/t1/196920/7/13405/5323/60f66654E7eebb47c/33f5f1b29f39cf2d.png"
          tagImage="https://img12.360buyimg.com/imagetools/jfs/t1/196743/13/13312/6193/60f6640cE5bcf3fd2/0d3af0bf6e062eba.png"
          backgroundColor="#f6ffed"
        />
      </View>
    </View>
  );
};

export default CustomTagDemo;
