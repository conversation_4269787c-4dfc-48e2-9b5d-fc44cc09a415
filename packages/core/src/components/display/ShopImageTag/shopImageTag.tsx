import React, { memo } from 'react';
import { pipe, mergeRight, filter, join } from 'ramda';
import { View } from '../../basic/View';
import { Image } from '../../basic/Image';
import { CustomTag } from '../CustomTag';
import { TagType } from '../CustomTag/types';
import { TaroImageTagProps, TagProps } from './types';
import { processTags, validatePosition } from './core';
import './shopImageTag.scss';

/**
 * 商品图片标签组件
 * 用于在商品图片上添加各种位置的标签，支持文本和图片标签
 * 采用函数式编程实现，使用 Ramda 库进行数据处理
 */
const ShopImageTag: React.FC<TaroImageTagProps> = ({
  src,
  size = 64,
  style = {},
  imageStyle = {},
  tag,
  tagPosition = 'top',
  tagStyle = {},
  textStyle = {},
  borderRadius,
  className = '',
  onError,
  onLoad,
}) => {
  /**
   * 渲染单个标签
   * @param tagProps - 标签属性
   * @param index - 当前索引
   */
  const renderTag = (tagProps: TagProps, index: number) => {
    const {
      text,
      position = tagPosition,
      customTagStyle,
      textStyle,
      backgroundColor = '#FF5B00',
      className: tagClassName = '',
      imageSrc,
      imageStyle = {},
      tagComponent
    } = tagProps;

    // 确保position是有效的值
    const validPosition = validatePosition(position);

    // 根据位置生成出更完整的样式对象，确保标签位置正确
    const generatePositionStyle = (position: string) => {
      // 基础样式，所有标签都是绝对定位
      const baseStyle = {
        position: 'absolute',
        zIndex: 1
      };
      
      // 根据位置生成相应的样式
      switch (position) {
        case 'top':
          return { ...baseStyle, top: 0, left: '50%', transform: [{ translateX: -size / 2 }] };
        case 'right':
          return { ...baseStyle, top: '50%', right: 0, transform: [{ translateY: -size / 2 }] };
        case 'bottom':
          return { ...baseStyle, bottom: 0, left: '50%', transform: [{ translateX: -size / 2 }] };
        case 'left':
          return { ...baseStyle, top: '50%', left: 0, transform: [{ translateY: -size / 2 }] };
        case 'topLeft':
          return { ...baseStyle, top: 0, left: 0 };
        case 'topRight':
          return { ...baseStyle, top: 0, right: 0 };
        case 'bottomLeft':
          return { ...baseStyle, bottom: 0, left: 0 };
        case 'bottomRight':
          return { ...baseStyle, bottom: 0, right: 0 };
        default:
          return baseStyle;
      }
    };

    // 使用函数式编程生成最终的标签样式
    const tagStyleMerged = pipe(
      () => generatePositionStyle(validPosition),
      mergeRight({ backgroundColor: tagComponent ? 'transparent' : backgroundColor }),
      mergeRight(tagStyle || {}),
      mergeRight(customTagStyle || {})
    )({}) as React.CSSProperties;


    // 使用函数式编程构建类名
    const tagClassNames = pipe(
      () => [tagClassName],
      filter(Boolean),
      join(' ')
    )();

    // 如果使用自定义标签组件，直接返回包裹的组件
    if (tagComponent) {
      return (
        <View
          key={index}
          className={tagClassNames}
          style={tagStyleMerged}
        >
          {tagComponent}
        </View>
      );
    }

    // 使用函数式编程确定标签类型
    const determineTagType = () => {
      if (imageSrc) {
        return text ? TagType.ICON_PREFIX : TagType.IMAGE;
      }
      return TagType.TEXT;
    };
    
    console.log('tagStyleMerged', textStyle)
    // 使用 CustomTag 组件替代自定义实现
    return (
      <View
        key={index}
        className={tagClassNames}
        style={tagStyleMerged}
      >
        <CustomTag
          type={determineTagType()}
          text={text}
          textStyle={textStyle}
          className="shop-image-tag__custom-tag"
          tagImage={imageSrc}
          prefixIcon={imageSrc && text ? imageSrc : undefined}
        />
      </View>
    );
  };


  // 处理标签数据，确保为数组形式
  const tags = processTags(tag, tagPosition);

  // 使用函数式编程构建图片容器样式
  const containerStyle = pipe(
    () => ({
      width: size,
      height: size,
      borderRadius: borderRadius || 0
    }),
    mergeRight(style || {})
  )({});

  // 使用函数式编程构建图片样式
  const imgStyle = pipe(
    () => ({
      width: size,
      height: size,
      borderRadius: borderRadius || 0
    }),
    mergeRight(imageStyle || {})
  )({});

  // 使用函数式编程构建容器类名
  const containerClassName = pipe(
    () => [
      'shop-image-tag',
      className
    ],
    filter(Boolean),
    join(' ')
  )();

  return (
    <View
      className={containerClassName}
      style={containerStyle}
    >
      <Image
        className="shop-image-tag__image"
        src={src}
        style={imgStyle}
        onLoad={onLoad}
        onError={onError}
      />
      {tags.map(renderTag)}
    </View>
  );
};

export default memo(ShopImageTag);
