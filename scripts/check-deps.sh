#!/bin/bash

# 依赖检查脚本
# 检查包依赖的一致性和安全性

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 开始依赖检查...${NC}"

# 1. 检查重复依赖
echo -e "${GREEN}📋 检查重复依赖...${NC}"
yarn list --pattern "react" | grep -E "react@" || true
yarn list --pattern "typescript" | grep -E "typescript@" || true

# 2. 检查过时的依赖
echo -e "${GREEN}📅 检查过时的依赖...${NC}"
yarn outdated || true

# 3. 安全审计
echo -e "${GREEN}🔒 运行安全审计...${NC}"
yarn audit --level moderate || true

# 4. 检查包大小
echo -e "${GREEN}📊 检查包大小...${NC}"
for package in packages/*/; do
  if [ -d "$package/dist" ]; then
    package_name=$(basename "$package")
    echo -e "${YELLOW}📦 $package_name:${NC}"
    du -sh "$package/dist"
  fi
done

# 5. 检查内部依赖版本一致性
echo -e "${GREEN}🔗 检查内部依赖版本...${NC}"
for package in packages/*/package.json; do
  package_dir=$(dirname "$package")
  package_name=$(basename "$package_dir")
  echo -e "${YELLOW}📦 $package_name:${NC}"
  
  # 检查是否有 @jd/lifeui-* 依赖
  if grep -q "@jd/lifeui-" "$package"; then
    grep "@jd/lifeui-" "$package" | sed 's/^/  /'
  else
    echo "  无内部依赖"
  fi
done

# 6. 检查 peer dependencies
echo -e "${GREEN}👥 检查 peer dependencies...${NC}"
for package in packages/*/package.json; do
  package_dir=$(dirname "$package")
  package_name=$(basename "$package_dir")
  
  if grep -q "peerDependencies" "$package"; then
    echo -e "${YELLOW}📦 $package_name:${NC}"
    grep -A 10 "peerDependencies" "$package" | sed 's/^/  /'
  fi
done

echo -e "${BLUE}✅ 依赖检查完成！${NC}"
