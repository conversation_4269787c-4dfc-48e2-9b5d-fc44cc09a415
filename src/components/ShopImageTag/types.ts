import React from 'react';
import { CSSProperties } from 'react';
// 定义有效的标签位置常量
export const VALID_POSITIONS = ['top', 'right', 'bottom', 'left', 'topLeft', 'topRight', 'bottomLeft', 'bottomRight'] as const;
export type ValidPosition = typeof VALID_POSITIONS[number];

/**
 * 标签位置配置 - 用于生成位置样式
 */
export const TAG_POSITION_CONFIG = {
  top: { vertical: 'top', horizontal: 'left', transform: 'translateX' },
  right: { vertical: 'top', horizontal: 'right', transform: 'translateY' },
  bottom: { vertical: 'bottom', horizontal: 'left', transform: 'translateX' },
  left: { vertical: 'left', horizontal: 'top', transform: 'translateY' },
  topLeft: { vertical: 'top', horizontal: 'left', transform: 'none' },
  topRight: { vertical: 'top', horizontal: 'right', transform: 'none' },
  bottomLeft: { vertical: 'bottom', horizontal: 'left', transform: 'none' },
  bottomRight: { vertical: 'bottom', horizontal: 'right', transform: 'none' }
};

/**
 * 标签位置类型
 */
export type TagPosition = 'top' | 'right' | 'bottom' | 'left' | 'topLeft' | 'topRight' | 'bottomLeft' | 'bottomRight';

/**
 * 标签属性
 */
export interface TagProps {
  /** 标签文本 */
  text?: string;
  /** 标签位置 */
  position?: TagPosition;
  /** 标签背景色 */
  backgroundColor?: string;
  /** 标签文字样式 */
  textStyle?: any;
  /** 标签类名（仅在 Taro 环境下有效）*/
  className?: string;
  /** 标签图片地址 */
  imageSrc?: string;
  /** 标签图片尺寸 */
  imageSize?: number;
  /** 标签图片样式 */
  imageStyle?: any;
  /** 自定义标签组件 */
  tagComponent?: React.ReactNode;

  /** 自定义标签样式 */
  customTagStyle?: CSSProperties;
}

/**
 * 图片标签组件基础属性
 */
export interface BaseImageTagProps {
  /** 图片地址 */
  src: string;
  /** 图片尺寸 */
  size?: number;
  /** 图片圆角 */
  borderRadius?: number;
  /** 标签配置 */
  tag?: TagProps | TagProps[];
  /** 图片加载失败回调 */
  onError?: () => void;
  /** 图片加载完成回调 */
  onLoad?: () => void;
}

/**
 * Taro 版本的图片标签组件属性
 */
export interface TaroImageTagProps extends BaseImageTagProps {
  /** 图片样式 */
  imageStyle?: any;
  /** 标签位置 */
  tagPosition?: TagPosition;
  /** 标签样式 */
  tagStyle?: any;
  /** 标签文本样式 */
  textStyle?: any;
  /** 容器样式 */
  style?: any;
  /** 容器类名 */
  className?: string;
  /** 子元素 */
  children?: React.ReactNode;
}

/**
 * RN 版本的图片标签组件属性
 */
export interface RNImageTagProps extends Omit<BaseImageTagProps, 'onError' | 'onLoad' | 'tagPosition' | 'tagStyle' | 'style'> {
  // RN 特有属性
  /** 容器样式 */
  style?: any;
  /** 图片样式 */
  imageStyle?: any;
  /** 标签样式 */
  tagStyle?: any;
  /** 标签文字样式 */
  textStyle?: any;
  /** 标签位置 */
  tagPosition?: TagPosition;
  /** 图片加载失败回调 */
  onError?: (error: any) => void;
  /** 图片加载完成回调 */
  onLoad?: () => void;
  /** 子元素 */
  children?: React.ReactNode;
  /** 容器类名 */
  className?: string;
}

// 导出兼容类型
export type ImageTagProps = RNImageTagProps;

/**
 * Taro 版本的位置样式映射
 */
export const positionToStyleMap = {
  top: (_size: number) => ({
    position: 'absolute',
    top: 0,
    left: '50%',
    transform: 'translateX(-25%)',
  }),
  right: (_size: number) => ({
    position: 'absolute',
    right: 0,
    top: '50%',
    transform: 'translateY(-25%)',
  }),
  bottom: (_size: number) => ({
    position: 'absolute',
    bottom: 0,
    left: '50%',
    transform: 'translateX(-25%)',
  }),
  left: (_size: number) => ({
    position: 'absolute',
    left: 0,
    top: '50%',
    transform: 'translateY(-25%)',
  }),
  topLeft: (_size: number) => ({
    position: 'absolute',
    top: 0,
    left: 0,
  }),
  topRight: (_size: number) => ({
    position: 'absolute',
    top: 0,
    right: 0,
  }),
  bottomLeft: (_size: number) => ({
    position: 'absolute',
    bottom: 0,
    left: 0,
  }),
  bottomRight: (_size: number) => ({
    position: 'absolute',
    bottom: 0,
    right: 0,
  }),
};

/**
 * 创建 RN 版本的位置样式
 */
export const createRNPositionStyle = (position: ValidPosition, size: number): any => {
  const config = TAG_POSITION_CONFIG[position];

  // 处理角落位置，角落位置不需要变换
  if (config.transform === 'none') {
    return {
      position: 'absolute',
      [config.vertical]: 0,
      [config.horizontal]: 0
    };
  }

  // 处理边缘中间位置，需要变换
  const transform = config.transform === 'translateX'
    ? [{ translateX: -size / 4 }]
    : [{ translateY: -size / 4 }];

  return {
    position: 'absolute',
    [config.vertical]: 0,
    [config.horizontal]: config.vertical === 'top' || config.vertical === 'bottom' ? '50%' : '50%',
    transform
  };
};

/**
 * RN 版本的位置样式映射 - 为每个位置创建一个函数
 */
export const createRNPositionToStyleMap = () => {
  const map: Record<ValidPosition, (size: number) => any> = {} as any;

  VALID_POSITIONS.forEach(position => {
    map[position] = (size: number) => createRNPositionStyle(position, size);
  });

  return map;
};

export const rnPositionToStyleMap = createRNPositionToStyleMap();
