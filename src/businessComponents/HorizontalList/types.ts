import React from 'react';
import { ViewStyle, ViewToken } from 'react-native';
import { CouponCardProps } from '../../components/display/CouponCard/types';
import { ProductCardProps } from '../../components/display/ProductCard/types';

// 列表项的类型定义
export type ListItemType = 
  | { type: 'coupon'; data: CouponCardProps } 
  | { type: 'product'; data: ProductCardProps };

export interface HorizontalListProps {
  /** 列表数据 */
  items: ListItemType[];
  /** 是否显示水平滚动条 */
  showScrollbar?: boolean;
  /** 列表项之间的间距 */
  itemSpacing?: number;
  /** 列表左右内边距 */
  horizontalPadding?: number;
  /** 列表项宽度 */
  itemWidth?: number | string;
  /** 自定义渲染某个列表项的方法 */
  renderItem?: (item: ListItemType, index: number) => React.ReactNode;
  /** 列表为空时的占位内容 */
  emptyComponent?: React.ReactNode;
  /** 列表滚动到末尾时触发的事件 */
  onEndReached?: () => void;
  /** 列表滚动事件 */
  onScroll?: (event: any) => void;
  /** 列表项点击事件 */
  onItemClick?: (item: ListItemType, index: number) => void;
  /** 可见列表项变化事件 */
  onViewableItemsChanged?: (info: { viewableItems: ViewToken[]; changed: ViewToken[] }) => void;
  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
  /** 自定义根元素内联样式 (Taro/Web) */
  style?: React.CSSProperties;
  /** 自定义根元素样式 (RN) */
  containerStyle?: ViewStyle;
  /** 自定义列表容器样式 (RN) */
  listStyle?: ViewStyle;
} 