import React from 'react';
import { View, Text, ScrollView, StyleSheet } from 'react-native';
import ShopHeader from './shopHeader';
import { ContentType } from './types';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 16,
  },
  pageTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
    textAlign: 'center',
  },
  section: {
    marginBottom: 20,
    backgroundColor: '#ffffff',
    borderRadius: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
    marginTop: 12,
  },
  iconWithText: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  inlineImage: {
    width: 16,
    height: 16,
    marginRight: 4,
  },
});

const ShopHeaderDemo: React.FC = () => {
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <Text style={styles.pageTitle}>店铺信息展示组件</Text>
      
      <Text style={styles.sectionTitle}>基础用法 - 标题和标签</Text>
      <View style={styles.section}>
        <ShopHeader
          imageUrl="https://img.example.com/shop/logo.png"
          lines={[
            {
              left: [
                {
                  type: ContentType.TITLE,
                  text: "品牌官方旗舰店",
                  fontSize: 18,
                  style: { color: '#e93b3d' }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: "官方认证",
                  backgroundColor: '#f0f9ff',
                  textStyle: { color: '#1890ff' }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: "优惠券",
                  showBorder: true,
                  borderColor: '#e93b3d',
                  textStyle: { color: '#e93b3d' }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: "满减",
                  backgroundColor: '#fff0f0',
                  textStyle: { color: '#e93b3d' }
                }
              ],
              height: 30
            }
          ]}
        />
      </View>
      
      <Text style={styles.sectionTitle}>下酒店铺卡片示例</Text>
      <View style={styles.section}>
        <ShopHeader 
          imageUrl="https://img10.360buyimg.com/img/jfs/t1/313656/11/2760/13502/682d8341Fd74611c4/d647a2c4bcdf2c76.png"
          imageWidth={72}
          imageHeight={72}
          lines={[
            {
              left: [
                {
                  type: ContentType.TITLE,
                  text: "下酒 (林肯公园店)",
                  fontSize: 20,
                  style: { color: '#1A1A1A', fontWeight: 'bold' }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: '已收藏',
                  backgroundColor: '#fff9f0',
                  textStyle: { color: '#996633' }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '4.9分',  
                  backgroundColor: '#fff0f0',
                  textStyle: { color: '#e93b3d', fontWeight: 'bold' }
                },
                {
                  type: ContentType.TAG,
                  text: '2372条评价', 
                  backgroundColor: '#f5f5f5',
                  textStyle: { color: '#999' }
                },
                {
                  type: ContentType.TAG,
                  text: '￥95/人', 
                  backgroundColor: '#f5f5f5',
                  textStyle: { color: '#999' }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: '营业至1:00',
                  backgroundColor: '#fff9f0',
                  textStyle: { color: '#996633' }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '烧烤',  
                  backgroundColor: '#f5f5f5',
                  textStyle: { color: '#999' }
                },
                {
                  type: ContentType.TAG,
                  text: '亦庄', 
                  backgroundColor: '#f5f5f5',
                  textStyle: { color: '#999' }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: '距您步行321m',
                  backgroundColor: '#f5f5f5',
                  textStyle: { color: '#999' }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '亦庄烧烤销量榜第1名', 
                  backgroundColor: '#fff9f0',
                  textStyle: { color: '#996633' }
                },
                {
                  type: ContentType.TAG,
                  text: '当地人推荐', 
                  backgroundColor: '#fff9f0',
                  textStyle: { color: '#996633' }
                },
                {
                  type: ContentType.TAG,
                  text: 'PLUS免费停车', 
                  backgroundColor: '#fff9f0',
                  textStyle: { color: '#996633' }
                }
              ]
            }
          ]}
          lineSpacing={0}
        />
      </View>
      
      <Text style={styles.sectionTitle}>多个标签使用案例</Text>
      <View style={styles.section}>
        <ShopHeader 
          imageUrl="https://img.example.com/shop/logo.png"
          lines={[
            {
              left: [
                {
                  type: ContentType.TITLE,
                  text: "京东自营官方旗舰店",
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '新品', 
                  backgroundColor: '#e6f7ff',
                  textStyle: { color: '#1890ff' }
                },
                {
                  type: ContentType.TAG,
                  text: '热卖', 
                  backgroundColor: '#fff2e8',
                  textStyle: { color: '#fa541c' }
                },
                {
                  type: ContentType.TAG,
                  text: '促销', 
                  backgroundColor: '#f6ffed',
                  textStyle: { color: '#52c41a' }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '免运费',
                  backgroundColor: '#f5f5f5',
                  textStyle: { color: '#999' }
                },
                {
                  type: ContentType.TAG,
                  text: '正品保障',
                  backgroundColor: '#f5f5f5',
                  textStyle: { color: '#999' }
                },
                {
                  type: ContentType.TAG,
                  text: '7天无理由退货',
                  backgroundColor: '#f5f5f5',
                  textStyle: { color: '#999' }
                },
                {
                  type: ContentType.TAG,
                  text: '闪电发货',
                  backgroundColor: '#f5f5f5',
                  textStyle: { color: '#999' }
                }
              ]
            }
          ]}
        />
      </View>
      
      <Text style={styles.sectionTitle}>带标签的标题示例</Text>
      <View style={styles.section}>
        <ShopHeader 
          imageUrl="https://img.example.com/shop/logo.png"
          lines={[
            {
              left: [
                {
                  type: ContentType.TITLE,
                  text: "品牌连锁店",
                  fontSize: 18,
                  tags: [
                    {
                      text: "品牌",
                      backgroundColor: "#ff4d4f",
                      textStyle: { color: "#fff" }
                    },
                    {
                      text: "连锁",
                      backgroundColor: "#1890ff",
                      textStyle: { color: "#fff" }
                    }
                  ],
                  style: {
                    color: '#333',
                    fontWeight: 'bold'
                  }
                }
              ]
            }
          ]}
        />
      </View>
      
      <Text style={styles.sectionTitle}>万家早安早餐店示例</Text>
      <View style={styles.section}>
        <ShopHeader 
          imageUrl="https://img30.360buyimg.com/vendersettle/jfs/t1/288943/8/4072/226575/681d994cF8a48657e/9bb15de0b2ef8ef5.jpg"
          imageWidth={72}
          imageHeight={72}
          lineSpacing={6}
          lines={[
            {
              left: [
                {
                  type: ContentType.TAG,
                  tagImage: "https://img13.360buyimg.com/imagetools/jfs/t1/290926/12/3398/4189/6819ea74F4f5b1c38/1f646e3a88d34e20.png",
                  tagImageStyle: {
                    width: 16,
                    height: 16
                  },
                  textStyle: {
                    color: "#999"
                  }
                },
                {
                  type: ContentType.TITLE,
                  text: "万家早安·品致早餐（次渠店）",
                  fontSize: 15,
                  lines: 1,
                  style: {
                    height: 22,
                    color: "#1A1A1A",
                    fontWeight: "bold",
                    fontFamily: "PingFangSC-Bold"
                  }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: "",
                  backgroundColor: "transparent",
                  tagImage: "https://img30.360buyimg.com/mobilecms/jfs/t1/265151/15/24587/8005/67bd9b68Fcc947dba/7e6aa1b1b5944bc4.png",
                  textStyle: {
                    color: "#999"
                  }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: "4.7",
                  backgroundColor: "transparent",
                  textStyle: {
                    color: "#FA2C19",
                    fontSize: 13
                  }
                },
                {
                  type: ContentType.TAG,
                  text: "起送¥18",
                  backgroundColor: "transparent",
                  textStyle: {
                    color: "#999",
                    fontSize: 14
                  }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: "1.7km",
                  backgroundColor: "transparent",
                  textStyle: {
                    color: "#999",
                    fontSize: 14
                  }
                },
                {
                  type: ContentType.TAG,
                  text: "23分钟达",
                  backgroundColor: "transparent",
                  textStyle: {
                    color: "#999",
                    fontSize: 14
                  }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: "台湖镇餐饮美食第4名",
                  backgroundColor: "transparent",
                  textStyle: {
                    color: "#B5691A"
                  }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: "已售6万+",
                  backgroundColor: "transparent",
                  textStyle: {
                    color: "#999",
                    fontSize: 14
                  }
                }
              ]
            },
            {
              left: [],
              right: [
                {
                  type: ContentType.TAG,
                  text: "已售6万+",
                  backgroundColor: "transparent",
                  textStyle: {
                    color: "#999",
                    fontSize: 14
                  }
                }
              ]
            }
          ]}
        />
      </View>
    </ScrollView>
  );
};

export default ShopHeaderDemo; 