/**
 * 平台环境检测工具（默认值版本）
 * 
 * 本文件提供默认的平台检测工具，主要用于非Web非RN环境或服务端渲染时使用
 * 会在构建时根据不同平台自动选择对应实现：
 * - Web环境使用 index.web.ts
 * - React Native环境使用 index.rn.ts
 * - 其他环境使用当前文件
 * 
 * 所有环境判断默认返回false，确保在非特定环境下安全使用
 */
import Taro from '@tarojs/taro'

/**
 * 当前运行环境类型
 */
export const env: TaroGeneral.ENV_TYPE = Taro.getEnv()

/**
 * 是否为Web环境
 */
export const isWeb = false

/**
 * 是否为微信小程序环境
 */
export const isWechatMini = env === 'WEAPP'

/**
 * 是否为京东App环境
 */
export const isJDApp = false

/**
 * 是否为微信环境
 */
export const isWeixin = false

/**
 * 是否为鸿蒙系统环境
 */
export const isHarmonyOS = false

/**
 * 是否为iOS系统环境
 */
export const isIOS = false

/**
 * 是否为Android系统环境
 */
export const isAndroid = false

/**
 * 是否为手机QQ环境
 */
export const isMobileQQ = false

/**
 * 是否为Safari浏览器环境
 */
export const isSafari = false

/**
 * 是否支持导航器GPS
 */
export const supportNavigatorGps = false

/**
 * 是否为京小包APP环境
 */
export const isJDMiniApp = false

/**
 * 是否为京东特价版APP环境
 */
export const isJDLtApp = false

/**
 * 是否为京东主站APP导流中间页入口
 */
export const noJDLogin = false

/**
 * 是否为京喜APP环境
 */
export const isJDPinGou = false

/**
 * 是否为达达APP环境
 */
export const isDadaApp = false

/**
 * 是否为京东到家APP环境
 */
export const isDaojiaApp = false

/**
 * 是否为支持WKWebView的到家APP
 */
export const supportDJSHWK = false

/**
 * 京东APP版本号
 */
export const jdAppVersion = undefined

/**
 * 是否为京东金融APP环境
 */
export const isjdjrApp = false

/**
 * 是否为支付宝APP环境（用于售后处方药和到家小程序转支付宝小程序）
 */
export const isZhifubaoApp = false

/**
 * 是否为京购小程序环境
 */
export const isJingGouMiniprogram = false

/**
 * 获取京东手机信息
 * 在非京东APP环境中返回拒绝的Promise
 * @returns {Promise<any>} 包含设备信息的Promise
 */
export const getJdPhoneInfo = function () {
    return new Promise((resolve, reject) => {
        reject('not in jd app!')
    })
}

/**
 * 京东设备ID
 * 在非京东APP环境中返回空字符串
 */
export const jdDevice = Promise.resolve('')

export const isH5 = false
export const isPad = false
