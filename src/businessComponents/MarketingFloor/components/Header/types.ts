import React from 'react';
import { ViewStyle, TextStyle } from 'react-native';

export interface HeaderProps {
  /** 顶部左侧百亿补贴图片地址 */
  subsidyImageUrl?: string;
  /** 顶部左侧百亿补贴图片宽度 */
  subsidyImageWidth?: number;
  /** 顶部左侧百亿补贴图片高度 */
  subsidyImageHeight?: number;
  /** 顶部左侧官方补贴文案 */
  officialSubsidyText?: string;
  /** 顶部左侧气泡背景图片地址 */
  bubbleImageUrl?: string;
  /** 顶部右侧是否显示查看更多 */
  showMoreLink?: boolean;
  /** 顶部右侧查看更多文案 */
  moreText?: string;
  /** 顶部右侧查看更多点击事件 */
  onMoreClick?: () => void;
  /** 顶部背景色 */
  backgroundColor?: string;
  /** 顶部文字颜色 */
  textColor?: string;
  /** 顶部文字大小 */
  fontSize?: number;
  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
  /** 自定义根元素内联样式 (Taro/Web) */
  style?: React.CSSProperties;
  /** 自定义根元素样式 (RN) */
  containerStyle?: ViewStyle;
} 