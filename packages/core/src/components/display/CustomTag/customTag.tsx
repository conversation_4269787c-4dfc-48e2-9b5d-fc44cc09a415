import React, { memo } from 'react';
import { cond, equals, always, pipe, mergeRight, filter, join } from 'ramda';
import { TaroCustomTagProps, TagType } from './types';
import { TextTag, ImageTag, PrefixIconTag, SuffixIconTag } from './components';
import './customTag.scss';

/**
 * 自定义标签组件
 * 用于展示各种风格的标签，支持自定义样式和文本
 * 根据type类型展示不同的标签样式：
 * - TEXT: 纯文字版本
 * - IMAGE: 图片版本
 * - ICON_PREFIX: 前置图标 + 文字/图片版本
 * - ICON_SUFFIX: 后置图标 + 文字/图片版本
 */
const CustomTag: React.FC<TaroCustomTagProps> = ({
  type,
  text = '',
  style,
  textStyle,
  className,
  iconStyle,
  tagImage,
  tagImageStyle,
  prefixIcon,
  suffixIcon,
  ...props
}) => {

  // 合并样式
  const containerStyleMerged = pipe(
    mergeRight(style || {}),
    mergeRight({ backgroundColor: 'transparent' })
  )({});

  // 定义常规类名，用于标签组件
  const commonClassName = pipe(
    () => [
      'custom-tag',
      className
    ],
    filter(Boolean),
    join(' ')
  )();

  // 使用 cond 代替 switch 来渲染不同类型的标签
  const renderTag = cond([
    // 纯文本标签
    [equals(TagType.TEXT), always(
      <TextTag
        text={text}
        textStyle={textStyle}
        style={containerStyleMerged}
        className={commonClassName}
        {...props}
      />
    )],
    // 纯图片标签
    [equals(TagType.IMAGE), always(
      <ImageTag
        tagImage={tagImage || ''}
        tagImageStyle={tagImageStyle}
        style={containerStyleMerged}
        className={commonClassName}
        {...props}
      />
    )],
    // 前置图标标签
    [equals(TagType.ICON_PREFIX), always(
      <PrefixIconTag
        iconSrc={prefixIcon || ''}
        iconStyle={iconStyle}
        contentType={tagImage ? 'image' : 'text'}
        text={text}
        textStyle={textStyle}
        tagImage={tagImage}
        tagImageStyle={tagImageStyle}
        style={containerStyleMerged}
        className={commonClassName}
        {...props}
      />
    )],
    // 后置图标标签
    [equals(TagType.ICON_SUFFIX), always(
      <SuffixIconTag
        iconSrc={suffixIcon || ''}
        iconStyle={iconStyle}
        contentType={tagImage ? 'image' : 'text'}
        text={text}
        textStyle={textStyle}
        tagImage={tagImage}
        tagImageStyle={tagImageStyle}
        style={containerStyleMerged}
        className={commonClassName}
        {...props}
      />
    )]
  ]);

  return renderTag(type);
};

export default memo(CustomTag);
