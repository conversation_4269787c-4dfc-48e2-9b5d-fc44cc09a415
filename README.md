### 通用UI组件库目录

---

## 首次开发环境准备

1. **拉取代码**
   ```bash
   git clone <你的仓库地址>
   cd <项目目录>
   ```

2. **安装依赖**
   ```bash
   npm install
   # 或
   yarn install
   ```

3. **代码规范说明**

   ### 样式规范（Stylelint）
   本项目采用自定义 Stylelint 规则（见 `stylelint-rn-rules` 目录），用于约束样式书写，确保兼容 React Native 端。

   - 只允许 RN 官方文档支持的样式属性
   - 禁止 position: fixed、background-image、float、box-shadow 等不兼容属性
   - display: flex 必须有 flex-direction
   - 只允许单一类选择器，禁止组合选择器、伪类、伪元素

   ### 代码规范（ESLint）
   本项目采用自定义 ESLint 插件（见 `eslint-plugin-taro-rn-best-practices` 目录），用于约束 JS/TS 代码风格和 React Native 端最佳实践。

   - 强制显式声明 flexDirection
   - 禁止 position: fixed、内联样式、不支持的样式属性等
   - 要求 Image 必须声明宽高
   - 文本必须包裹在 <Text /> 组件内
   - 其他跨端一致性相关规则

4. **本地检查规范**

   - 检查样式规范：
     ```bash
     npx stylelint "src/**/*.{css,scss,less}"
     ```
   - 检查代码规范：
     ```bash
     npx eslint src
     ```
   - 推荐集成到编辑器（如 VSCode）自动检查。

5. **CI/提交前校验（可选）**
   推荐在 CI 或 pre-commit 钩子中集成 stylelint 和 eslint 检查，保证团队协作时规范统一。

---

如需了解自定义 Stylelint 插件的详细规则，请查阅 `stylelint-rn-rules/index.js` 文件。
如需了解自定义 ESLint 插件的详细规则，请查阅 `eslint-plugin-taro-rn-best-practices` 目录下的文档和源码。