.coupon-card {
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  
  &__background {
    display: block;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
  }
  

  &__content {
    position: relative;
    z-index: 1;
    display: flex;
    flex-direction: column;
    // padding: 0 8px;
    height: 100%;
    width: 100%;
    align-items: center;
    justify-content: flex-start;
  }

  &__title {
    font-family: 'PingFang SC';
    font-weight: 600;
    font-size: 12px;
    letter-spacing: 0px;
    color: #8F4100;
    text-align: center;
    margin-top: 3px;
    width: 90%;
    overflow: hidden;
    
    &--no-condition {
      margin-top: 13px;
    }
  }

  &__amount {
    display: flex;
    justify-content: center;
    width: 100%;
    align-items: center;
    text-align: center;
  }

  &__condition {
    font-family: 'PingFang SC';
    font-weight: 400;
    font-size: 10px;
    width: 90%;
    color: #8F4100;
    overflow: hidden;
    text-align: center;
  }
} 