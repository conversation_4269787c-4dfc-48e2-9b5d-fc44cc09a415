import { ESLintUtils } from '@typescript-eslint/utils';

const createRule = ESLintUtils.RuleCreator(
  (name) => `https://docs.taro.zone/docs/react-native-remind#${name}`
);

export default createRule({
  name: 'text-must-be-in-text-component',
  meta: {
    type: 'problem',
    docs: {
      description: 'Enforce that text is always inside a Text component in Taro React Native.',
      recommended: 'error',
    },
    messages: {
      textMustBeInTextComponent: 'Text strings must be rendered within a <Text> component',
    },
    schema: [],
    fixable: 'code',
  },
  defaultOptions: [],
  create(context) {
    return {
      JSXElement(node) {
        // Skip if this is already a Text component
        if (
          node.openingElement.name.type === 'JSXIdentifier' &&
          node.openingElement.name.name === 'Text'
        ) {
          return;
        }

        // Check for text children
        for (const child of node.children) {
          if (child.type === 'JSXText' && child.value.trim() !== '') {
            context.report({
              node: child,
              messageId: 'textMustBeInTextComponent',
              fix: (fixer) => {
                return fixer.replaceText(
                  child,
                  `<Text>${child.value}</Text>`
                );
              },
            });
          }
        }
      },
    };
  },
});