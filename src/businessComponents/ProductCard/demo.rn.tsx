import React from 'react';
import { SafeAreaView, ScrollView, StyleSheet, Alert } from 'react-native';
import View from '../../components/basic/View';
import Text from '../../components/basic/Text';
import { ProductCard } from './index';

const ProductCardDemoRN: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.demoItem}>
          <Text style={styles.title}>基本用法 - 与图片一致的促销商品卡</Text>
          <ProductCard 
            imageUrl="https://img.example.com/crab.jpg"
            title="袁记云饺"
            description="帝王蟹畅吃"
            currentPrice={199}
            originalPrice={399}
            priceType="discount"
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>不带原价</Text>
          <ProductCard 
            imageUrl="https://img.example.com/food.jpg"
            title="美味佳肴"
            description="限时特惠"
            currentPrice={99}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>自定义样式</Text>
          <ProductCard 
            imageUrl="https://img.example.com/special.jpg"
            title="特色菜品"
            description="精选食材"
            currentPrice={159}
            originalPrice={259}
            titleColor="#1890FF"
            descriptionColor="#52C41A"
            priceProps={{
              currentPriceColor: '#F5222D',
              originalPriceColor: '#8C8C8C'
            }}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>自定义图片尺寸</Text>
          <ProductCard 
            imageUrl="https://img.example.com/large.jpg"
            imageWidth={250}
            imageHeight={180}
            title="大份量美食"
            description="家庭聚餐首选"
            currentPrice={299}
            originalPrice={399}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>带点击事件</Text>
          <ProductCard 
            imageUrl="https://img.example.com/clickable.jpg"
            title="点击查看详情"
            description="优惠多多"
            currentPrice={88}
            originalPrice={118}
            onClick={() => Alert.alert('提示', '点击了商品卡片')}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 16,
  },
  demoItem: {
    marginBottom: 20,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333333',
  }
});

export default ProductCardDemoRN; 