/* BEM命名规范的SCSS样式 */
.one-line-two-biz {
  display: flex;
  flex-direction: column;
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  position: relative;
  
  &__image-container {
    position: relative;
    width: 100%;
    border-radius: 8px 8px 0 0;
    overflow: hidden;
  }
  
  &__distance {
    position: absolute;
    left: 8px;
    bottom: 8px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #ffffff;
    font-size: 12px;
    padding: 2px 6px;
    border-radius: 4px;
  }
  
  &__content {
    padding: 8px 7px;
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  
  &__title-container {
    margin-bottom: 4px;
  }
  
  &__price-sales-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  &__price-container {
    display: flex;
    align-items: baseline;
  }
  
  &__monthly-sales {
    font-size: 12px;
    color: #999999;
  }
  
  &__discount-label {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #FF1530;
    color: #ffffff;
    font-size: 12px;
    padding: 2px 8px;
    border-radius: 0 0 0 8px;
  }
}
