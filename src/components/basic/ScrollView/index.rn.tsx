import { ScrollView as RNScrollView } from 'react-native';
import withClassName from '../utils/withClassName.rn';
import { forwardRef } from 'react';


function ScrollView(props, ref) {
  const {
    style,
    children,
    scrollX,
    horizontal,
    alwaysBounceHorizontal,
    showsHorizontalScrollIndicator = false,
    onTouchStart,
    onTouchEnd,
    keyboardShouldPersistTaps = 'always',
    showsVerticalScrollIndicator = false,
    onLayout,
    onContentSizeChange,
    contentContainerStyle,
    onScroll,
  } = props;

  return (
    <RNScrollView
      ref={ref}
      style={style}
      horizontal={scrollX || horizontal}
      alwaysBounceHorizontal={alwaysBounceHorizontal}
      showsHorizontalScrollIndicator={showsHorizontalScrollIndicator}
      onTouchStart={onTouchStart}
      onTouchEnd={onTouchEnd}
      keyboardShouldPersistTaps={keyboardShouldPersistTaps}
      showsVerticalScrollIndicator={showsVerticalScrollIndicator}
      onLayout={onLayout}
      onContentSizeChange={onContentSizeChange}
      contentContainerStyle={contentContainerStyle}
      onScroll={onScroll}
    >
      {children}
    </RNScrollView>
  );
}

export const _ScrollView = forwardRef(ScrollView)

export default withClassName()(_ScrollView)
