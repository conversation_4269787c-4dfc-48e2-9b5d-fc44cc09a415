# 贡献指南

感谢你考虑为 eslint-plugin-taro-rn-best-practices 做出贡献！

## 开发环境设置

1. Fork 并 clone 仓库
2. 安装依赖: `npm install`
3. 构建项目: `npm run build`
4. 运行测试: `npm test`

## 添加新规则

1. 在 `src/rules` 目录下创建新规则文件，例如 `new-rule.ts`
2. 在 `src/__tests__/rules` 目录下创建对应的测试文件，例如 `new-rule.test.ts`
3. 在 `src/index.ts` 中导入并注册新规则
4. 在 README.md 中添加新规则的文档

## 规则模板

```typescript
import { ESLintUtils } from '@typescript-eslint/utils';

const createRule = ESLintUtils.RuleCreator(
  (name) => `https://docs.taro.zone/docs/react-native-remind#${name}`
);

export default createRule({
  name: 'rule-name',
  meta: {
    type: 'problem', // 'problem', 'suggestion', or 'layout'
    docs: {
      description: 'Description of the rule',
      recommended: 'error', // 'error', 'warn', or false
    },
    messages: {
      messageId: 'Message text',
    },
    schema: [], // 规则选项
    fixable: 'code', // 'code', 'whitespace', or undefined
  },
  defaultOptions: [],
  create(context) {
    return {
      // ESLint 访问器
      Identifier(node) {
        // 规则逻辑
        context.report({
          node,
          messageId: 'messageId',
        });
      },
    };
  },
});
```

## 测试模板

```typescript
import { RuleTester } from '@typescript-eslint/utils/dist/eslint-utils';
import rule from '../../rules/rule-name';

const ruleTester = new RuleTester({
  parser: require.resolve('@typescript-eslint/parser'),
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
});

ruleTester.run('rule-name', rule, {
  valid: [
    {
      code: `// 有效代码示例`,
    },
  ],
  invalid: [
    {
      code: `// 无效代码示例`,
      errors: [{ messageId: 'messageId' }],
      output: `// 修复后的代码 (如果规则支持自动修复)`,
    },
  ],
});
```

## 提交 Pull Request

1. 确保所有测试通过
2. 更新文档
3. 提交 PR 并描述你的更改

感谢你的贡献！