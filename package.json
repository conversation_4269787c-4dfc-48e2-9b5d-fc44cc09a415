{"name": "@jd/lifeui", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "lerna run build --stream", "build:core": "lerna run build --scope @jd/lifeui-core", "build:shared": "lerna run build --scope @jd/lifeui-business-shared", "build:takeout": "lerna run build --scope @jd/lifeui-business-takeout", "build:groupon": "lerna run build --scope @jd/lifeui-business-groupon", "build:travel": "lerna run build --scope @jd/lifeui-business-travel", "build:instant": "lerna run build --scope @jd/lifeui-business-instant", "dev:core": "cd packages/core && yarn dev", "dev:shared": "cd packages/business-shared && yarn dev", "dev:takeout": "cd packages/business-takeout && yarn dev", "dev:groupon": "cd packages/business-groupon && yarn dev", "dev:travel": "cd packages/business-travel && yarn dev", "dev:instant": "cd packages/business-instant && yarn dev", "test": "lerna run test", "lint": "lerna run lint", "clean": "lerna clean", "bootstrap": "lerna bootstrap", "publish:core": "lerna publish --scope @jd/lifeui-core", "publish:shared": "lerna publish --scope @jd/lifeui-business-shared", "publish:takeout": "lerna publish --scope @jd/lifeui-business-takeout", "publish:groupon": "lerna publish --scope @jd/lifeui-business-groupon", "publish:travel": "lerna publish --scope @jd/lifeui-business-travel", "publish:instant": "lerna publish --scope @jd/lifeui-business-instant", "publish:all": "lerna <PERSON>", "version:patch": "lerna version patch --conventional-commits", "version:minor": "lerna version minor --conventional-commits", "version:major": "lerna version major --conventional-commits", "version:prerelease": "lerna version prerelease --preid beta --conventional-commits", "release": "./scripts/release.sh", "release:patch": "./scripts/release.sh patch", "release:minor": "./scripts/release.sh minor", "release:major": "./scripts/release.sh major", "release:beta": "./scripts/release.sh prerelease", "check:deps": "./scripts/check-deps.sh", "check:changed": "lerna changed", "check:diff": "lerna diff", "audit:security": "yarn audit --level moderate", "clean:all": "lerna clean && rm -rf node_modules", "reset": "yarn clean:all && yarn install", "deps:check": "./scripts/manage-internal-deps.sh check", "deps:validate": "./scripts/manage-internal-deps.sh validate", "deps:sync": "./scripts/manage-internal-deps.sh sync", "deps:fix": "./scripts/fix-internal-deps.sh", "ci:check": "./scripts/ci-check.sh", "prerelease": "yarn ci:check"}, "devDependencies": {"@types/react": "^19.1.4", "@types/react-native": "^0.72.8", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.0.0", "eslint": "^9.0.0", "jest": "^29.0.0", "lerna": "^8.0.0", "sass-embedded": "1.89.0", "stylelint": "^16.19.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-order": "^6.0.4", "stylelint-scss": "^6.12.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-dts": "^4.0.0", "vite-plugin-libcss": "^1.0.6"}, "dependencies": {"@tarojs/taro": "4.1.1", "lodash-es": "4.17.21", "ramda": "0.30.1", "react-native-linear-gradient": "^2.8.3"}}