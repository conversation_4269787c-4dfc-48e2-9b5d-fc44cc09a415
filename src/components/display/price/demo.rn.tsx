import React from 'react';
import { Safe<PERSON>reaView, Sc<PERSON>View, StyleSheet } from 'react-native';
import View from '../../basic/View';
import Text from '../../basic/Text';
import { Price } from './index';

const PriceDemoRN: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.demoItem}>
          <Text style={styles.title}>默认样式 - 红色当前价格和灰色原价</Text>
          <Price 
            currentPrice={240} 
            originalPrice={1080} 
            type="default"
            priceTagWidth={
              100
            }
          />
        </View>
        <View style={styles.demoItem}>
          <Text style={styles.title}>默认样式 - 仅显示当前价格</Text>
          <Price 
            currentPrice={240} 
            type="default"
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>默认样式 - 自定义颜色和字体大小</Text>
          <Price 
            currentPrice={240} 
            originalPrice={1080} 
            type="default"
            currentPriceColor="#0066cc"
            currentPriceFontSize={30}
            currentPriceSymbolFontSize={20}
            originalPriceColor="#666666"
            originalPriceFontSize={16}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>促销样式 - 背景图</Text>
          <Price 
            currentPrice={39} 
            originalPrice={118} 
            type="discount"
            discountBackgroundImageSrc="https://img30.360buyimg.com/img/jfs/t1/301190/25/8889/652/682ee8f7F17feb63d/c33d3db673a40a68.png"
            discountBackgroundImageWidth={130}
            discountBackgroundImageHeight={40}
            currentPriceColor="#FF5252"
            currentPriceFontSize={22}
            currentPriceSymbolFontSize={14}
            originalPriceColor="#0066cc"
            originalPriceFontSize={16}
            originalPriceSymbolFontSize={12}
            // discountCurrentPriceLeftOffset={15}
            // discountOriginalPriceRightOffset={15}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>促销样式 - 无原价（使用背景图）</Text>
          <Price 
            currentPrice={199} 
            type="discount"
            discountBackgroundImageSrc="https://img30.360buyimg.com/img/jfs/t1/301190/25/8889/652/682ee8f7F17feb63d/c33d3db673a40a68.png"
            discountBackgroundImageWidth={100}
            discountBackgroundImageHeight={35}
            currentPriceColor="#FF5252"
            currentPriceFontSize={20}
            // currentPriceSymbolFontSize={14}
            // discountCurrentPriceLeftOffset={30}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>促销样式 - 自定义前景颜色（使用背景图）</Text>
          <Price 
            currentPrice={88} 
            originalPrice={288} 
            type="discount"
            discountBackgroundImageSrc="https://img30.360buyimg.com/img/jfs/t1/301190/25/8889/652/682ee8f7F17feb63d/c33d3db673a40a68.png"
            discountBackgroundImageWidth={130}
            discountBackgroundImageHeight={40}
            currentPriceColor="#FF5252"
            originalPriceColor="#0066cc"
            currentPriceFontSize={22}
            originalPriceFontSize={16}
            // discountCurrentPriceLeftOffset={15}
            // discountOriginalPriceRightOffset={15}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 16,
  },
  demoItem: {
    marginBottom: 20,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    alignItems: 'flex-start',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333333',
  }
});

export default PriceDemoRN; 