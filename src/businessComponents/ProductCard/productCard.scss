.product-card {
  background-color: #FFFFFF;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  flex-direction: column;
  max-width: 300px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  width: 100%;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-4px);
  }

  &__image-container {
    height: auto;
    overflow: hidden;
    position: relative;
    width: 100%;
  }

  &__image {
    display: block;
    height: auto;
    object-fit: cover;
    transition: transform 0.3s ease;
    width: 100%;

    &:hover {
      transform: scale(1.05);
    }
  }

  &__content {
    display: flex;
    flex-direction: column;
    padding: 12px;
  }

  &__title {
    color: #333333;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 4px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__description {
    color: #666666;
    font-size: 14px;
    margin-bottom: 8px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &__price {
    margin-top: 8px;
  }
} 