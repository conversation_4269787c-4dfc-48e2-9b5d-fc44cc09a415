import { CSSProperties } from 'react';
import { TagProps } from '../../../../core/src/components/display/ShopImageTag/types';

export interface OneLineTwoBizProps {
  // 整体商卡参数
  className?: string;
  style?: CSSProperties;
  onClick?: () => void;

  // 商品图片参数
  imageSrc: string;
  imageSize?: number;
  imageTag?: TagProps | TagProps[];
  distance?: string; // 距离信息，如图片左下角的3.5km

  // 商品名称参数
  productName: string;
  productNameFontSize?: number;
  productNameLines?: number;
  productNameEllipsis?: 'ellipsis' | 'fade';
  productNameTag?: TagProps | TagProps[];
  productNameStyle?: CSSProperties;

  // 商品价格参数
  currentPrice: number;
  originalPrice?: number;
  priceType?: 'default' | 'discount';
  currentPriceColor?: string;
  originalPriceColor?: string;
  currentPriceFontSize?: number;
  originalPriceFontSize?: number;
  currencySymbol?: string;
  currentPriceSymbolFontSize?: number;
  originalPriceSymbolFontSize?: number;
  currentPriceDecimalFontSize?: number;
  originalPriceDecimalFontSize?: number;
  strikethrough?: boolean;

  // 销量信息
  monthlySales?: number | string;
  monthlySalesLabel?: string;

  // 折扣信息
  discountPrefixIcon?: any // 折扣图片
  discountText:string,
  discountLabelBgColor?: string; // 折扣标签背景颜色
}
