import React from 'react';
import { View, Text, ScrollView, StyleSheet, SafeAreaView, Platform } from 'react-native';
import { BasePrice } from './index';

export default function BasePriceDemo() {
  const HEADER_HEIGHT = 56;
  return (
    <View style={styles.container}>
      {/* 顶部标题栏，绝对定位 */}
      <View style={[styles.header, { height: HEADER_HEIGHT }]}> 
        <Text style={[styles.headerTitle, { lineHeight: HEADER_HEIGHT }]}>Price</Text>
      </View>
      <ScrollView
        style={[styles.scrollView, { marginTop: HEADER_HEIGHT }]}
        contentContainerStyle={styles.content}
      >
        {/* 不保留小数 */}
        <Text style={styles.sectionTitle}>不保留小数</Text>
        <View style={styles.card}>
          <BasePrice 
            price={618} 
            symbolColor="#f2270c"
            integerColor="#f2270c"
            symbolFontSize={24}
            integerFontSize={32}
            decimalPlaces={0} 
          />
        </View>
        {/* 有人民币符号，无千位分隔 */}
        <Text style={styles.sectionTitle}>有人民币符号，无千位分隔</Text>
        <View style={styles.card}>
          <BasePrice 
            price={10010.01} 
            symbolColor="#f2270c"
            integerColor="#f2270c"
            decimalColor="#f2270c"
            symbolFontSize={24}
            integerFontSize={32}
            decimalFontSize={24}
          />
        </View>
        {/* 各种效果展示 */}
        <Text style={[styles.sectionTitle, { marginTop: 24 }]}>各种样式效果</Text>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>基础用法</Text>
          <BasePrice price={123.45} />
        </View>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>自定义颜色和字号</Text>
          <BasePrice
            price={12345.67}
            symbolColor="#0051cc"
            symbolFontSize={20}
            integerColor="#1a1a1a"
            integerFontSize={28}
            decimalColor="#fa2c19"
            decimalFontSize={16}
          />
        </View>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>删除线</Text>
          <BasePrice price={88.88} strikethrough />
        </View>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>自定义货币符号/无小数</Text>
          <BasePrice price={100} currencySymbol="$" showDecimal={false} />
        </View>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>小数部分更大</Text>
          <BasePrice price={0.99} integerFontSize={16} decimalFontSize={20} symbolFontSize={16} />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f8fa',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    paddingTop: Platform.OS === 'android' ? 0 : 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    marginBottom: 12,
    marginLeft: 4,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 14,
    color: '#999',
    marginBottom: 12,
  },
}); 