import { ReactNode } from 'react';
import { StyleProp, ViewStyle, TextStyle } from 'react-native';

/**
 * Diamond组件基础属性接口
 */
export interface BaseDiamondProps {
  /** 宽度 */
  width: number;
  /** 高度 */
  height?: number;
  /** 水平内边距 */
  paddingHorizontal?: number;

  /** 是否选中 */
  selected?: boolean;
  /** 是否禁用 */
  disabled?: boolean;

  /** 图片尺寸 */
  imageSize: number;
  /** 图片源 */
  imageSrc: string;
  /** 选中状态图片源 */
  imageSelectedSrc?: string;
  /** 选中状态图片尺寸 */
  imageSelectedSize?: number;
  /** 选中状态图片是否有边框 */
  imageSelectedBorder?: boolean;
  /** 选中状态图片边框颜色 */
  imageSelectedBorderColor?: string;
  /** 选中状态图片边框圆角 */
  imageSelectedBorderRadius?: number;

  /** 文本颜色 */
  textColor?: string;
  /** 选中状态文本颜色 */
  textSelectedColor?: string;

  /** 文本内容 */
  text: string;
  /** 文本高度 */
  textHeight?: number;
  /** 文本字体大小 */
  textFontSize?: number;
  /** 选中状态文本字体大小 */
  textSelectedFontSize?: number;

  /** 选中状态文本背景圆角 */
  textSelectedBackgroundRadius?: number;
  /** 选中状态文本背景颜色 */
  textSelectedBackgroundColor?: string;
  /** 选中状态文本背景渐变起始颜色 */
  textSelectedBackgroundStartColor?: string;
  /** 选中状态文本背景渐变结束颜色 */
  textSelectedBackgroundEndColor?: string;
  /** 选中状态文本背景内边距 */
  textSelectedBackgroundPadding?: number;
}

/**
 * React Native版本的Diamond组件属性
 */
export interface RNDiamondProps extends BaseDiamondProps {
  /** 容器样式 */
  style?: StyleProp<ViewStyle>;
  /** 文本样式 */
  textStyle?: StyleProp<TextStyle>;
  /** 图片样式 */
  imageStyle?: StyleProp<ViewStyle>;
}

/**
 * Taro版本的Diamond组件属性
 */
export interface TaroDiamondProps extends BaseDiamondProps {
  /** 容器样式 */
  style?: React.CSSProperties;
  /** 文本样式 */
  textStyle?: React.CSSProperties;
  /** 图片样式 */
  imageStyle?: React.CSSProperties;
  /** 自定义类名 */
  className?: string;
}

/**
 * 兼容性导出类型
 */
export type DiamondProps = RNDiamondProps | TaroDiamondProps;
