import { DiamondItem } from './types';

/**
 * 根据数据项数量自动分配布局
 * @param items 数据项
 * @param forceRows 强制指定行数，如果提供则使用指定行数
 * @returns 分组后的数据
 */
export const splitItemsByRows = (items: DiamondItem[], forceRows?: 1 | 2): DiamondItem[][] => {
  if (items.length === 0) {
    return [[]];
  }
  
  // 如果强制指定了行数，则按照指定行数处理
  if (forceRows === 1) {
    return [items];
  }
  
  if (forceRows === 2) {
    const middleIndex = Math.ceil(items.length / 2);
    return [
      items.slice(0, middleIndex),
      items.slice(middleIndex)
    ];
  }
  
  // 否则根据数量自动调整布局
  const count = items.length;
  
  // 5个及以下项目：单行排列
  if (count <= 5) {
    return [items];
  }
  
  // 6-9个项目：单行排列
  if (count >= 6 && count <= 9) {
    return [items];
  }
  
  // 10个项目：5x2两行排列，每行5个
  if (count === 10) {
    return [
      items.slice(0, 5),
      items.slice(5)
    ];
  }
  
  // 11-20个项目：奇数在上，偶数在下
  if (count >= 11 && count <= 20) {
    const firstRow = items.filter((_, index) => index % 2 === 0);  // 奇数位置项(从0开始索引)
    const secondRow = items.filter((_, index) => index % 2 === 1); // 偶数位置项
    return [firstRow, secondRow];
  }
  
  // 其他情况，平均分配到两行
  const middleIndex = Math.ceil(count / 2);
  return [
    items.slice(0, middleIndex),
    items.slice(middleIndex)
  ];
};

/**
 * 根据selectedId获取选中的ID数组
 * @param selectedId 选中的ID字符串，多选时用逗号分隔
 * @param multiple 是否多选
 * @returns 选中的ID数组
 */
export const getSelectedIds = (selectedId?: string, multiple: boolean = false): string[] => {
  if (!selectedId) {
    return [];
  }
  
  return multiple ? selectedId.split(',') : [selectedId];
};

/**
 * 处理项目点击，返回新的selectedId
 * @param currentSelectedIds 当前选中的ID数组
 * @param clickedId 点击的ID
 * @param multiple 是否多选
 * @returns 新的选中ID数组
 */
export const handleItemSelection = (currentSelectedIds: string[], clickedId: string, multiple: boolean = false): string[] => {
  const isSelected = currentSelectedIds.includes(clickedId);
  
  if (multiple) {
    // 多选模式
    return isSelected 
      ? currentSelectedIds.filter(id => id !== clickedId) // 移除选中项
      : [...currentSelectedIds, clickedId]; // 添加选中项
  } else {
    // 单选模式
    return isSelected ? [] : [clickedId];
  }
};
