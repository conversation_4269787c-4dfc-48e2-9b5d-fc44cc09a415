import { resolve } from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';
import libCss from 'vite-plugin-libcss';

// 创建平台特定的依赖重写插件
function createPlatformDependencyPlugin(platform) {
  return {
    name: 'platform-dependency-rewrite',
    resolveId(id, importer) {
      // 调试文件解析
      if (id.includes('screenUtils') && importer) {
        console.log(`🔍 [${platform}] Resolving:`, { id, importer });

        // 对于非 h5 平台，强制重定向到平台特定的文件
        if (platform !== 'h5' && id === '../utils/screenUtils' && importer.includes('core/src')) {
          const fs = require('fs');
          const path = require('path');

          // 构造平台特定的文件路径
          const basePath = path.dirname(importer);
          const platformFile = path.resolve(basePath, `../utils/screenUtils.${platform}.ts`);

          console.log(`🎯 [${platform}] Checking platform file:`, platformFile);

          if (fs.existsSync(platformFile)) {
            console.log(`✅ [${platform}] Redirecting to:`, platformFile);
            return platformFile;
          }
        }
      }
      return null;
    },
    load(id) {
      // 调试文件加载
      if (id.includes('screenUtils')) {
        console.log(`📁 [${platform}] Loading:`, id);
      }
      return null;
    },
    generateBundle(_, bundle) {
      // 只在非 h5 平台处理依赖重写
      if (platform === 'h5') return;

      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];
        if (chunk.type === 'chunk' && chunk.code) {
          // 重写对 @jd/lifeui-core 的导入
          chunk.code = chunk.code.replace(
            /from\s+['"]@jd\/lifeui-core['"]/g,
            `from '@jd/lifeui-core/${platform}'`
          );
          chunk.code = chunk.code.replace(
            /require\(['"]@jd\/lifeui-core['"]\)/g,
            `require('@jd/lifeui-core/${platform}')`
          );
        }
      });
    }
  };
}

// 多端构建配置
export default function createMultiPlatformConfig(packageDir, packageJson) {
  const { name, dependencies, peerDependencies } = packageJson;

  // 外部依赖
  const external = [
    ...Object.keys(dependencies || {}),
    ...Object.keys(peerDependencies || {}),
    'react/jsx-runtime'
  ];

  // 为不同平台创建特定的外部依赖配置
  function createPlatformExternals() {
    // 对于所有平台，我们都内联 @jd/lifeui-core 以确保使用正确的平台特定代码
    return external.filter(dep => dep !== '@jd/lifeui-core');
  }

  // 平台特定的外部依赖
  const platformExternals = {
    rn: [...createPlatformExternals(), 'react-native'],
    h5: [...createPlatformExternals(), 'react-dom', '@tarojs/components', '@tarojs/taro'],
    weapp: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    alipay: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    swan: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    tt: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    qq: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    jd: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro']
  };

  // 平台特定的解析条件
  const platformConditions = {
    rn: ['react-native', 'import', 'module', 'default'],
    h5: ['browser', 'import', 'module', 'default'],
    weapp: ['weapp', 'import', 'module', 'default'],
    alipay: ['alipay', 'import', 'module', 'default'],
    swan: ['swan', 'import', 'module', 'default'],
    tt: ['tt', 'import', 'module', 'default'],
    qq: ['qq', 'import', 'module', 'default'],
    jd: ['jd', 'import', 'module', 'default']
  };

  // 平台特定的文件扩展名
  const platformExtensions = {
    rn: ['.rn.tsx', '.rn.ts', '.rn.jsx', '.rn.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    h5: ['.h5.tsx', '.h5.ts', '.h5.jsx', '.h5.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    weapp: ['.weapp.tsx', '.weapp.ts', '.weapp.jsx', '.weapp.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    alipay: ['.alipay.tsx', '.alipay.ts', '.alipay.jsx', '.alipay.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    swan: ['.swan.tsx', '.swan.ts', '.swan.jsx', '.swan.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    tt: ['.tt.tsx', '.tt.ts', '.tt.jsx', '.tt.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    qq: ['.qq.tsx', '.qq.ts', '.qq.jsx', '.qq.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    jd: ['.jd.tsx', '.jd.ts', '.jd.jsx', '.jd.js', '.tsx', '.ts', '.jsx', '.js', '.json']
  };

  // 创建平台特定的构建配置
  function createPlatformConfig(platform) {
    return defineConfig({
      build: {
        lib: {
          entry: resolve(packageDir, 'src/index.ts'),
          name: name.replace('@jd/', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase()),
          fileName: (format) => `index.${platform}.${format}.js`,
          formats: ['es', 'cjs']
        },
        rollupOptions: {
          external: platformExternals[platform],
          output: {
            globals: {
              react: 'React',
              'react-dom': 'ReactDOM',
              'react-native': 'ReactNative',
              '@tarojs/taro': 'Taro',
              '@tarojs/components': 'TaroComponents'
            }
          }
        },
        outDir: resolve(packageDir, `dist/${platform}`),
        emptyOutDir: false,
        sourcemap: true
      },
      plugins: [
        react(),
        // 只为第一个平台生成类型定义
        ...(platform === 'rn' ? [dts({
          include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.d.ts'],
          outDir: resolve(packageDir, 'dist/types')
        })] : []),
        libCss(),
        // 添加平台特定的依赖重写插件
        createPlatformDependencyPlugin(platform)
      ],
      resolve: {
        extensions: platformExtensions[platform],
        conditions: platformConditions[platform],
        alias: {
          '@jd/lifeui-core': resolve(packageDir, '../core/src')
        }
      },
      define: {
        'process.env.TARO_ENV': JSON.stringify(platform === 'h5' ? 'h5' : platform === 'rn' ? 'rn' : platform)
      }
    });
  }

  return {
    rn: createPlatformConfig('rn'),
    h5: createPlatformConfig('h5'),
    weapp: createPlatformConfig('weapp'),
    alipay: createPlatformConfig('alipay'),
    swan: createPlatformConfig('swan'),
    tt: createPlatformConfig('tt'),
    qq: createPlatformConfig('qq'),
    jd: createPlatformConfig('jd')
  };
}
