#!/bin/bash

# 修复内部依赖配置脚本
# 移除 devDependencies 中的内部包依赖，只保留在 dependencies 中

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🔧 开始修复内部依赖配置...${NC}"

# 需要修复的包列表
packages=(
  "packages/business-groupon"
  "packages/business-instant" 
  "packages/business-takeout"
  "packages/business-travel"
)

for package_dir in "${packages[@]}"; do
  package_json="$package_dir/package.json"
  package_name=$(basename "$package_dir")
  
  echo -e "${YELLOW}📦 修复 $package_name...${NC}"
  
  if [ -f "$package_json" ]; then
    # 使用 node 脚本来精确修改 JSON
    node -e "
      const fs = require('fs');
      const path = '$package_json';
      const pkg = JSON.parse(fs.readFileSync(path, 'utf8'));
      
      // 移除 devDependencies 中的内部包依赖
      if (pkg.devDependencies) {
        Object.keys(pkg.devDependencies).forEach(dep => {
          if (dep.startsWith('@jd/lifeui-')) {
            console.log('  移除 devDependencies 中的:', dep);
            delete pkg.devDependencies[dep];
          }
        });
      }
      
      fs.writeFileSync(path, JSON.stringify(pkg, null, 2) + '\n');
      console.log('  ✅ 修复完成');
    "
  else
    echo -e "${RED}  ❌ 找不到 $package_json${NC}"
  fi
done

echo -e "${GREEN}✅ 内部依赖配置修复完成！${NC}"

# 验证修复结果
echo -e "${GREEN}🔍 验证修复结果...${NC}"
for package_dir in "${packages[@]}"; do
  package_name=$(basename "$package_dir")
  echo -e "${YELLOW}📦 $package_name:${NC}"
  
  echo "  dependencies:"
  grep -A 10 '"dependencies"' "$package_dir/package.json" | grep "@jd/lifeui" | sed 's/^/    /' || echo "    无内部依赖"
  
  echo "  devDependencies:"
  grep -A 10 '"devDependencies"' "$package_dir/package.json" | grep "@jd/lifeui" | sed 's/^/    /' || echo "    无内部依赖 ✅"
done
