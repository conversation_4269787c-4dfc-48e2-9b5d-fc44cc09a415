type ValueListener = (value: number) => void;

export class AnimatedValue {
  private _value: number;
  private listeners: ValueListener[] = [];

  constructor(value: number) {
    this._value = value;
  }

  getValue(): number {
    return this._value;
  }

  setValue(value: number): void {
    if (this._value !== value) {
      this._value = value;
      this.listeners.forEach(listener => listener(value));
    }
  }

  addListener(listener: ValueListener): void {
    this.listeners.push(listener);
  }

  removeListener(): void {
    // 简化实现，清空所有监听器
    this.listeners = [];
  }

  interpolate(config: {
    inputRange: number[];
    outputRange: number[] | string[];
  }): AnimatedValue {
    const { inputRange, outputRange } = config;
    const value = new AnimatedValue(this._value);
    
    this.addListener((val) => {
      if (inputRange.length === 2 && outputRange.length === 2) {
        const [inputMin, inputMax] = inputRange;
        const [outputMin, outputMax] = outputRange as [number, number];
        const ratio = (val - inputMin) / (inputMax - inputMin);
        const outputValue = outputMin + (outputMax - outputMin) * ratio;
        value.setValue(outputValue);
      }
    });
    
    return value;
  }
}

export const Animated = {
  timing: (
    value: AnimatedValue,
    config: { toValue: number; duration: number }
  ) => {
    return {
      start: (callback?: () => void) => {
        const startValue = value.getValue();
        const diff = config.toValue - startValue;
        const startTime = Date.now();
        
        const animate = () => {
          const now = Date.now();
          const progress = Math.min(1, (now - startTime) / config.duration);
          
          // 使用缓动函数（这里使用线性）
          const currentValue = startValue + diff * progress;
          value.setValue(currentValue);
          
          if (progress < 1) {
            requestAnimationFrame(animate);
          } else if (callback) {
            callback();
          }
        };
        
        requestAnimationFrame(animate);
      },
    };
  },
  Value: AnimatedValue,
};
