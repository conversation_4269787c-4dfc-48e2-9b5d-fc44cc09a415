# eslint-plugin-taro-rn-best-practices

## 插件安装与使用说明

### 1. 安装依赖

确保已安装以下依赖（建议作为 devDependencies）：

```bash
npm install --save-dev eslint @typescript-eslint/eslint-plugin @typescript-eslint/parser
```

如需本地开发或调试本插件，可在你的项目根目录下使用 npm link 或直接通过相对路径引用。

### 2. 安装本插件

如果本插件已发布到 npm，可直接安装：
```bash
npm install --save-dev eslint-plugin-taro-rn-best-practices
```

如在本地开发环境下使用（未发布到 npm），可通过相对路径引用：
```js
// .eslintrc.js
module.exports = {
  plugins: [
    'taro-rn-best-practices',
  ],
  extends: [
    // 你的其他 eslint 配置
  ],
  rules: {
    // 启用本插件的规则
    'taro-rn-best-practices/explicit-flex-direction': 'warn',
    'taro-rn-best-practices/no-style-inheritance': 'error',
    'taro-rn-best-practices/no-fixed-position': 'error',
    'taro-rn-best-practices/no-unsupported-style-props': 'error',
    'taro-rn-best-practices/no-unsupported-selectors': 'error',
    'taro-rn-best-practices/no-inline-styles': 'warn',
    'taro-rn-best-practices/require-image-size': 'error',
    'taro-rn-best-practices/text-must-be-in-text-component': 'error',
  },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
};
```

### 3. 运行 ESLint 检查

在项目根目录下运行：
```bash
npx eslint src
```

### 4. 团队协作建议

- 建议将 .eslintrc.js 和本插件一同提交到仓库，确保团队成员规范一致。
- 推荐在 CI 或 pre-commit 钩子中集成 ESLint 检查。
- 如需了解每条规则的详细说明，请查阅本目录下的文档和源码。

---

ESLint plugin for enforcing Taro React Native best practices

## 安装

```bash
npm install eslint-plugin-taro-rn-best-practices --save-dev
```

### 本地开发链接

如果你在本地开发此插件，并希望在你的项目中使用本地版本进行测试，请按照以下步骤操作：

1.  **在插件目录中创建全局链接:**
    首先，导航到 `eslint-plugin-taro-rn-best-practices` 插件的根目录，然后执行以下命令。这会将你的本地插件链接到全局 `node_modules` 目录。
    ```bash
    # 示例: cd /eslint-plugin-taro-rn-best-practices
    cd path/to/eslint-plugin-taro-rn-best-practices
    npm link
    ```

2.  **在你的项目中链接本地插件:**
    然后，导航到你的项目（例如 `uicomponents`）的根目录，并执行以下命令。这将从全局链接在你的项目的 `node_modules` 中创建一个符号链接。
    ```bash
    # 示例: cd /uicomponents
    cd path/to/your-project
    npm link eslint-plugin-taro-rn-best-practices
    ```
    此命令会链接之前创建的全局链接。根据我们之前的操作，使用 `eslint-plugin-taro-rn-best-practices`（不带 `@` 符号和 scope）作为包名是有效的。

完成这些步骤后，你的项目将使用你本地开发的 `eslint-plugin-taro-rn-best-practices` 版本。

## 使用

在你的 `.eslintrc.js` 文件中添加：

```javascript
module.exports = {
  plugins: ['taro-rn-best-practices'],
  extends: ['plugin:taro-rn-best-practices/recommended']
};
```

或者你可以单独配置每个规则：

```javascript
module.exports = {
  plugins: ['taro-rn-best-practices'],
  rules: {
    'taro-rn-best-practices/text-must-be-in-text-component': 'error',
    'taro-rn-best-practices/no-style-inheritance': 'warn',
    'taro-rn-best-practices/require-image-size': 'error',
    'taro-rn-best-practices/no-unsupported-style-props': 'error',
    'taro-rn-best-practices/no-inline-styles': 'warn',
    'taro-rn-best-practices/explicit-flex-direction': 'warn',
    'taro-rn-best-practices/no-fixed-position': 'error',
    'taro-rn-best-practices/no-unsupported-selectors': 'error',
  }
};
```

## 使用 CLI 工具

你也可以使用内置的 CLI 工具来检查你的代码：

```bash
npx taro-rn-lint src/ --fix
```

## 规则

### text-must-be-in-text-component

在 React Native 中，文本必须包裹在 `<Text>` 组件中。

```jsx
// ❌ 错误
<View>
  Hello World
</View>

// ✅ 正确
<View>
  <Text>Hello World</Text>
</View>
```

### no-style-inheritance

React Native 中样式不会继承，每个组件都需要明确设置样式。

```jsx
// ❌ 错误
<Text style={{ color: 'red' }}>
  Parent text
  <Text>Child text</Text> {/* 不会继承父元素的红色 */}
</Text>

// ✅ 正确
<Text style={{ color: 'red' }}>
  Parent text
  <Text style={{ color: 'red' }}>Child text</Text>
</Text>
```

### require-image-size

在 React Native 中，Image 组件必须指定宽高。

```jsx
// ❌ 错误
<Image source={{ uri: 'https://example.com/image.png' }} />

// ✅ 正确
<Image 
  source={{ uri: 'https://example.com/image.png' }}
  style={{ width: 100, height: 100 }}
/>
```

### no-unsupported-style-props

避免使用 React Native 不支持的样式属性。

```jsx
// ❌ 错误
<View style={{ animation: '1s ease', float: 'left' }} />

// ✅ 正确
<View style={{ opacity: 1 }} />
```

### no-inline-styles

避免使用内联样式，使用 StyleSheet.create 创建样式。

```jsx
// ❌ 错误
<View style={{ padding: 10, margin: 10 }} />

// ✅ 正确
const styles = StyleSheet.create({
  container: {
    padding: 10,
    margin: 10,
  },
});

<View style={styles.container} />
```

### explicit-flex-direction

明确指定 flexDirection，因为 React Native 默认为 column，而 Web 默认为 row。

```jsx
// ❌ 错误
<View style={{ display: 'flex' }} />

// ✅ 正确
<View style={{ display: 'flex', flexDirection: 'column' }} />
```

### no-fixed-position

React Native 不支持 position: fixed，应使用 position: absolute。

```jsx
// ❌ 错误
<View style={{ position: 'fixed' }} />

// ✅ 正确
<View style={{ position: 'absolute' }} />
```

### no-unsupported-selectors

React Native 不支持 CSS 选择器。

```jsx
// ❌ 错误
const StyledView = styled.View`
  &:hover {
    background-color: red;
  }
`;

// ✅ 正确
const StyledView = styled.View`
  background-color: blue;
`;
```

## 许可证

MIT