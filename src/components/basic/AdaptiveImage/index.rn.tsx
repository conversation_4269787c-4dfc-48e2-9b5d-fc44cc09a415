import { useState, useEffect } from 'react';
import { Image } from 'react-native';

/**
 * 自适应图片组件
 * @param {Object} props - 组件属性
 * @param {Object} props.source - 图片源
 * @param {Object} props.style - 样式对象
 * @returns {JSX.Element} - 图片组件
 *
 * @example
 * <AdaptiveImage source={{ uri: 'image_url' }} style={{ width: 100 }} />
 *
 * @description
 * 根据传入的样式宽高自动调整图片尺寸，保持宽高比。
 */
const AdaptiveImage = ({ src, source, style }) => {
    // 定义状态变量 imageWidth 和 imageHeight，初始值为 0
    const [imageWidth, setImageWidth] = useState(0);
    // 定义状态变量 imageHeight，初始值为 0
    const [imageHeight, setImageHeight] = useState(0);

    if(!source && src) {
        source = { uri: src };
    }

    // 使用 useEffect 钩子，在组件挂载和更新时执行
    useEffect(() => {
        // 获取图片的原始宽度和高度
        Image.getSize(source.uri, (width, height) => {
            // 计算图片的宽高比
            const aspectRatio = width / height;

            let newWidth, newHeight;

            // 如果样式中指定了宽度但没有指定高度
            if (style.width && !style.height) {
                // 使用指定的宽度，并根据宽高比计算高度
                newWidth = style.width;
                newHeight = style.width / aspectRatio;
                // 如果样式中指定了高度但没有指定宽度
            } else if (!style.width && style.height) {
                // 使用指定的高度，并根据宽高比计算宽度
                newHeight = style.height;
                newWidth = style.height * aspectRatio;
                // 如果样式中同时指定了宽度和高度
            } else if (style.width && style.height) {
                // 使用指定的宽度和高度
                newWidth = style.width;
                newHeight = style.height;
                // 如果样式中既没有指定宽度也没有指定高度
            } else {
                // 使用图片的原始宽度和高度
                newWidth = width;
                newHeight = height;
            }

            // 更新状态变量 imageWidth 和 imageHeight
            setImageWidth(newWidth);
            setImageHeight(newHeight);
        });
        // 依赖项为 source 和 style，当它们变化时重新执行 useEffect
    }, [source, style]);

    // 返回一个 Image 组件，使用计算后的宽度和高度
    return (
        <Image
            source={source}
            style={{ ...style, width: imageWidth, height: imageHeight }}
        />
    );
};

export default AdaptiveImage;
