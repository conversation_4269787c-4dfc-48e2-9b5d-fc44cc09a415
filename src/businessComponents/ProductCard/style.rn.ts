import { StyleSheet, ViewStyle, TextStyle, ImageStyle } from 'react-native';

interface ProductCardStyles {
  container: ViewStyle;
  imageContainer: ViewStyle;
  image: ImageStyle;
  content: ViewStyle;
  title: TextStyle;
  description: TextStyle;
  priceContainer: ViewStyle;
}

const styles = StyleSheet.create<ProductCardStyles>({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
    flexDirection: 'column',
    maxWidth: 300,
    overflow: 'hidden',
    width: '100%',
  },
  imageContainer: {
    height: 'auto',
    overflow: 'hidden',
    position: 'relative',
    width: '100%',
  },
  image: {
    height: 'auto',
    width: '100%',
    resizeMode: 'cover',
  },
  content: {
    flexDirection: 'column',
    padding: 12,
  },
  title: {
    color: '#333333',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  description: {
    color: '#666666',
    fontSize: 14,
    marginBottom: 8,
  },
  priceContainer: {
    marginTop: 8,
  },
});

export default styles; 