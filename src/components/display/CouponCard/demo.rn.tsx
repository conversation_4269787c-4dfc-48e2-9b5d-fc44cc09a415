import React from 'react';
import {ScrollView, StyleSheet } from 'react-native';
import View from '../../basic/View';
import Text from '../../basic/Text';
import { CouponCard } from './index';

const COUPON_BG_IMAGE = 'https://storage.360buyimg.com/wximg/LocStore/couponcard_bg.png';

const CouponCardDemoRN: React.FC = () => {
  return (
    <View style={styles.container}>
      {/* 顶部标题 */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>CouponCard 优惠券卡片</Text>
      </View>

      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        style={{marginTop: 56}} // 添加上边距避免被header遮挡
      >
        <View style={styles.demoItem}>
          <Text style={styles.title}>默认样式</Text>
          <CouponCard 
            title="百亿补贴"
            amount={15} 
            width={72} 
            height={110}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>自定义标题和使用条件</Text>
          <CouponCard 
            title="优惠券优惠券优惠券" 
            amount={50} 
            condition="满100可用满100可用"
            width={72}
            height={110}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>自定义颜色</Text>
          <CouponCard 
            title="满减券"
            amount={20} 
            titleColor="#1890FF"
            amountColor="#1890FF"
            conditionColor="#1890FF"
            width={72}
            height={110}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>自定义字体大小（背景等比放大）</Text>
          <CouponCard 
            title="代金券"
            amount={100} 
            titleFontSize={16}
            amountFontSize={38}
            amountSymbolFontSize={20}
            conditionFontSize={14}
            width={108}
            height={165}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>美元符号</Text>
          <CouponCard 
            title="国际券"
            amount={10} 
            currencySymbol="$"
            width={72}
            height={110}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>

        <View style={styles.demoItem}>
          <Text style={styles.title}>自定义尺寸</Text>
          <View style={styles.flexRow}>
            <CouponCard 
              title="小券"
              amount={5} 
              width={60}
              height={92}
              titleFontSize={10}
              amountFontSize={22}
              amountSymbolFontSize={12}
              conditionFontSize={8}
              backgroundImage={COUPON_BG_IMAGE}
            />
            <View style={{width: 20}} />
            <CouponCard 
              title="中券"
              amount={20} 
              width={90}
              height={138}
              titleFontSize={14}
              amountFontSize={32}
              amountSymbolFontSize={16}
              conditionFontSize={12}
              backgroundImage={COUPON_BG_IMAGE}
            />
            <View style={{width: 20}} />
            <CouponCard 
              title="大券"
              amount={50} 
              width={120}
              height={183}
              titleFontSize={18}
              amountFontSize={42}
              amountSymbolFontSize={22}
              conditionFontSize={16}
              backgroundImage={COUPON_BG_IMAGE}
            />
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    height: 56,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 16,
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  scrollContent: {
    padding: 16,
  },
  demoItem: {
    marginBottom: 20,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333333',
  },
  flexRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-start',
    flexWrap: 'wrap',
  }
});

export default CouponCardDemoRN; 