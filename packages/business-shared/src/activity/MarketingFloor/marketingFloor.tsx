import React, { useState, useEffect, useRef } from 'react';
import { View } from '../../../../core/src/components/basic/View';
import ProductGroup from './components/ProductGroup';
import Header from './components/Header';
import { MarketingFloorProps } from './types';
import { styles } from './style';
import './marketingFloor.scss';

const MarketingFloor: React.FC<MarketingFloorProps> = ({
  subsidyImageUrl,
  subsidyImageWidth,
  subsidyImageHeight,
  showMoreLink,
  moreText,
  onMoreClick,
  products = [],
  autoplayInterval = 5000,
  headerBackgroundColor,
  headerTextColor,
  headerFontSize,
  contentBackgroundColor = '#FFFFFF',
  onCarouselChange,
  className,
  style,
  containerStyle,
  onProductClick,
}) => {
  // 只展示4个一组的完整组
  const validProducts = products.slice(0, Math.floor(products.length / 4) * 4);

  // 如果商品不足4个，不显示
  if (validProducts.length < 4) {
    return null;
  }
  // 计算总页数（每页固定4个商品）
  const groupCount = validProducts.length / 4;

  // 当前显示的页码
  const [activeIndex, setActiveIndex] = useState(0);

  // 下一个要显示的页码
  const [nextIndex, setNextIndex] = useState((activeIndex + 1) % groupCount);

  // 切换标识，用于区分哪个组件当前可见
  const [isFirstGroupVisible, setIsFirstGroupVisible] = useState(true);

  // 动画状态
  const [animationPlaying, setAnimationPlaying] = useState(false);

  // 自动轮播的引用
  const autoplayTimerRef = useRef<number | null>(null);

  // 初始化和清理自动轮播
  useEffect(() => {
    if (autoplayInterval > 0 && groupCount > 1) {
      startAutoplay();
    }

    return () => {
      if (autoplayTimerRef.current) {
        clearInterval(autoplayTimerRef.current);
        autoplayTimerRef.current = null;
      }
    };
  }, [autoplayInterval, groupCount, validProducts]);

  // 开始自动轮播
  const startAutoplay = () => {
    if (autoplayTimerRef.current) {
      clearInterval(autoplayTimerRef.current);
    }

    autoplayTimerRef.current = setInterval(() => {
      if (animationPlaying) return;

      const newNextIndex = (activeIndex + 1) % groupCount;
      setNextIndex(newNextIndex);

      // 标记动画开始
      setAnimationPlaying(true);

      // 模拟动画完成后的状态更新
      setTimeout(() => {
        setActiveIndex(newNextIndex);
        setIsFirstGroupVisible(!isFirstGroupVisible);
        setAnimationPlaying(false);

        if (onCarouselChange) {
          onCarouselChange(newNextIndex);
        }
      }, 500); // 动画持续时间
    }, autoplayInterval);
  };

  // 处理商品点击事件
  const handleProductClick = (product: any, index: number) => {
    if (onProductClick) {
      onProductClick(product, index);
    }
  };

  // 构建类名
  const rootClassName = [
    'marketing-floor',
    className
  ].filter(Boolean).join(' ');

  // 构建样式
  const rootStyle = {
    ...containerStyle,
    ...style,
  };

  // 当前显示组和下一个显示组的数据
  const currentStartIndex = activeIndex * 4;
  const nextStartIndex = nextIndex * 4;

  const currentProducts = validProducts.slice(currentStartIndex, currentStartIndex + 4);
  const nextProducts = validProducts.slice(nextStartIndex, nextStartIndex + 4);

  // 动画样式类名
  const getFirstGroupAnimationClass = () => {
    if (!animationPlaying) {
      return isFirstGroupVisible ? 'marketing-floor__carousel-item--active' : 'marketing-floor__carousel-item--inactive';
    }

    return isFirstGroupVisible
      ? 'marketing-floor__carousel-item--slide-out'
      : 'marketing-floor__carousel-item--slide-in';
  };

  const getSecondGroupAnimationClass = () => {
    if (!animationPlaying) {
      return !isFirstGroupVisible ? 'marketing-floor__carousel-item--active' : 'marketing-floor__carousel-item--inactive';
    }

    return !isFirstGroupVisible
      ? 'marketing-floor__carousel-item--slide-out'
      : 'marketing-floor__carousel-item--slide-in';
  };

  return (
    <View className={rootClassName} style={rootStyle}>
      {/* 头部区域 */}
      <Header
        subsidyImageUrl={subsidyImageUrl}
        subsidyImageWidth={subsidyImageWidth}
        subsidyImageHeight={subsidyImageHeight}
        bubbleImageUrl={bubbleImageUrl}
        showMoreLink={showMoreLink}
        moreText={moreText}
        onMoreClick={onMoreClick}
        backgroundColor={headerBackgroundColor}
        textColor={headerTextColor}
        fontSize={headerFontSize}
        className="marketing-floor__header"
      />

      {/* 内容区域 */}
      <View
        className="marketing-floor__content"
        style={{ backgroundColor: contentBackgroundColor }}
      >
        <View className="marketing-floor__carousel">
          <View className={`marketing-floor__carousel-item ${getFirstGroupAnimationClass()}`}>
            <ProductGroup
              products={currentProducts}
              startIndex={currentStartIndex}
              onProductClick={handleProductClick}
              className="marketing-floor__products"
            />
          </View>

          <View className={`marketing-floor__carousel-item ${getSecondGroupAnimationClass()}`}>
            <ProductGroup
              products={nextProducts}
              startIndex={nextStartIndex}
              onProductClick={handleProductClick}
              className="marketing-floor__products"
            />
          </View>
        </View>
      </View>
    </View>
  );
};

export default MarketingFloor; 