import React from 'react';
import { ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { PriceProps } from '../../components/display/price/types';

export interface ProductCardProps {
  /** 商品图片地址 */
  imageUrl: string;
  /** 商品图片宽度 */
  imageWidth?: number;
  /** 商品图片高度 */
  imageHeight?: number;
  /** 图片圆角 */
  imageRadius?: number;
  /** 商品标题 */
  title: string;
  /** 商品描述 */
  description?: string;
  /** 当前价格 */
  currentPrice: number;
  /** 原价，如果提供则显示原价 */
  originalPrice?: number;
  /** 价格展示类型，默认为折扣样式 */
  priceType?: 'default' | 'discount';
  /** 价格组件的额外props */
  priceProps?: Partial<PriceProps>;
  /** 标题颜色 */
  titleColor?: string;
  /** 描述文字颜色 */
  descriptionColor?: string;
  /** 标题字体大小 */
  titleFontSize?: number;
  /** 描述字体大小 */
  descriptionFontSize?: number;
  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
  /** 自定义根元素内联样式 (Taro/Web) */
  style?: React.CSSProperties;
  /** 自定义根元素样式 (RN) */
  containerStyle?: ViewStyle;
  /** 自定义图片样式 (RN) */
  imageStyle?: ImageStyle;
  /** 卡片点击事件 */
  onClick?: () => void;
} 