// FlipCarousel 组件样式
.flip-carousel {
  &__container {
    width: 100%;
    align-items: center;
    overflow: hidden;
  }

  &__content {
    width: 100%;
    position: relative;
  }

  &__page {
    width: 100%;
    background-color: transparent;
  }

  &__front-page {
    position: absolute;
    top: 0;
    left: 0;
  }

  &__back-page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
  }


  &__page-content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }

  &__pagination {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 12px;
  }

  &__pagination-dot {
    width: 6px;
    height: 6px;
    border-radius: 3px;
    background-color: #CCCCCC;
    margin-left: 3px;
    margin-right: 3px;

    &--active {
      width: 12px;
      background-color: #FF1F3D;
    }
  }
}
