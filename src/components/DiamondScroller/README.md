# DiamondScroller 钻石滚动分类导航组件

基于Diamond组件封装的横向滚动分类导航组件，支持单行或双行展示。

## 功能特点

- 支持横向滚动展示分类项
- 支持配置为单行或双行展示
- 支持单选和多选模式
- 可自定义每个Diamond的宽度和图片尺寸
- 支持点击回调功能

## 使用方法

```jsx
import { DiamondScroller } from '@/pages/components/DiamondScroller';

// 分类数据
const items = [
  {
    id: '1',
    imageSrc: 'https://example.com/image1.png',
    text: '附近美食',
  },
  {
    id: '2',
    imageSrc: 'https://example.com/image2.png',
    text: '休闲玩乐',
  },
  // 更多分类项...
];

// 使用组件
<DiamondScroller
  items={items}
  selectedId="1"
  rows={2} // 1为单行，2为双行
  onItemClick={(item, index, selected) => {
    console.log('点击了', item.text);
  }}
/>
```

## 参数说明

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| items | 数据列表 | DiamondItem[] | [] |
| selectedId | 当前选中项ID | string | - |
| multiple | 是否启用多选 | boolean | false |
| itemWidth | 每个Diamond的宽度 | number | 80 |
| imageSize | 图片尺寸 | number | 60 |
| rows | 行数 - 1或2 | 1 \| 2 | 1 |
| onItemClick | 点击回调 | (item, index, selected) => void | - |
| style | 容器样式 | CSSProperties / StyleProp<ViewStyle> | - |
| diamondStyle | Diamond组件样式 | CSSProperties / StyleProp<ViewStyle> | - |
| className | 自定义类名(仅Taro) | string | - |

## 示例

查看 [demo.tsx](./demo.tsx) 和 [demo.rn.tsx](./demo.rn.tsx) 获取更多使用示例。
