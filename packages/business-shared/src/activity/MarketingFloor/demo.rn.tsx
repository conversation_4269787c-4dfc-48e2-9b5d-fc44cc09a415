import React from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import MarketingFloor from './index';

export default () => {
  // 模拟商品数据
  const products = [
    {
      id: '1',
      shopName: '京东自营',
      productName: '百亿补贴 | 苹果iPhone 14',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      imageSize: 75,
      imageTag: [{ text: "限时", position: "topLeft", backgroundColor: "#FF5B00" }],
      currentPrice: 59,
      originalPrice: 69,
      priceType: 'discount',
      discountBackgroundImageSrc: 'https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png',
      discountBackgroundImageWidth: 78,
      discountBackgroundImageHeight: 19,
      currentPriceColor: '#FFFFFF',
      currentPriceFontSize: 14,
      originalPriceColor: '#FF5252',
      originalPriceFontSize: 11,
      productNameTag: { text: "热卖", backgroundColor: "#FF0036" },
      shopNameStyle: {
        color: '#CA1010',
        fontSize: 11,
        fontWeight: 'bold',
      },
      productNameStyle: {
        color: '#505259',
        fontSize: 11,
      }
    },
    {
      id: '2',
      shopName: '华为官方旗舰店',
      productName: '百亿补贴 | 华为Mate 60 Pro',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      imageSize: 75,
      imageTag: [{ text: "爆款", position: "topRight", backgroundColor: "#FF5B00" }],
      currentPrice: 6999,
      originalPrice: 7699,
      priceType: 'discount',
      discountBackgroundImageSrc: 'https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png',
      discountBackgroundImageWidth: 78,
      discountBackgroundImageHeight: 19,
      currentPriceColor: '#FFFFFF',
      currentPriceFontSize: 14,
      originalPriceColor: '#FF5252',
      originalPriceFontSize: 11,
      shopNameStyle: {
        color: '#CA1010',
        fontSize: 11,
        fontWeight: 'bold',
        textAlign: 'center'
      },
      productNameStyle: {
        color: '#505259',
        fontSize: 11,
        textAlign: 'center'
      }
    },
    {
      id: '3',
      shopName: '华为官方旗舰店',
      productName: '百亿补贴 | 华为Mate 60 Pro',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      imageSize: 75,
      imageTag: [{ text: "爆款", position: "topRight", backgroundColor: "#FF5B00" }],
      currentPrice: 6999,
      originalPrice: 7699,
      priceType: 'discount',
      discountBackgroundImageSrc: 'https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png',
      discountBackgroundImageWidth: 78,
      discountBackgroundImageHeight: 19,
      currentPriceColor: '#FFFFFF',
      currentPriceFontSize: 14,
      originalPriceColor: '#FF5252',
      originalPriceFontSize: 11,
      shopNameStyle: {
        color: '#CA1010',
        fontSize: 11,
        fontWeight: 'bold',
        textAlign: 'center'
      },
      productNameStyle: {
        color: '#505259',
        fontSize: 11,
        textAlign: 'center'
      }
    },
    {
      id: '4',
      shopName: '华为官方旗舰店',
      productName: '百亿补贴 | 华为Mate 60 Pro',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      imageSize: 75,
      imageTag: [{ text: "爆款", position: "topRight", backgroundColor: "#FF5B00" }],
      currentPrice: 6999,
      originalPrice: 7699,
      priceType: 'discount',
      discountBackgroundImageSrc: 'https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png',
      discountBackgroundImageWidth: 78,
      discountBackgroundImageHeight: 19,
      currentPriceColor: '#FFFFFF',
      currentPriceFontSize: 14,
      originalPriceColor: '#FF5252',
      originalPriceFontSize: 11,
      shopNameStyle: {
        color: '#CA1010',
        fontSize: 11,
        fontWeight: 'bold',
        textAlign: 'center'
      },
      productNameStyle: {
        color: '#505259',
        fontSize: 11,
        textAlign: 'center'
      }
    },

    {
      id: '5',
      shopName: '京东自营',
      productName: '百亿补贴 | 苹果iPhone 14',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      imageSize: 75,
      imageTag: [{ text: "限时", position: "topLeft", backgroundColor: "#FF5B00" }],
      currentPrice: 59,
      originalPrice: 69,
      priceType: 'discount',
      discountBackgroundImageSrc: 'https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png',
      discountBackgroundImageWidth: 78,
      discountBackgroundImageHeight: 19,
      currentPriceColor: '#FFFFFF',
      currentPriceFontSize: 14,
      originalPriceColor: '#FF5252',
      originalPriceFontSize: 11,
      productNameTag: { text: "热卖", backgroundColor: "#FF0036" },
      shopNameStyle: {
        color: '#CA1010',
        fontSize: 11,
        fontWeight: 'bold',
      },
      productNameStyle: {
        color: '#505259',
        fontSize: 11,
      }
    },
    {
      id: '6',
      shopName: '华为官方旗舰店',
      productName: '百亿补贴 | 华为Mate 60 Pro',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      imageSize: 75,
      imageTag: [{ text: "爆款", position: "topRight", backgroundColor: "#FF5B00" }],
      currentPrice: 6999,
      originalPrice: 7699,
      priceType: 'discount',
      discountBackgroundImageSrc: 'https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png',
      discountBackgroundImageWidth: 78,
      discountBackgroundImageHeight: 19,
      currentPriceColor: '#FFFFFF',
      currentPriceFontSize: 14,
      originalPriceColor: '#FF5252',
      originalPriceFontSize: 11,
      shopNameStyle: {
        color: '#CA1010',
        fontSize: 11,
        fontWeight: 'bold',
        textAlign: 'center'
      },
      productNameStyle: {
        color: '#505259',
        fontSize: 11,
        textAlign: 'center'
      }
    },
    {
      id: '7',
      shopName: '华为官方旗舰店',
      productName: '百亿补贴 | 华为Mate 60 Pro',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      imageSize: 75,
      imageTag: [{ text: "爆款", position: "topRight", backgroundColor: "#FF5B00" }],
      currentPrice: 6999,
      originalPrice: 7699,
      priceType: 'discount',
      discountBackgroundImageSrc: 'https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png',
      discountBackgroundImageWidth: 78,
      discountBackgroundImageHeight: 19,
      currentPriceColor: '#FFFFFF',
      currentPriceFontSize: 14,
      originalPriceColor: '#FF5252',
      originalPriceFontSize: 11,
      shopNameStyle: {
        color: '#CA1010',
        fontSize: 11,
        fontWeight: 'bold',
        textAlign: 'center'
      },
      productNameStyle: {
        color: '#505259',
        fontSize: 11,
        textAlign: 'center'
      }
    },
    {
      id: '8',
      shopName: '华为官方旗舰店',
      productName: '百亿补贴 | 华为Mate 60 Pro',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      imageSize: 75,
      imageTag: [{ text: "爆款", position: "topRight", backgroundColor: "#FF5B00" }],
      currentPrice: 6999,
      originalPrice: 7699,
      priceType: 'discount',
      discountBackgroundImageSrc: 'https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png',
      discountBackgroundImageWidth: 78,
      discountBackgroundImageHeight: 19,
      currentPriceColor: '#FFFFFF',
      currentPriceFontSize: 14,
      originalPriceColor: '#FF5252',
      originalPriceFontSize: 11,
      shopNameStyle: {
        color: '#CA1010',
        fontSize: 11,
        fontWeight: 'bold',
        textAlign: 'center'
      },
      productNameStyle: {
        color: '#505259',
        fontSize: 11,
        textAlign: 'center'
      }
    },

  ];

  const handleProductClick = (product: any, index: number) => {
    console.log('点击了商品:', product.title, '索引:', index);
  };

  const handleMoreClick = () => {
    console.log('点击了查看更多');
  };

  console.log(products, '==========>products')

  return (
    <ScrollView style={styles.container}>
      <Text style={styles.title}>营销楼层自动轮播示例</Text>

      <MarketingFloor
        subsidyImageUrl="https://img10.360buyimg.com/img/jfs/t1/309324/24/2806/6644/682d8404F950e64e5/32462ec51895847a.png"
        officialSubsidyText="官方补贴"
        bubbleImageUrl="https://img.example.com/bubble.png"
        moreText="查看更多 >"
        products={products}
        autoplayInterval={3000}
        onProductClick={handleProductClick}
        onMoreClick={handleMoreClick}
        onCarouselChange={(index) => console.log('轮播切换到:', index)}
      />
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    backgroundColor: '#F5F5F5',
    marginTop: 100,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 20,
    color: '#333333',
  }
}); 