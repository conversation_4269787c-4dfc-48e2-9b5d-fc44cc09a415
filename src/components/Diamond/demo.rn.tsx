import React, { useState } from 'react';
import { ScrollView } from 'react-native';
import View from '@DJUIComponents/atoms/DJView';
import Text from '@DJUIComponents/atoms/DJText';
import { Diamond } from './index';

const DiamondDemo: React.FC = () => {
  const [selected, setSelected] = useState<number>(0);
  
  const handleSelect = (index: number) => {
    setSelected(index);
  };
  
  return (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 20 }}>
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>u57fau7840u94bbu77f3u9009u9879</Text>
          <View style={{ flexDirection: 'row' }}>
            <View onTouchEnd={() => handleSelect(0)} style={{ marginRight: 16 }}>
              <Diamond 
                width={80}
                imageSize={60}
                imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
                text="u94bbu77f31"
                selected={selected === 0}
              />
            </View>
            <View onTouchEnd={() => handleSelect(1)} style={{ marginRight: 16 }}>
              <Diamond 
                width={80}
                imageSize={60}
                imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
                text="u94bbu77f32"
                selected={selected === 1}
              />
            </View>
          </View>
        </View>
        
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>u5e26u6e10u53d8u80ccu666fu7684u94bbu77f3u9009u9879</Text>
          <View style={{ flexDirection: 'row' }}>
            <View onTouchEnd={() => handleSelect(2)} style={{ marginRight: 16 }}>
              <Diamond 
                width={80}
                imageSize={60}
                imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
                text="u5e26u6e10u53d81"
                selected={selected === 2}
                textSelectedBackgroundStartColor="#FF0000"
                textSelectedBackgroundEndColor="#FF9500"
                imageSelectedBorder={true}
                imageSelectedBorderColor="#FF0000"
              />
            </View>
            <View onTouchEnd={() => handleSelect(3)} style={{ marginRight: 16 }}>
              <Diamond 
                width={80}
                imageSize={60}
                imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
                text="u5e26u6e10u53d82"
                selected={selected === 3}
                textSelectedBackgroundColor="#4285F4"
                imageSelectedBorderRadius={8}
              />
            </View>
          </View>
        </View>
        
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>u7981u7528u72b6u6001u7684u94bbu77f3u9009u9879</Text>
          <Diamond 
            width={80}
            imageSize={60}
            imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
            text="u7981u7528u9009u9879"
            disabled={true}
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default DiamondDemo;
