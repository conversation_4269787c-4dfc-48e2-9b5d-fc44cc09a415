import { Image as TaroImage } from "@tarojs/components";
import { forwardRef, memo } from 'react';
import { useScreen } from '../../../hooks/useScreen';

/**
 * 自定义 Image 组件
 * 扩展了 Taro 的 Image 组件，自动处理style中的尺寸缩放
 */
const Image = forwardRef<any, any>((props, ref) => {
  const { style, ...restProps } = props;
  const { scaleStyle } = useScreen();
  
  // 对style应用自动缩放（只处理对象类型的style）
  const scaledStyle = style && typeof style === 'object' && !Array.isArray(style) 
    ? scaleStyle(style as Record<string, any>) 
    : style;
  
  return <TaroImage {...restProps as any} style={scaledStyle as any} ref={ref} />;
});

export default memo(Image);