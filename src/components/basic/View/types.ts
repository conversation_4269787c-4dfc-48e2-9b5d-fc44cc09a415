import { ViewProps as TaroViewProps } from "@tarojs/components";
import { CSSProperties, ReactNode } from 'react';

/**
 * 宽松的 CSS 属性类型定义
 */
export interface LooseCSSProperties {
  [key: string]: any;
}

/**
 * 样式属性值类型
 */
export type StylePropValue = string | LooseCSSProperties | undefined | CSSProperties;

/**
 * 混合样式类型，支持单个样式或样式数组
 */
export type mixinStyle = StylePropValue | Array<StylePropValue>;

/**
 * View 组件属性
 */
export interface ViewProps extends Omit<TaroViewProps, 'style'> {
  /** 子元素 */
  children?: ReactNode;
  /** 自定义样式 */
  style?: mixinStyle;
  /** 自定义类名 */
  className?: string;
}
