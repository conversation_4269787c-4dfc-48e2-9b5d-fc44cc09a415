import React from 'react';
import View from '../../../../components/basic/View';
import Text from '../../../../components/basic/Text';
import { HeaderProps } from './types';
import './styles.scss';
import { CustomTagList } from '@/uicomponents/src/components/CustomTagList';
import { TagType } from '../../../../components/CustomTag/types';

/**
 * MarketingFloor头部组件
 * 通用实现，同时支持Taro和RN平台
 */
const Header: React.FC<HeaderProps> = ({
  subsidyImageUrl,
  subsidyImageWidth = 120,
  subsidyImageHeight = 36,
  showMoreLink = true,
  moreText = '查看更多 >',
  onMoreClick,
  textColor = '#333333',
  fontSize = 14,
  className = '',
}) => {

  // 处理"查看更多"的点击事件
  const handleMoreClick = () => {
    console.log('handleMoreClickhandleMoreClick')
    if (onMoreClick) {
      onMoreClick();
    }
  };

  return (
    <View
      className={`marketing-floor-header ${className}`}
    >
      {/* 标题 */}
      <CustomTagList
        tags={[
          {
            tagImage: subsidyImageUrl,
            type: TagType.IMAGE,
            tagImageStyle: {
              width: subsidyImageWidth,
              height: subsidyImageHeight
            }
          }
        ]} />

      {/* 更多 */}
      <View onClick={handleMoreClick}>
        {showMoreLink && (
          <Text
            style={{
              color: textColor,
              fontSize,
            }}
          >
            {moreText}
          </Text>
        )}
      </View>
    </View>
  );
};

export default Header; 