# DiamondScroller 钻石滚动分类导航组件

## 组件概述

`DiamondScroller` 是一个基于Diamond组件封装的横向滚动分类导航组件。它支持配置单行或双行展示，适合作为App中的分类导航模块使用。

## 设计目的

在移动应用中，分类导航是常见的UI模式，用户可以通过横向滚动浏览不同的分类选项。该组件解决了以下问题：

1. 提供分类导航的横向滚动展示
2. 支持单行和双行两种布局方式
3. 支持单选和多选模式
4. 提供统一的点击回调和选中状态管理

## 使用场景

- 电商应用中的商品分类导航
- 服务类应用的服务分类导航
- 内容平台的内容分类导航

## 功能特性

- **横向滚动**：通过滚动展示更多分类项
- **行数配置**：支持单行或双行展示，适应不同的UI需求
- **选择模式**：支持单选和多选两种模式
- **自定义样式**：可自定义每个Diamond的宽度、图片尺寸等属性
- **点击回调**：提供点击事件回调，方便处理用户交互

## 使用示例

```jsx
import { DiamondScroller } from '@/pages/components/DiamondScroller';

// 分类数据
const categoryItems = [
  {
    id: '1',
    imageSrc: 'https://example.com/image1.png',
    text: '附近美食',
  },
  {
    id: '2',
    imageSrc: 'https://example.com/image2.png',
    text: '休闲玩乐',
  },
  // 更多分类...
];

// 单行模式
<DiamondScroller
  items={categoryItems}
  selectedId="1"
  rows={1}
  onItemClick={(item, index, selected) => {
    console.log('点击了分类:', item.text);
  }}
/>

// 双行模式
<DiamondScroller
  items={categoryItems}
  selectedId="1"
  rows={2}
  onItemClick={(item, index, selected) => {
    console.log('点击了分类:', item.text);
  }}
/>
```

## API参考

### DiamondScroller Props

| 属性 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| items | 数据列表 | DiamondItem[] | 必填 |
| selectedId | 当前选中项ID | string | - |
| multiple | 是否启用多选 | boolean | false |
| itemWidth | 每个Diamond的宽度 | number | 80 |
| imageSize | 图片尺寸 | number | 60 |
| rows | 行数 - 1或2 | 1 \| 2 | 1 |
| onItemClick | 点击回调 | (item, index, selected) => void | - |
| style | 容器样式 | CSSProperties / StyleProp<ViewStyle> | - |
| diamondStyle | Diamond组件样式 | CSSProperties / StyleProp<ViewStyle> | - |
| className | 自定义类名(仅Taro) | string | - |

### DiamondItem 类型

```ts
interface DiamondItem {
  id: string;              // 唯一标识符
  imageSrc: string;        // 图片源
  text: string;            // 文本内容
  disabled?: boolean;      // 是否禁用
  imageSelectedSrc?: string; // 选中状态图片源
  // ... 其他Diamond组件支持的属性
}
```

## 注意事项

1. 确保每个item都有唯一的id属性
2. 根据UI需求选择合适的rows值(1或2)
3. 如需多选功能，设置multiple为true
4. 可以通过style和diamondStyle自定义组件外观
