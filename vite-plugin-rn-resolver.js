export default function rnResolverPlugin() {
  const rnExtensions = ['.rn.tsx', '.rn.ts', '.rn.jsx', '.rn.js'];
  
  return {
    name: 'vite-plugin-rn-resolver',
    resolveId(id, importer) {
      // 如果已经有扩展名，跳过
      if (/\.(js|jsx|ts|tsx|rn\.js|rn\.jsx|rn\.ts|rn\.tsx)$/.test(id)) {
        return null;
      }
      
      // 尝试解析 .rn.* 版本
      for (const ext of rnExtensions) {
        try {
          const rnPath = `${id}${ext}`;
          const resolved = this.resolve(rnPath, importer, { skipSelf: true });
          if (resolved) {
            return resolved;
          }
        } catch (e) {
          // 忽略错误，继续尝试下一个扩展名
        }
      }
      
      return null;
    }
  };
}