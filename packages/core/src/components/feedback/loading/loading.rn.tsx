import { View } from '../../basic/View';
import { Text } from '../../basic/Text';
import { JDLoadingView } from '@jdreact/jdreact-core-lib';

const Loading = (props: any) => {
  const { text = "加载中", isLoading = true } = props || {};

  return (
    <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'center' }}>
      <JDLoadingView />
      {text && <Text style={{ color: '#999999', fontSize: 14, marginLeft: 5 }}>{text}</Text>}
    </View>
  );
};

export default Loading; 