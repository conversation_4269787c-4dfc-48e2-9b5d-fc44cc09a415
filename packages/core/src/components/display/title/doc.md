# Title 标题组件

用于展示不同大小的标题，支持左侧标签图片、右侧文本的展示形式，并可限制文本显示行数。

## 组件特性

- 支持自定义标题字号
- 支持左侧标签图片展示
- 支持标签图片点击事件
- 支持标签图片换行模式
- 支持设置标题显示行数
- 支持多种文本截断方式
- 跨平台兼容（Web、React Native）

## 使用方法

```tsx
import { Title } from '@uicomponent/display';

// 基础用法
<Title fontSize={32}>大标题</Title>

// 带标签的标题
<Title 
  fontSize={24}
  tags={[
    { src: 'https://example.com/tag1.png', width: 24, height: 24 },
    { src: 'https://example.com/tag2.png', width: 24, height: 24 }
  ]}
>
  带标签的标题
</Title>

// 标签换行模式
<Title 
  fontSize={20}
  tags={tags}
  wrap
  tagContainerWidth={80}
>
  标签图片换行的标题
</Title>

// 限制显示行数
<Title
  fontSize={20}
  lines={2}
  ellipsis="ellipsis"
>
  这是一个很长的标题，将会限制为两行显示，超出部分会显示省略号...
</Title>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| fontSize | 标题字号大小 | number | 32 |
| tags | 标签图片配置 | TagImage[] | [] |
| tagGap | 标签之间的间距 | number | 4 |
| tagTitleGap | 标签与标题之间的间距 | number | 4 |
| wrap | 是否启用标签换行模式 | boolean | false |
| tagContainerWidth | 标签容器宽度（仅在 wrap 模式下生效） | number | 120 |
| lines | 标题显示的行数 | number | - |
| ellipsis | 文本截断方式 | 'clip' \| 'ellipsis' \| 'fade' | 'ellipsis' |
| textAlign | 文本对齐方式 | 'left' \| 'center' \| 'right' | 'left' |

### TagImage 类型

```ts
interface TagImage {
  src: string;           // 图片地址
  width?: number;        // 图片宽度
  height?: number;       // 图片高度
  borderRadius?: number; // 图片圆角
  onClick?: () => void;  // 点击事件
}
```

## 平台差异

### Web 平台
- 使用 CSS 类名控制样式
- 使用 flex 布局实现标签和文本的布局
- 文本截断支持三种方式：clip（直接截断）、ellipsis（显示省略号）、fade（渐变消失）

### React Native 平台
- 使用 StyleSheet 控制样式
- 使用 flex 布局实现标签和文本的布局
- 文本截断主要依赖于 ellipsis 属性

## 更新日志

### 3.0.0
- 去掉传入props功能
- 添加标题行数限制功能
- 支持多种文本截断方式
- 优化文本对齐方式设置

### 2.0.0
- 简化组件 API
- 移除 type 和 align 属性
- 将 level 改为 fontSize，支持自定义字号
- 优化标签图片展示逻辑
- 统一左对齐布局 