import { StyleSheet, ViewStyle, TextStyle, ImageStyle } from 'react-native';

interface PriceStyles {
  price: ViewStyle;
  priceDefault: ViewStyle;
  priceDiscount: ViewStyle;
  discountBg: ViewStyle;
  currentContainer: ViewStyle;
  currentContainerDefault: ViewStyle;
  lightningIcon: ImageStyle;
  originalContainer: ViewStyle;
  originalContainerDefault: ViewStyle;
  originalContainerDiscount: ViewStyle;
}

const styles = StyleSheet.create<PriceStyles>({
  price: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  priceDefault: {
    // 默认价格容器样式
  },
  priceDiscount: {
    backgroundColor: '#ff4d4f',
    borderRadius: 4,
    padding: 6,
    position: 'relative',
  },
  discountBg: {
    backgroundColor: '#ffccc7',
    bottom: 0,
    position: 'absolute',
    right: 0,
    top: 0,
    width: '50%',
    zIndex: 0,
  },
  currentContainer: {
    position: 'relative',
    zIndex: 1,
  },
  currentContainerDefault: {
    marginRight: 8,
  },
  lightningIcon: {
    marginLeft: 8,
    marginRight: 8,
    position: 'relative',
    zIndex: 2,
  },
  originalContainer: {
    position: 'relative',
    zIndex: 1,
  },
  originalContainerDefault: {
    marginLeft: 4,
  },
  originalContainerDiscount: {
    marginLeft: 8,
  },
});

export default styles; 