---
description: 
globs: 
alwaysApply: false
---
# UI组件开发规范 (MDC)

## 目录结构

### 1. 根目录结构
```
src/
├── components/               # 组件目录
│   ├── basic/               # 基础组件
│   │   ├── button/          # 按钮组件
│   │   ├── icon/            # 图标组件
│   │   └── ...
│   ├── form/                # 表单组件
│   ├── feedback/            # 反馈组件
│   ├── navigation/          # 导航组件
│   └── display/             # 展示组件
├── businessComponents/       # 业务组件目录
│   ├── StoreList/           # 门店列表组件
│   │   ├── OneLineOneStoreList/  # 门店一行一
│   │   ├── OneLineTwoStoreList/  # 门店一行二
│   │   └── ...
│   ├── ProductList/         # 商品列表组件
│   └── ...                  
├── styles/                  # 样式目录
│   ├── theme/               # 主题
│   │   ├── variables.ts     # 共享变量
│   │   ├── theme.rn.ts      # RN主题
│   │   └── theme.scss       # Taro主题
│   ├── utils/               # 样式处理工具
│   │   ├── rn.ts            # RN样式工具
│   │   └── taro.scss        # Taro样式工具
│   └── index.ts             # 样式入口
├── utils/                   # 工具函数
│   ├── platform.ts          # 平台检测
│   ├── style.ts             # 样式处理
│   └── ...
├── hooks/                   # 自定义Hooks
│   ├── useTheme.ts          # 主题Hook
│   ├── usePlatform.ts       # 平台Hook
│   └── ...
├── types/                   # 类型定义
│   ├── components.ts        # 组件类型
│   ├── theme.ts             # 主题类型
│   └── ...
├── index.rn.ts              # RN入口
└── index.ts                 # Taro入口
```

### 2. 单个组件目录结构
```
src/components/button/        # 组件目录
├── button.scss              # 组件样式文件(taro版本)
├── style.rn.ts              # 组件样式文件(RN版本)
├── button.tsx               # 组件主体实现(taro版本)
├── button.rn.tsx            # 组件RN实现(RN版本)
├── demo.tsx                 # taro示例代码
├── demo.rn.tsx              # RN示例代码
├── doc.md                   # 文档
├── index.ts                 # 组件导出文件
├── type.ts                 # 组件类型
└── __tests__/              # 测试目录
    └── button.spec.tsx      # 组件测试文件
```

### 3. 组件分类说明

#### 3.1 基础组件 (basic/)
- button: 按钮组件
- icon: 图标组件
- text: 文本组件
- image: 图片组件
- layout: 布局组件

#### 3.2 表单组件 (form/)
- input: 输入框
- checkbox: 复选框
- radio: 单选框
- select: 选择器
- form: 表单容器

#### 3.3 反馈组件 (feedback/)
- modal: 模态框
- toast: 轻提示
- loading: 加载中
- message: 消息提示
- progress: 进度条

#### 3.4 导航组件 (navigation/)
- tabs: 标签页
- menu: 菜单
- breadcrumb: 面包屑
- pagination: 分页
- steps: 步骤条

#### 3.5 展示组件 (display/)
- card: 卡片
- list: 列表
- table: 表格
- tree: 树形控件
- calendar: 日历

#### 3.6 业务组件 (businessComponents/)
- StoreList: 门店列表相关组件
  - OneLineOneStoreList: 一行一个门店
  - OneLineTwoStoreList: 一行两个门店
- ProductList: 商品列表相关组件
- OrderList: 订单列表相关组件
- UserInfo: 用户信息相关组件

## 组件开发模板

### 1. 组件主文件 (button.tsx)
```typescript
import React from 'react';
import { View, Text } from '../../basic';
import { ButtonProps } from '../../types/components';
import { useTheme } from '../../hooks/useTheme';
import './button.scss';
import { styles as rnStyles } from './style.rn';

export const Button: React.FC<ButtonProps> = ({
  children,
  type = 'primary',
  size = 'medium',
  disabled = false,
  loading = false,
  onClick,
  className = '',
  style,
  ...props
}) => {
  const theme = useTheme();

  const containerClassName = [
    'button',
    `button--${type}`,
    `button--${size}`,
    disabled ? 'button--disabled' : '',
    loading ? 'button--loading' : '',
    className
  ].filter(Boolean).join(' ');

  const containerStyle = [
    rnStyles.button,
    rnStyles[`button--${type}`],
    rnStyles[`button--${size}`],
    disabled && rnStyles['button--disabled'],
    loading && rnStyles['button--loading'],
    style
  ];

  const textStyle = [
    rnStyles.text,
    rnStyles[`text--${type}`],
    rnStyles[`text--${size}`],
    disabled && rnStyles['text--disabled']
  ];

  return (
    <View 
      className={containerClassName}
      style={containerStyle}
      onClick={disabled || loading ? undefined : onClick}
      {...props}
    >
      {loading && (
        <View className="button__loading" style={rnStyles.loading}>
          {/* 加载图标组件 */}
        </View>
      )}
      <Text className="button__text" style={textStyle}>
        {children}
      </Text>
    </View>
  );
};

export default Button;
```

### 2. RN版本实现 (button.rn.tsx)
```typescript
// 仅在需要特殊处理 RN 平台逻辑时使用
// 例如：需要使用 RN 特有的组件或 API
import React from 'react';
import { TouchableOpacity } from 'react-native';
import { Button } from './button';
import { ButtonProps } from '../../types/components';

export const ButtonRN: React.FC<ButtonProps> = (props) => {
  // 这里可以添加 RN 平台特有的逻辑
  // 例如：使用 TouchableOpacity 替代 View
  // 或者处理 RN 特有的手势事件等
  return <Button {...props} />;
};

export default ButtonRN;
```

### 3. 样式文件 (button.scss)
```scss
.button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  
  &__loading {
    margin-right: 8px;
  }
  
  &__text {
    font-size: 14px;
    line-height: 1.5;
  }
  
  &--primary {
    background-color: var(--color-primary);
    color: #fff;
  }
  
  &--secondary {
    background-color: var(--color-secondary);
    color: #fff;
  }
  
  &--disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &--loading {
    cursor: wait;
  }
}
```

### 4. RN 样式文件 (style.rn.ts)
```typescript
import { StyleSheet } from 'react-native';

export const styles = StyleSheet.create({
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 8,
    borderRadius: 4,
  },
  text: {
    fontSize: 14,
    lineHeight: 21,
  },
  loading: {
    marginRight: 8,
  },
  'button--primary': {
    backgroundColor: '#1677ff',
  },
  'button--secondary': {
    backgroundColor: '#666666',
  },
  'button--disabled': {
    opacity: 0.5,
  },
  'text--primary': {
    color: '#ffffff',
  },
  'text--secondary': {
    color: '#ffffff',
  },
  'text--disabled': {
    color: '#999999',
  },
});
```

### 5. 组件导出文件 (index.ts)
```typescript
import { Platform } from 'react-native';
import Button from './button';
import ButtonRN from './button.rn';

// 根据平台导出不同实现
export default Platform.OS === 'web' ? Button : ButtonRN;
export type { ButtonProps } from '../../types/components';
```

### 6. 示例代码 (demo.tsx)
```typescript
import React from 'react';
import { View } from '../../basic';
import Button from './index';

export default () => (
  <View className="demo">
    <Button type="primary">主要按钮</Button>
    <Button type="secondary" disabled>禁用按钮</Button>
    <Button type="primary" loading>加载中</Button>
  </View>
);
```

### 7. RN示例代码 (demo.rn.tsx)
```typescript
import React from 'react';
import { View } from 'react-native';
import Button from './index';

export default () => (
  <View style={{ padding: 16 }}>
    <Button type="primary">主要按钮</Button>
    <Button type="secondary" disabled>禁用按钮</Button>
    <Button type="primary" loading>加载中</Button>
  </View>
);
```

### 8. 文档模板 (doc.md)
```markdown
# Button 按钮

## 组件描述
按钮用于开始一个即时操作。

## 使用示例
```tsx
import { Button } from '@/components';

export default () => (
  <Button type="primary" onClick={() => console.log('clicked')}>
    点击按钮
  </Button>
);
```

## Props
| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| type | 'primary' \| 'secondary' \| 'text' | 否 | 'primary' | 按钮类型 |
| size | 'small' \| 'medium' \| 'large' | 否 | 'medium' | 按钮尺寸 |
| disabled | boolean | 否 | false | 是否禁用 |
| loading | boolean | 否 | false | 是否加载中 |
| onClick | () => void | 否 | - | 点击事件处理函数 |

## 平台差异
- RN 平台使用 TouchableOpacity 实现
- Taro 平台使用 View 实现

## 注意事项
- 按钮文字建议不超过 4 个字
- 主要按钮一个页面最多出现一次
- 禁用状态需要给出明确的提示

## 更新日志
- v1.0.0 (2024-xx-xx): 首次发布
```

## 开发规范

### 1. 代码规范
- 使用 TypeScript 开发
- 遵循 ESLint 和 StyleLint 规则
- 使用 Prettier 格式化代码
- 组件必须包含完整的类型定义
- 必须编写单元测试
- 必须包含完整的文档

### 2. 性能规范
- 合理使用 React.memo
- 使用 useMemo 和 useCallback 优化性能
- 避免不必要的重渲染
- 图片资源必须优化
- 按需加载组件

### 3. 跨平台规范
- 使用 Taro 提供的跨平台组件
- 样式适配不同平台
- 使用条件编译处理平台差异
- 确保在 RN 和 H5 平台都能正常运行

### 4. 版本控制
- 遵循语义化版本
- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

### 5. 提交规范
提交信息格式：`<type>(<scope>): <subject>`

type 类型：
- feat: 新功能
- fix: 修复
- docs: 文档
- style: 格式
- refactor: 重构
- test: 测试
- chore: 构建过程或辅助工具的变动

### 6. 发布规范
- 发布前进行完整测试
- 更新版本号
- 更新 CHANGELOG.md
- 编写发布说明
- 确保文档完整性

## 组件分类

### 基础组件 (basic/)
- Button 按钮
- Input 输入框
- Icon 图标
- Text 文本
- Image 图片
- Layout 布局
- Form 表单
- Modal 弹窗
- Toast 提示
- Loading 加载

### 业务组件 (business/)
- ShopLogo 店铺logo
- ProductCard 商品卡片
- OrderItem 订单项
- UserInfo 用户信息
- AddressCard 地址卡片

## 组件开发流程

1. 需求分析
   - 明确组件用途
   - 确定使用场景
   - 定义组件接口

2. 开发准备
   - 创建组件目录
   - 初始化文件结构
   - 编写类型定义

3. 组件开发
   - 实现核心功能
   - 编写样式
   - 添加单元测试
   - 编写文档

4. 代码审查
   - 代码规范检查
   - 性能优化检查
   - 跨平台兼容性检查
   - 文档完整性检查

5. 发布准备
   - 版本号更新
   - 更新日志编写
   - 文档完善
   - 测试用例补充

6. 发布
   - 提交代码
   - 合并主分支
   - 发布新版本
   - 通知相关团队

代码规范：
采用严格的BEM格式（只允许 &__ 和 &--，且最多三层结构），并且用嵌套的方式书写（即保留Sass嵌套，但只用BEM结构，不用选择器或多层嵌套）。
只允许 .coupon-card { ... &__xxx { ... &--xxx { ... } } }
不允许出现如 ::before、::after 这种伪类
不允许出现超过三层的嵌套
不允许出现非BEM的选择器

