import { View as TaroView } from "@tarojs/components";
import { ViewProps } from './type';
import { forwardRef, memo } from 'react';
import { useScreen } from '../../../hooks/useScreen';

/**
 * 自定义 View 组件
 * 扩展了 Taro 的 View 组件，添加了对样式数组的处理
 * 使用更宽松的样式类型定义，以兼容 React Native 和 Web 的样式属性
 * 自动处理style中的尺寸缩放
 */
const View = forwardRef<typeof TaroView, ViewProps>((props, ref) => {
  const { style, ...restProps } = props;
  const { scaleStyle } = useScreen();
  
  // 对style应用自动缩放（只处理对象类型的style）
  const scaledStyle = style && typeof style === 'object' && !Array.isArray(style) 
    ? scaleStyle(style as Record<string, any>) 
    : style;
  
  return <TaroView {...restProps} style={scaledStyle as any} ref={ref} />;
});

export default memo(View);