import React, { memo } from 'react';
import { View } from '../../basic/View';
import { Text } from '../../basic/Text';
import { TitleProps, TitleLevel } from './types';
import './title.scss';
import { CustomTag } from '../CustomTag';
import { TagType } from '../CustomTag/types';
import { useTheme } from '../../../theme/ThemeProvider';

const Title: React.FC<TitleProps> = ({
  children,
  fontSize = 32,
  className,
  style,
  tags = [],
  tagGap = 4,
  tagTitleGap = 4,
  lines,
  ellipsis = 'ellipsis',
  textAlign = 'left',
}) => {
  const { theme } = useTheme();

  if(typeof fontSize === 'string') {
    fontSize = TitleLevel[fontSize as keyof typeof TitleLevel]
  };

  // 构建类名
  const titleClassName = [
    'title',
    className
  ].filter(Boolean).join(' ');

  const tagContainerClassName = 'title__tag-container';
  const tagClassName = 'title__tag';
  const textClassName = 'title__text';

  // 构建样式
  const titleStyle = {
    fontSize: fontSize,
    textAlign: textAlign,
    // backgroundColor: theme.colors.primary,
    ...style,
  };

  // 文本样式，包含行数限制
  // const textStyle: React.CSSProperties = {
  //   // marginLeft: tags.length > 0 ? 0 : 0,
  //   textAlign: textAlign,
  //   ...style,
  //   fontSize: fontSize,
  // };

  // 设置文字对齐格式、字体字号
  const textStyle = {
    // marginLeft: tags.length > 0 ? 0 : 0,
    textAlign: textAlign,
    justifyContent: textAlign,
    ...style,
    fontSize: fontSize,
  };

  // 如果设置了行数限制，添加相应样式
  if (lines) {
    Object.assign(textStyle, {
      textAlign: textAlign,
      webkitLineClamp: lines,
      textOverflow: ellipsis === 'ellipsis' ? 'ellipsis' : 'clip',
      display: '-webkit-box',
      // WebkitBoxOrient: 'horizontal' as any,
      numberOfLines: lines,
      ...style,
      fontSize: fontSize,
    });
  };
  console.log("textStyle===>",children);
  // 根据省略方式添加特殊样式
  // if (ellipsis === 'fade' && lines) {
  //   textStyle.maskImage = 'linear-gradient(to bottom, black 50%, transparent 100%)';
  // };
  return (
    <View
      className={titleClassName}
      style={titleStyle}
    >
      <Text
        className={textClassName}
        style={textStyle}
        numberOfLines={lines}
        overflow={ellipsis}
      >
        {tags.length > 0 && (
        <View 
          className={tagContainerClassName}
          style={{height: fontSize, lineHeight: fontSize}}
        >
          {tags.map((tag, index) => (
            <View
              key={index}
              style={{flexDirection: 'row'}}
              className={tagClassName}
            >
              <CustomTag 
                type={TagType.TEXT}
                text={tag.text || '促销标签'}
                style={{
                  alignItems: 'center',
                  // backgroundColor: '#ff4d4f'
                }}
                textStyle={{ 
                  color: '#fff', 
                  fontSize: ((fontSize * 0.75))
                }}
              />
              <View style={{width: tagGap}}/>
            </View>
          ))}
        </View>
        )}
        {children}
      </Text>
    </View>
  );
};

export default memo(Title); 