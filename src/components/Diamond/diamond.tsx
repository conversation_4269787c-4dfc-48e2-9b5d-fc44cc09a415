import React, { useMemo } from 'react';
import { StyleSheet } from 'react-native';
import DJImage from '@DJUIComponents/atoms/DJImage';
import DJText from '@DJUIComponents/atoms/DJText';
import DJView from '@DJUIComponents/atoms/DJView';
import { RNDiamondProps } from './types';
import './diamond.scss';

/**
 * Diamond组件 - 用于展示可选择的带图标和文本的方块
 */
const Diamond: React.FC<RNDiamondProps> = (props) => {
    const { imageSrc, imageSelectedSrc, text, width, paddingHorizontal, height, disabled, selected } = props;
    const { imageSize, imageSelectedSize, imageSelectedBorder, imageSelectedBorderColor, imageSelectedBorderRadius } = props;
    const { textHeight = defaultTextHeight, textColor, textSelectedColor, textFontSize, textSelectedFontSize } = props;
    const { textSelectedBackgroundColor, textSelectedBackgroundRadius, textSelectedBackgroundStartColor, textSelectedBackgroundEndColor, textSelectedBackgroundPadding } = props;

    // 图片大小
    const imageRect = useMemo(() => (
        selected
            ? imageSelectedSize || imageSize
            : imageSize
    ), [selected, imageSize, imageSelectedSize]);

    // 禁用状态
    const opacity = useMemo(() => disabled ? 0.5 : 1, [disabled]);

    // 图片显示的 SRC
    const imageDisplaySrc = useMemo(() => (
        selected
            ? imageSelectedSrc || imageSrc
            : imageSrc
    ), [imageSrc, imageSelectedSrc, selected]);

    // 图片显示的边框宽度
    const imageDisplayBorderWidth = useMemo(() => (
        imageSelectedBorder ? 1 : 0
    ), [imageSelectedBorder]);

    // 图片显示的真实尺寸
    const imageDisplaySize = useMemo(() => (
        imageRect - imageDisplayBorderWidth * 2
    ), [imageRect, imageDisplayBorderWidth]);

    // 图片显示的圆角
    const imageDisplayRadius = useMemo(() => (
        selected ? imageSelectedBorderRadius : undefined
    ), [selected, imageSelectedBorderRadius]);

    // 图片显示的边框颜色
    const imageDisplayBorderColor = useMemo(() => (
        selected ? imageSelectedBorderColor : 'transparent'
    ), [selected, imageSelectedBorderColor]);

    // 文字容器的最大宽度
    const textDisplayMaxWidth = useMemo(() => (
        Math.ceil(width - (paddingHorizontal ?? 0) * 2)
    ), [width, paddingHorizontal]);

    // 文字显示的颜色
    const textDisplayColor = useMemo(() => (
        selected
            ? textSelectedColor || defaultTextSelectedColor
            : textColor || defaultTextColor
    ), [selected, textSelectedColor, textColor]);

    // 文字显示的字体粗细
    const textDisplayFontWeight = useMemo(() => (
        selected ? '500' : '400'
    ), [selected]);

    // 文字显示的字体大小
    const textDisplayFontSize = useMemo(() => {
        // 根据选中状态选择字体大小
        const fontSize = selected
            ? (textSelectedFontSize ?? textFontSize)
            : textFontSize;

        // 如果还是undefined，使用默认大小
        const finalSize = fontSize || 12;

        // 向下取整
        return Math.floor(finalSize);
    }, [selected, textSelectedFontSize, textFontSize]);

    // 文字显示的背景颜色
    const textDisplayBackgroundColors = useMemo(() => {
        // 非选中状态返回默认颜色
        if (!selected) return defaultColors;

        // 有起始和结束颜色时返回渐变色
        if (textSelectedBackgroundStartColor && textSelectedBackgroundEndColor) {
            return [textSelectedBackgroundStartColor, textSelectedBackgroundEndColor];
        }

        // 只有背景色时返回相同的两个颜色
        if (textSelectedBackgroundColor) {
            return [textSelectedBackgroundColor, textSelectedBackgroundColor];
        }

        // 默认情况
        return defaultColors;
    }, [selected, textSelectedBackgroundStartColor, textSelectedBackgroundEndColor, textSelectedBackgroundColor]);

    // 文字背景内边距
    const textDisplayBackgroundPadding = useMemo(() => {
        // 检查背景色是否有效
        const isValidBackground = textDisplayBackgroundColors.length > 0 &&
            textDisplayBackgroundColors[0] !== 'transparent';

        // 有效时使用提供的内边距或默认值，无效时为0
        const padding = isValidBackground
            ? (textSelectedBackgroundPadding ?? 6)
            : 0;

        return Math.floor(padding);
    }, [textDisplayBackgroundColors, textSelectedBackgroundPadding]);

    // 构建容器类名
    const containerClassName = 'diamond';
    const imageClassName = 'diamond__image-wrapper';
    const textClassName = 'diamond__text-wrapper';

    return (
        <DJView style={[commonStyles.center, { width, height, opacity }]} className={containerClassName}>
            {/* 图片区域 */}
            <DJView style={[
                commonStyles.center,
                styles.wrapperImage,
                {
                    width: imageRect,
                    height: imageRect,
                    borderRadius: imageDisplayRadius,
                },
            ]} className={imageClassName}>
                <DJImage
                    style={{
                        borderWidth: imageDisplayBorderWidth,
                        borderColor: imageDisplayBorderColor,
                        width: imageDisplaySize,
                        height: imageDisplaySize,
                        borderRadius: imageDisplayRadius,
                    }}
                    src={imageDisplaySrc}
                />
            </DJView>

            {/* 文本区域 */}
            <DJView style={[
                styles.wrapperText,
                {
                    maxWidth: textDisplayMaxWidth,
                    height: textHeight,
                    borderRadius: textSelectedBackgroundRadius ?? 4,
                }
            ]} className={textClassName}>
                <DJView
                    style={[commonStyles.center, {
                        height: textHeight,
                        backgroundColor: textDisplayBackgroundColors[0] !== 'transparent' ? textDisplayBackgroundColors[0] : undefined
                    }]}
                >
                    <DJText
                        style={StyleSheet.flatten([
                            styles.text,
                            {
                                lineHeight: textHeight,
                                fontWeight: textDisplayFontWeight,
                                color: textDisplayColor,
                                fontSize: textDisplayFontSize,
                                paddingHorizontal: textDisplayBackgroundPadding,
                            },
                        ])}
                    >
                        {text}
                    </DJText>
                </DJView>
            </DJView>
        </DJView>
    );
};

const defaultTextColor = '#1A1A1A';
const defaultTextSelectedColor = '#FFF';
const defaultTextHeight = 20;
const defaultColors = ['transparent', 'transparent'];

const commonStyles = StyleSheet.create({
    center: {
        alignItems: 'center',
        justifyContent: 'flex-end',
        flexGrow: 1,
    }
});

const styles = StyleSheet.create({
    wrapperImage: {
        overflow: 'hidden',
    },
    wrapperText: {
        marginTop: 4,
        overflow: 'hidden',
    },
    text: {
        fontFamily: 'PingFang SC',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
    }
});

export default Diamond;
