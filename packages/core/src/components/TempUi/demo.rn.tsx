import React from 'react';
import { View, Text, ScrollView, StyleSheet, SafeAreaView, Platform } from 'react-native';
import TempUI from './TempUi';

export default function TempUIDemo() {
  const HEADER_HEIGHT = 56;
  return (
    <View style={styles.container}>
      {/* 顶部标题栏，绝对定位 */}
      <View style={[styles.header, { height: HEADER_HEIGHT }]}> 
        <Text style={[styles.headerTitle, { lineHeight: HEADER_HEIGHT }]}>TempUI</Text>
      </View>
      <ScrollView
        style={[styles.scrollView, { marginTop: HEADER_HEIGHT }]}
        contentContainerStyle={styles.content}
      >
        {/* 基础用法 */}
        <Text style={styles.sectionTitle}>基础用法</Text>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>默认样式</Text>
          <TempUI>基础内容</TempUI>
        </View>

        {/* 自定义样式 */}
        <Text style={styles.sectionTitle}>自定义样式</Text>
        <View style={styles.card}>
          <Text style={styles.cardTitle}>自定义样式</Text>
          <TempUI style={{ color: 'red', backgroundColor: '#f5f5f5', padding: 8, borderRadius: 4 }}>
            自定义样式内容
          </TempUI>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f8fa',
  },
  header: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    backgroundColor: '#fff',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    paddingTop: Platform.OS === 'android' ? 0 : 0,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 16,
    paddingBottom: 40,
  },
  sectionTitle: {
    fontSize: 16,
    color: '#666',
    marginTop: 16,
    marginBottom: 12,
    marginLeft: 4,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 14,
    color: '#999',
    marginBottom: 12,
  },
}); 