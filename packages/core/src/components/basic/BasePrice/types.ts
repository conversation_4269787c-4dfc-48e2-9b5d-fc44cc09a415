import React from 'react';
import { ViewStyle, TextStyle } from 'react-native'; // Restoring for RN specific style props

export interface BasePriceProps {
  /** 价格数值 */
  price: number;
  /** 货币符号，默认为 '¥' */
  currencySymbol?: string;

  /** 是否显示小数部分。如果 price 本身是整数，则根据 decimalPlaces补齐.00。默认为 true */
  showDecimal?: boolean;
  /** 小数位数，可选 0, 1, 2。默认为 2。如果为 0，则不显示小数部分。 */
  decimalPlaces?: 0 | 1 | 2;

  /** 货币符号的颜色 */
  symbolColor?: string;
  /** 货币符号的字体大小 (统一使用数值，不带单位) */
  symbolFontSize?: number;
  /** 货币符号的额外Text样式 (RN) */
  symbolTextStyle?: TextStyle;

  /** 整数部分的颜色 */
  integerColor?: string;
  /** 整数部分的字体大小 (统一使用数值，不带单位) */
  integerFontSize?: number;
  /** 整数部分的额外Text样式 (RN) */
  integerTextStyle?: TextStyle;

  /** 小数部分的颜色 */
  decimalColor?: string;
  /** 小数部分的字体大小 (统一使用数值，不带单位) */
  decimalFontSize?: number;
  /** 小数部分的额外Text样式 (RN) */
  decimalTextStyle?: TextStyle;

  /** 是否应用删除线效果，默认为 false */
  strikethrough?: boolean;

  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
  /** 自定义根元素内联样式 (Taro/Web) */
  style?: React.CSSProperties;
  /** 自定义根元素样式 (RN) */
  containerStyle?: ViewStyle;
} 