import { forwardRef } from "react";
import { StyleSheet } from "react-native";

/**
 * 高阶函数，为 Web 样式包装组件, 只适用于React-Native组件
 */
export default function withStyleForWeb() {
    return function (WrappedComponent) {
        return forwardRef<any, any>(function (props, ref) {

            const { className, style, ...others } = props;

            let clsObj = {};
            let hasStyleWithClassName = false;
            if (Array.isArray(style)) {
                const cls = style.find(x => x?.['$$css'])
                if (cls) {
                    hasStyleWithClassName = true
                    cls['_'] = `${className} ${cls['_']}`;
                }
            }

            if (className && !hasStyleWithClassName) {
                clsObj['$$css'] = true;
                clsObj['_'] = className;
            }

            const updatedStyle = [clsObj, StyleSheet.flatten(props.style)];

            return <WrappedComponent ref={ref} {...others} style={updatedStyle} />;

        });
    };
}
