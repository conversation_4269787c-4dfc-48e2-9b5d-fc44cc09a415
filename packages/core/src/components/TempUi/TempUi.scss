.temp-ui {
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

// Demo 样式
.demo-container {
  min-height: 100vh;
  background-color: #f7f8fa;
}

.demo-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 10;
  height: 56px;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.demo-header-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

.demo-content {
  padding: 16px;
  padding-top: 72px;
  padding-bottom: 40px;
}

.demo-section {
  margin-bottom: 24px;
}

.demo-section-title {
  font-size: 16px;
  color: #666;
  margin: 16px 0 12px 4px;
}

.demo-card {
  background-color: #fff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 16px;
}

.demo-card-title {
  font-size: 14px;
  color: #999;
  margin-bottom: 12px;
}

.custom-temp {
  padding: 8px 16px;
  background: #f5f5f5;
  border-radius: 4px;
} 