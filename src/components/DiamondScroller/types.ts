import { BaseDiamondProps, RNDiamondProps, TaroDiamondProps } from '../Diamond/types';
import { CSSProperties } from 'react';

/**
 * Diamond滚动组件项目数据
 */
export interface DiamondItem extends Omit<BaseDiamondProps, 'width' | 'imageSize'> {
  /** 唯一标识符 */
  id: string;
  /** 是否禁用 */
  disabled?: boolean;
}

/**
 * Diamond滚动组件基础属性
 */
export interface BaseDiamondScrollerProps {
  /** 数据列表 */
  items: DiamondItem[];
  /** 当前选中项ID */
  selectedId?: string;
  /** 是否启用多选 */
  multiple?: boolean;
  /** 每个Diamond的宽度 */
  itemWidth?: number;
  /** 图片尺寸 */
  imageSize?: number;
  /** 行数 - 1或2 */
  rows?: 1 | 2;
  /** 是否显示滚动指示器 */
  showIndicator?: boolean;
  /** 点击回调 */
  onItemClick?: (item: DiamondItem, index: number, selected: boolean) => void;
}

/**
 * React Native版本的Diamond滚动组件属性
 */
export interface RNDiamondScrollerProps extends BaseDiamondScrollerProps {
  /** 容器样式 */
  style?: any;
  /** Diamond组件样式 */
  diamondStyle?: RNDiamondProps['style'];
}

/**
 * Taro版本的Diamond滚动组件属性
 */
export interface TaroDiamondScrollerProps extends BaseDiamondScrollerProps {
  /** 容器样式 */
  style?: CSSProperties;
  /** Diamond组件样式 */
  diamondStyle?: TaroDiamondProps['style'];
  /** 自定义类名 */
  className?: string;
}

/**
 * 兼容性导出类型
 */
export type DiamondScrollerProps = RNDiamondScrollerProps | TaroDiamondScrollerProps;
