/**
 * useScreen Hook
 * 
 * 提供屏幕宽高和scaleByScreen方法，直接封装项目的ToolLibs/device
 */
import { useEffect, useState, useMemo } from 'react';
import {
  getScreenSize,
  getScaleByScreenFunction,
  addScreenChangeListener,
  getIsPad,
  createScaleStyleFunction
} from '../utils/screenUtils';
import type { ScreenInfo } from '../types/types';

/**
 * useScreen Hook
 * 
 * @returns 屏幕信息：宽度、高度、是否平板、scaleByScreen方法、scaleStyle方法
 */
export function useScreen(): ScreenInfo {
  // 屏幕尺寸状态
  const [screenSize, setScreenSize] = useState(() => getScreenSize());
  // 平板状态
  const [isPad, setIsPad] = useState(() => getIsPad());
  
  // 监听屏幕尺寸变化
  useEffect(() => {
    const removeListener = addScreenChangeListener(() => {
      setScreenSize(getScreenSize());
      setIsPad(getIsPad());
    });
    
    return removeListener;
  }, []);

  // 获取scaleByScreen方法（静态方法，不依赖屏幕尺寸变化）
  const scaleByScreen = useMemo(() => {
    return getScaleByScreenFunction();
  }, []);

  // 获取scaleStyle方法（静态方法，不依赖屏幕尺寸变化）
  const scaleStyle = useMemo(() => {
    return createScaleStyleFunction();
  }, []);

  return {
    width: screenSize.width,
    height: screenSize.height,
    isPad,
    scaleByScreen,
    scaleStyle
  };
} 