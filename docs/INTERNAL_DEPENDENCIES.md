# 🔗 内部依赖管理指南

## 📊 当前依赖关系图

```
@jd/lifeui-core (核心包)
    ↑
    │
@jd/lifeui-business-shared (共享业务组件)
    ↑
    │
┌───┴───┬───────┬───────┬───────┐
│       │       │       │       │
groupon instant takeout travel  ...
```

## 🎯 依赖管理原则

### 1. **单向依赖**
- ✅ 业务包 → 共享包 → 核心包
- ❌ 避免循环依赖
- ❌ 核心包不应依赖业务包

### 2. **版本策略**
- 使用 `^` 语义化版本范围
- 内部依赖只放在 `dependencies` 中
- ❌ 不要在 `devDependencies` 中声明内部依赖

### 3. **发布顺序**
1. 先发布 `@jd/lifeui-core`
2. 再发布 `@jd/lifeui-business-shared`
3. 最后发布各业务包

## 🛠️ 管理命令

### 检查依赖状态
```bash
# 检查所有内部依赖
yarn deps:check

# 验证依赖配置正确性
yarn deps:validate
```

### 修复依赖问题
```bash
# 修复重复依赖问题
yarn deps:fix

# 同步内部依赖版本
yarn deps:sync
```

### 手动更新依赖
```bash
# 更新特定内部依赖版本
./scripts/manage-internal-deps.sh update @jd/lifeui-core 1.2.0
```

## 🔄 版本更新流程

### 场景1: 核心包更新
```bash
# 1. 更新核心包
cd packages/core
# ... 修改代码

# 2. 发布核心包
yarn release:patch

# 3. 更新依赖核心包的其他包
yarn deps:sync

# 4. 发布其他包
yarn release:patch
```

### 场景2: 共享包更新
```bash
# 1. 更新共享包
cd packages/business-shared
# ... 修改代码

# 2. 发布共享包
yarn publish:shared

# 3. 更新依赖共享包的业务包
./scripts/manage-internal-deps.sh update @jd/lifeui-business-shared 1.1.0

# 4. 发布业务包
yarn publish:groupon
yarn publish:instant
# ... 其他业务包
```

## ⚠️ 常见问题

### 1. 依赖版本不一致
**问题**: 不同包中同一内部依赖版本不同
```bash
# 检查
yarn deps:check

# 修复
yarn deps:sync
```

### 2. devDependencies 中有内部依赖
**问题**: 在 devDependencies 中声明了内部包
```bash
# 检查
yarn deps:validate

# 修复
yarn deps:fix
```

### 3. 循环依赖
**问题**: 包A依赖包B，包B又依赖包A
```bash
# 检查依赖图
npx madge --circular packages/*/src/index.ts

# 解决方案: 重构代码，提取共同依赖到上层包
```

### 4. 构建顺序问题
**问题**: 依赖的包还未构建就开始构建当前包
```bash
# 使用 lerna 的拓扑排序构建
npx lerna run build --stream

# 或者手动按顺序构建
yarn build:core
yarn build:shared
yarn build:groupon
# ... 其他包
```

## 🔒 最佳实践

### 1. **依赖声明**
```json
{
  "dependencies": {
    "@jd/lifeui-core": "^1.0.0"
  },
  "devDependencies": {
    "@types/react": "^18.0.0"
  },
  "peerDependencies": {
    "react": "^18.0.0"
  }
}
```

### 2. **版本更新**
- 使用 `yarn deps:sync` 保持版本一致
- 遵循语义化版本规范
- 重大更新前通知所有依赖包维护者

### 3. **发布流程**
- 使用自动化发布脚本
- 按依赖顺序发布
- 发布前运行完整测试

### 4. **监控依赖**
- 定期运行 `yarn deps:check`
- 在 CI/CD 中集成依赖检查
- 监控包大小变化

## 📋 检查清单

发布前检查:
- [ ] 运行 `yarn deps:validate` 通过
- [ ] 运行 `yarn test` 通过
- [ ] 运行 `yarn build` 成功
- [ ] 检查版本号正确
- [ ] 确认依赖版本兼容

## 🚨 紧急修复流程

如果发现依赖问题导致生产问题:

1. **立即回滚**
```bash
# 回滚到上一个稳定版本
npm dist-tag add @jd/lifeui-core@1.0.0 latest
```

2. **修复问题**
```bash
# 修复代码
# 运行测试
yarn test

# 发布修复版本
yarn release:patch
```

3. **通知下游**
- 通知所有依赖包维护者
- 更新文档和变更日志
- 分析问题原因，改进流程

## 📚 相关工具

- [Lerna](https://lerna.js.org/) - Monorepo 管理
- [Madge](https://github.com/pahen/madge) - 依赖分析
- [npm-check-updates](https://github.com/raineorshine/npm-check-updates) - 依赖更新
- [Dependency Cruiser](https://github.com/sverweij/dependency-cruiser) - 依赖可视化
