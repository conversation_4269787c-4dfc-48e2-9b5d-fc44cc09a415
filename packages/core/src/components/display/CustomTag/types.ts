import { CSSProperties } from 'react';
import { StyleProp, ViewStyle, TextStyle } from 'react-native';

/**
 * 基础标签属性接口
 */
export interface BaseCustomTagProps {
  /** 标签类型 */
  type: TagType;
  /** 标签文本 */
  text?: string;
}

// 给标组件定义一个type, 每种type对应不同的必填属性
// 1、纯文字版本
// 2、图片版本
// 3、前面icon + 文字版本/图片版本
// 4、后面icon + 文字版本/图片版本

/**
 * 标签类型枚举
 */
export enum TagType {
  /** 纯文字版本 */
  TEXT = 'text',
  /** 图片版本 */
  IMAGE = 'image',
  /** 前面icon + 文字/图片版本 */
  ICON_PREFIX = 'iconPrefix',
  /** 后面icon + 文字/图片版本 */
  ICON_SUFFIX = 'iconSuffix'
}

/**
 * 各类型标签属性接口
 */
export interface TagTypeProps {
  /** 标签类型 */
  type: TagType;
  /** 图片URL (仅IMAGE、ICON_PREFIX或ICON_SUFFIX类型带图片时必填) */
  tagImage?: string;
  /** 前置图标 (仅ICON_PREFIX类型必填) */
  prefixIcon?: string;
  /** 后置图标 (仅ICON_SUFFIX类型必填) */
  suffixIcon?: string;
  /** 文本内容 (仅TEXT、ICON_PREFIX或ICON_SUFFIX类型带文本时必填) */
  text?: string;
}

/**
 * Taro版本的自定义标签属性
 */
export interface TaroCustomTagProps extends BaseCustomTagProps {

  /** 标签容器样式 */
  style?: CSSProperties;
  /** 标签文本样式 */
  textStyle?: CSSProperties;
  /** 标签类名 */
  className?: string;
  /** 标签前图标 (兼容旧版本) */
  icon?: string;
  /** 前置图标 (ICON_PREFIX类型使用) */
  prefixIcon?: string;
  /** 后置图标 (ICON_SUFFIX类型使用) */
  suffixIcon?: string;
  /** 图标样式 */
  iconStyle?: CSSProperties;
  /** 标签图片 */
  tagImage?: string;
  /** 标签图片样式 */
  tagImageStyle?: CSSProperties;
}

/**
 * React Native版本的自定义标签属性
 */
export interface RNCustomTagProps extends BaseCustomTagProps {
  /** 标签容器样式 */
  style?: StyleProp<ViewStyle>;
  /** 标签文本样式 */
  textStyle?: StyleProp<TextStyle>;
  /** 仅为兼容性保留 */
  className?: string;
}

/**
 * 兼容性导出类型
 */
export type CustomTagProps = TaroCustomTagProps | RNCustomTagProps;
