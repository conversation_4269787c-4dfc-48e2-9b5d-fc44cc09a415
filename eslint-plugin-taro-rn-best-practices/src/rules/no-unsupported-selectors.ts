import { ESLintUtils } from '@typescript-eslint/utils';

const createRule = ESLintUtils.RuleCreator(
  (name) => `https://docs.taro.zone/docs/react-native-remind#${name}`
);

export default createRule({
  name: 'no-unsupported-selectors',
  meta: {
    type: 'problem',
    docs: {
      description: 'Disallow CSS selectors that are not supported in React Native styled-components.',
      recommended: 'error',
    },
    messages: {
      noUnsupportedSelectors: 'CSS selectors like "{{ selector }}" are not supported in React Native. Use component-specific styles instead.',
    },
    schema: [],
  },
  defaultOptions: [],
  create(context) {
    const sourceCode = context.getSourceCode();
    
    return {
      TaggedTemplateExpression(node) {
        // Check if this is a styled-components or emotion css template
        if (
          node.tag.type === 'Identifier' && 
          (node.tag.name === 'styled' || node.tag.name === 'css')
        ) {
          const templateText = sourceCode.getText(node.quasi);
          
          // Look for common CSS selectors
          const selectorPatterns = [
            /\s*:\s*(hover|active|focus|visited|link|first-child|last-child|nth-child|before|after)/g,
            /\s*>\s*[a-zA-Z]/g,
            /\s*\+\s*[a-zA-Z]/g,
            /\s*~\s*[a-zA-Z]/g,
            /\s*\.[a-zA-Z]/g,
            /\s*#[a-zA-Z]/g,
            /\s*\[[a-zA-Z]/g,
          ];
          
          for (const pattern of selectorPatterns) {
            const match = pattern.exec(templateText);
            if (match) {
              context.report({
                node,
                messageId: 'noUnsupportedSelectors',
                data: { selector: match[0].trim() },
              });
            }
          }
        }
      },
    };
  },
});