/**
 * 屏幕工具函数 - React Native版本
 * 
 * 专门为React Native环境提供的屏幕工具函数
 */
import { Dimensions } from 'react-native';
import type { DesignValue } from '../types/types';
import * as Device from './device/index';

/**
 * 获取屏幕尺寸
 */
export function getScreenSize(): { width: number; height: number } {
  const { width, height } = Dimensions.get('window');
  return { width, height };
}

/**
 * 判断是否为平板设备
 */
export function getIsPad(): boolean {
  return (Device as any).isPad || false;
}

/**
 * 获取项目的scaleByScreen方法
 */
export function getScaleByScreenFunction(): (value: DesignValue, forceScale?: boolean) => number {
  // 优先使用项目的device工具
  if (Device.scaleByScreen && typeof Device.scaleByScreen === 'function') {
    return Device.scaleByScreen;
  }
  
  // RN环境的降级实现
  return (value: DesignValue, forceScale = false): number => {
    if (typeof value === 'number') {
      const { width } = getScreenSize();
      return value * width / 375; // 基于375px设计稿的缩放
    }
    
    if (typeof value === 'string') {
      return parseFloat(value) || 0;
    }
    
    // 对象类型处理
    if (typeof value === 'object' && value !== null) {
      return getScaleByScreenFunction()(value.mobile, forceScale);
    }
    
    return 0;
  };
}

/**
 * 监听屏幕尺寸变化
 */
export function addScreenChangeListener(callback: () => void): () => void {
  const subscription = Dimensions.addEventListener('change', callback);
  
  return () => {
    if (subscription?.remove) {
      subscription.remove();
    }
  };
}

// 需要缩放的CSS属性列表
const SCALABLE_STYLE_PROPS = [
  // 尺寸属性
  'width', 'height', 'minWidth', 'maxWidth', 'minHeight', 'maxHeight',
  // 内边距
  'padding', 'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',
  'paddingHorizontal', 'paddingVertical',
  // 外边距  
  'margin', 'marginTop', 'marginRight', 'marginBottom', 'marginLeft',
  'marginHorizontal', 'marginVertical',
  // 边框
  'borderRadius', 'borderWidth', 'borderTopWidth', 'borderRightWidth', 
  'borderBottomWidth', 'borderLeftWidth',
  'borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomLeftRadius', 'borderBottomRightRadius',
  // 位置
  'top', 'right', 'bottom', 'left',
  // 字体
  'fontSize', 'lineHeight',
  // 其他
  'gap', 'rowGap', 'columnGap'
];

// React Native不支持的CSS属性列表
const UNSUPPORTED_RN_PROPS = [
  'display' // RN只支持flex和none，不支持block、inline等
];

// display属性的转换映射
const DISPLAY_VALUE_MAP = {
  'block': 'flex',
  'inline': 'flex', 
  'inline-block': 'flex',
  'flex': 'flex',
  'none': 'none'
};

/**
 * 创建样式缩放函数
 */
export function createScaleStyleFunction(): (style: Record<string, any>) => Record<string, any> {
  const scaleByScreenFn = getScaleByScreenFunction();
  
  return (style: Record<string, any>): Record<string, any> => {
    if (!style || typeof style !== 'object') {
      return style || {};
    }
    
    const scaledStyle: Record<string, any> = {};
    
    // 遍历样式属性
    Object.keys(style).forEach(key => {
      const value = style[key];
      
      // 特殊处理display属性
      if (key === 'display') {
        const mappedValue = DISPLAY_VALUE_MAP[value as string] || 'flex';
        scaledStyle[key] = mappedValue;
        return;
      }
      
      // 跳过React Native不支持的属性
      if (UNSUPPORTED_RN_PROPS.includes(key)) {
        return;
      }
      
      // 如果是需要缩放的属性且值为数字
      if (SCALABLE_STYLE_PROPS.includes(key) && typeof value === 'number') {
        const scaledValue = scaleByScreenFn(value);
        scaledStyle[key] = scaledValue;
      } else {
        // 其他属性直接复制
        scaledStyle[key] = value;
      }
    });
    
    return scaledStyle;
  };
} 