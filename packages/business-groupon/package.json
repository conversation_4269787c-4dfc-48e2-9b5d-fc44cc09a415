{"name": "@jd/lifeui-business-groupon", "version": "1.0.0", "description": "本地生活团购业务组件库", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/types/src/index.d.ts", "react-native": "./dist/index.rn.js", "scripts": {"dev": "vite --config vite.config.dev.js", "build": "vite build", "build:multi": "node ../../scripts/build-multiplatform.js .", "build:rn": "node ../../scripts/build-multiplatform.js . rn", "build:h5": "node ../../scripts/build-multiplatform.js . h5", "build:weapp": "node ../../scripts/build-multiplatform.js . weapp", "test": "jest", "lint": "eslint src"}, "files": ["dist", "src"], "dependencies": {"@jd/lifeui-core": "^1.0.0", "@jd/lifeui-business-shared": "^1.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-native": "^0.72.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-native": "^0.72.0"}, "exports": {".": {"react-native": "./dist/index.rn.js", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json", "./rn": {"import": "./dist/index.rn.js", "require": "./dist/index.rn.cjs", "default": "./dist/index.rn.js"}, "./h5": {"import": "./dist/index.h5.js", "require": "./dist/index.h5.cjs", "default": "./dist/index.h5.js"}, "./weapp": {"import": "./dist/index.weapp.js", "require": "./dist/index.weapp.cjs", "default": "./dist/index.weapp.js"}, "./alipay": {"import": "./dist/index.alipay.js", "require": "./dist/index.alipay.cjs", "default": "./dist/index.alipay.js"}, "./swan": {"import": "./dist/index.swan.js", "require": "./dist/index.swan.cjs", "default": "./dist/index.swan.js"}, "./tt": {"import": "./dist/index.tt.js", "require": "./dist/index.tt.cjs", "default": "./dist/index.tt.js"}, "./qq": {"import": "./dist/index.qq.js", "require": "./dist/index.qq.cjs", "default": "./dist/index.qq.js"}, "./jd": {"import": "./dist/index.jd.js", "require": "./dist/index.jd.cjs", "default": "./dist/index.jd.js"}}}