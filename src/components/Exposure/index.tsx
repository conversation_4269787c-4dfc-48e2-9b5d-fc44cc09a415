import React, {
    createContext,
    useContext,
    useRef,
    forwardRef,
    useEffect,
    useCallback,
    useMemo,
    useImperativeHandle
} from 'react';
import {
    View,
    ScrollView,
    FlatList,
    ScrollViewProps,
    FlatListProps,
    NativeScrollEvent,
    NativeSyntheticEvent,
    Dimensions,
    ViewStyle,
    LayoutChangeEvent,
    findNodeHandle,
    UIManager
} from 'react-native';

/****************** 类型定义 ******************/
type ScrollOffset = { x: number; y: number };
// 容器布局信息
type ContainerLayout = { x: number; y: number; width: number; height: number; } | null;
type LayoutCache = { x: number; y: number; width: number; height: number };

type ScrollContextValue = {
    // 获取所有祖先滚动偏移
    getAncestorScrollOffsets: () => ScrollOffset[];
    // 获取所有祖先容器布局
    getAncestorLayouts: () => ContainerLayout[];
    // 获取所有祖先容器的可见性偏移量
    getAncestorVisibilityOffsets: () => VisibilityOffset[];
    // 通知滚动变化
    notifyScrollChange: () => void;
    // 订阅滚动变化事件
    subscribeToScrollChanges: (callback: () => void) => () => void;
    // 注册子容器的滚动回调
    registerChildContainer: (callback: () => void) => () => void;
};

/****************** 滚动上下文 ******************/
const ScrollContext = createContext<ScrollContextValue>({
    getAncestorScrollOffsets: () => [],
    getAncestorLayouts: () => [],
    getAncestorVisibilityOffsets: () => [],
    notifyScrollChange: () => { }, // 默认空实现
    subscribeToScrollChanges: () => () => { }, // 默认返回一个空的清理函数
    registerChildContainer: () => () => { } // 默认返回一个空的清理函数
});

/****************** 高阶组件：增强滚动容器 ******************/
// 确保组件兼容属性
type ScrollableComponentProps = {
    onScroll?: (e: NativeSyntheticEvent<NativeScrollEvent>) => void;
    onLayout?: (event: LayoutChangeEvent) => void;
    // 容器的可见性偏移量，用于修正其所有子元素的曝光计算
    visibilityOffset?: VisibilityRect;
};

const withScrollTracking = <T extends ScrollableComponentProps>(
    Component: React.ComponentType<T>
) => {
    return forwardRef<any, T>((props, ref) => {
        const scrollOffset = useRef<ScrollOffset>({ x: 0, y: 0 });
        // 容器布局信息
        const containerLayoutRef = useRef<ContainerLayout>(null);
        const containerRef = useRef<any>(null);
        // 保存容器的可见性偏移量
        const visibilityOffsetRef = useRef<VisibilityRect | null>(props.visibilityOffset || null);
        const parentContext = useContext(ScrollContext);

        // 子容器回调函数列表，用于当前容器滚动时通知子容器
        const childContainers = useRef<Set<() => void>>(new Set()).current;

        // 更新容器布局信息
        const updateContainerLayout = useCallback(() => {
            if (!containerRef.current) return;
            const node = findNodeHandle(containerRef.current);
            if (!node) return;
            UIManager.measureInWindow(node, (x: number, y: number, width: number, height: number) => {
                containerLayoutRef.current = { x, y, width, height };
            });
        }, []);

        useImperativeHandle(ref, () => ({
            getNodeRef: () => containerRef.current,
            updateContainerLayout,
            // 添加更新visibilityOffset的方法
            updateVisibilityOffset: (newOffset: VisibilityRect | null) => {
                // 更新visibilityOffset引用
                visibilityOffsetRef.current = newOffset;
            }
        }));

        // 在组件挂载时，将自身注册到父容器的子容器列表中， 更新时测量容器布局
        useEffect(() => {
            // 初始测量
            // updateContainerLayout();

            // 订阅屏幕旋转或尺寸变化事件(如果需要)
            const dimensionsHandler = Dimensions.addEventListener('change', updateContainerLayout);
            // 将自身的滚动回调注册到父容器
            // 这样当父容器滚动时，会通知到子容器
            const unregister = parentContext.registerChildContainer(() => {
                // 当父容器滚动时，会调用这个回调
                // 触发本容器上下文的通知，从而通知所有相关的 ExposureTracker
                childContextValue.notifyScrollChange();
            });

            return () => {
                // 清理尺寸变化监听
                dimensionsHandler.remove();
                // 在组件卸载时取消注册
                unregister();
            };
        }, [parentContext, updateContainerLayout]);

        // 创建一个集合来跟踪订阅者
        const subscribers = useRef<Set<() => void>>(new Set()).current;

        // 创建子上下文值，使用 useMemo 避免不必要的重新创建
        const childContextValue = useMemo<ScrollContextValue>(() => {
            return {
                getAncestorScrollOffsets: () => [
                    ...parentContext.getAncestorScrollOffsets(),
                    scrollOffset.current
                ],
                getAncestorLayouts: () => [
                    ...parentContext.getAncestorLayouts(),
                    containerLayoutRef.current
                ],
                getAncestorVisibilityOffsets: () => [
                    ...parentContext.getAncestorVisibilityOffsets(),
                    visibilityOffsetRef.current
                ],
                notifyScrollChange: () => {
                    // 通知直接订阅者（如ExposureTracker组件）
                    subscribers.forEach(callback => callback());

                    // 通知所有子容器，传递滚动事件
                    childContainers.forEach(callback => callback());
                },
                subscribeToScrollChanges: (callback) => {
                    // 添加订阅
                    subscribers.add(callback);
                    // 返回取消订阅函数
                    return () => {
                        subscribers.delete(callback);
                    };
                },
                // 实现注册子容器回调的方法
                registerChildContainer: (callback) => {
                    // 添加子容器回调
                    childContainers.add(callback);
                    // 返回取消注册函数
                    return () => {
                        childContainers.delete(callback);
                    };
                }
            };
        }, [parentContext, childContainers]);

        // 处理滚动事件
        const handleScroll = useCallback((e: NativeSyntheticEvent<NativeScrollEvent>) => {
            const { x, y } = e.nativeEvent.contentOffset;
            scrollOffset.current = { x, y };
            console.log('handleScrollhandleScroll', scrollOffset.current);
            // 当滚动发生时，通知上下文中的所有订阅者
            // 包括当前容器内的 ExposureTracker 和所有子容器
            childContextValue.notifyScrollChange();

            // 注意：不在这里调用props.onScroll，而是在finalProps中处理
        }, [childContextValue]);


        // 处理props的副本，避免修改原始属性
        const finalProps: any = { ...props };

        // 绑定滚动事件
        const originalOnScroll = props.onScroll;
        finalProps.onScroll = (e: NativeSyntheticEvent<NativeScrollEvent>) => {
            handleScroll(e);
            if (originalOnScroll) {
                originalOnScroll(e);
            }
        };

        // 绑定布局事件
        const originalOnLayout = props.onLayout;
        finalProps.onLayout = (event: LayoutChangeEvent) => {
            updateContainerLayout();
            if (originalOnLayout) {
                originalOnLayout(event);
            }
        };

        return (
            <ScrollContext.Provider value={childContextValue}>
                <Component ref={containerRef} {...finalProps as T} />
            </ScrollContext.Provider>
        );
    });
};

// 定义要转发的ScrollView方法列表
const scrollViewMethods = [
    'scrollTo',
    'scrollToEnd',
    'getScrollResponder',
    'getScrollableNode',
    'getInnerViewNode',
    'setNativeProps',
    'flashScrollIndicators'
];

// 我们使用 registerChildContainer 方法实现子容器的注册
// 这样当父容器滚动时会通知到子容器

// 增强 withScrollTracking 高阶组件，使其支持方法转发
const enhancedWithScrollTracking = <T extends ScrollableComponentProps>(
    Component: React.ComponentType<T>
) => {
    // 使用原始的 withScrollTracking 创建基础组件
    const TrackedComponent = withScrollTracking<T>(Component);

    // 创建一个新的转发组件，它将在 ref 上添加原始组件的方法
    const ForwardingComponent = React.forwardRef<any, T>((props, ref) => {
        const innerRef = React.useRef<any>(null);

        // 将内部组件的方法暴露给外部 ref
        React.useImperativeHandle(ref, () => {
            // 创建一个包含所有基础方法的对象
            const methodsObj: any = {
                // 获取内部组件的 ref
                getInnerRef: () => innerRef.current?.getNodeRef?.()
            };

            // 为每个需要转发的方法创建包装函数
            scrollViewMethods.forEach(method => {
                methodsObj[method] = (...args: any[]) => {
                    const scrollViewRef = innerRef.current?.getNodeRef?.();
                    if (scrollViewRef && typeof scrollViewRef[method] === 'function') {
                        return scrollViewRef[method](...args);
                    }
                    return undefined;
                };
            });

            // 添加更新容器布局和可见性偏移量的方法
            methodsObj.updateContainerLayout = () => innerRef.current?.updateContainerLayout?.();
            methodsObj.updateVisibilityOffset = (offset: VisibilityRect | null) =>
                innerRef.current?.updateVisibilityOffset?.(offset);

            return methodsObj;
        });

        return <TrackedComponent ref={innerRef} {...props} />;
    });

    // 设置displayName以便调试
    ForwardingComponent.displayName = `Enhanced${Component.displayName || Component.name || 'Component'}`;

    return ForwardingComponent;
};

// 使用增强的高阶组件创建可跟踪的滚动视图组件
const TrackedScrollView = enhancedWithScrollTracking<ScrollViewProps>(ScrollView);
const TrackedFlatList = enhancedWithScrollTracking<FlatListProps<any> & { visibilityOffset?: VisibilityRect }>(FlatList);

/****************** 曝光组件实现 ******************/
type VisibilityRect = { top: number; bottom: number; left: number; right: number };
// 将null加入类型以允许未设置偏移量的情况
type VisibilityOffset = VisibilityRect | null;
type ExposureTrackerProps = {
    onExposure: () => void;
    onDisappear?: () => void;
    onVisibleImmediate?: () => void;
    onInvisibleImmediate?: () => void;
    // 本元素自身的可见性偏移量，会与祖先容器的偏移量叠加
    visibilityOffset?: VisibilityRect;
    exposureThreshold?: number;
    children: React.ReactNode;
    style?: ViewStyle;
    testID?: string;
};

const SCREEN = Dimensions.get('window');

const ExposureTracker = ({
    children,
    onExposure,
    onDisappear,
    onVisibleImmediate,
    onInvisibleImmediate,
    visibilityOffset,
    exposureThreshold = 300,
    style,
    testID
}: ExposureTrackerProps) => {
    // 元素自身的偏移量，默认为0
    const elementOffsetRef = useRef<VisibilityRect>(visibilityOffset ?? { top: 0, bottom: 0, left: 0, right: 0 });
    const viewRef = useRef<View>(null);
    const scrollContext = useContext(ScrollContext);

    // 全部使用 ref 存储状态
    const layoutCacheRef = useRef<LayoutCache | null>(null);
    const visibilityCacheRef = useRef<boolean>(false);
    const exposureTimerRef = useRef<NodeJS.Timeout | null>(null);
    const isMounted = useRef(true);

    // 获取所有祖先滚动偏移（始终最新值）
    const getScrollOffsets = useCallback(() => {
        return scrollContext.getAncestorScrollOffsets();
    }, [scrollContext]);

    // 测量并缓存布局（无状态更新）
    const measureAndCacheLayout = useCallback(() => {
        if (!viewRef.current) return;

        const measureCallback = (x: number, y: number, width: number, height: number) => {
            if (!isMounted.current) return;
            console.log('measureCallback', x, y, width, height);
            layoutCacheRef.current = { x, y, width, height };
            checkVisibility(); // 布局变化后立即检查
        };

        viewRef.current.measureInWindow(measureCallback);
    }, []);

    // 可见性计算（纯函数）
    const calculateVisibility = useCallback(() => {
        const layout = layoutCacheRef.current;
        if (!layout) return false;

        // 获取祖先滚动偏移
        const scrollOffsets = getScrollOffsets();
        const totalX = scrollOffsets.reduce((sum, o) => sum + o.x, 0);
        const totalY = scrollOffsets.reduce((sum, o) => sum + o.y, 0);

        // 获取祖先容器布局
        const ancestorLayouts = scrollContext.getAncestorLayouts();

        // 获取祖先容器的可见性偏移量
        const ancestorOffsets = scrollContext.getAncestorVisibilityOffsets();

        // 元素在屏幕中的实际位置（考虑所有滚动偏移）
        const actualX = layout.x - totalX;
        const actualY = layout.y - totalY;

        // 计算累积的可视性偏移量（包括元素自身和所有祖先容器的偏移量）
        let cumulativeOffset = {
            top: elementOffsetRef.current.top,
            bottom: elementOffsetRef.current.bottom,
            left: elementOffsetRef.current.left,
            right: elementOffsetRef.current.right
        };

        // 叠加所有祖先容器的可见性偏移量
        ancestorOffsets.forEach(offset => {
            if (offset) {
                cumulativeOffset.top += offset.top;
                cumulativeOffset.bottom += offset.bottom;
                cumulativeOffset.left += offset.left;
                cumulativeOffset.right += offset.right;
            }
        });

        // 屏幕可视区域计算（使用累积偏移量）
        const screenVisibleArea = {
            left: cumulativeOffset.left,
            right: SCREEN.width - cumulativeOffset.right,
            top: cumulativeOffset.top,
            bottom: SCREEN.height - cumulativeOffset.bottom
        };

        // 首先检查元素在整个屏幕中是否可见
        const isVisibleInScreen = (
            actualX + layout.width > screenVisibleArea.left &&
            actualX < screenVisibleArea.right &&
            actualY + layout.height > screenVisibleArea.top &&
            actualY < screenVisibleArea.bottom
        );

        if (!isVisibleInScreen) {
            // if (testID === 'ballItem5') {
            //     console.log('test ep 曝光222  000 ==>', isVisibleInScreen)
            // }
            // console.log('元素在屏幕中不可见');
            // console.log("================================================================");
            return false;
        }

        // 检查元素是否在所有祖先容器中可见
        // 只计算元素在最父级容器中是否可见
        if (ancestorLayouts.length === 0) {
            // 如果没有祖先容器，则仅检查元素在屏幕中的可视性
            return true;
        }
        
        // 获取最父级容器（数组中的第一个元素）
        const rootIndex = 0;
        const rootAncestorLayout = ancestorLayouts[rootIndex];
        if (!rootAncestorLayout) return false; // 如果没有有效的父级容器，则认为不可见
        
        // 获取到父级容器的偏移量（只考虑最父级容器）
        const rootVisibilityOffsets = ancestorOffsets.slice(0, rootIndex + 1).filter(Boolean) as VisibilityRect[];
        
        // 计算累积的可视性偏移量，包括元素自身和最父级容器的偏移量
        const rootCumulativeOffset = {
            top: elementOffsetRef.current.top,
            bottom: elementOffsetRef.current.bottom,
            left: elementOffsetRef.current.left,
            right: elementOffsetRef.current.right
        };
        
        rootVisibilityOffsets.forEach(offset => {
            if (offset) {
                rootCumulativeOffset.top += offset.top;
                rootCumulativeOffset.bottom += offset.bottom;
                rootCumulativeOffset.left += offset.left;
                rootCumulativeOffset.right += offset.right;
            }
        });
        
        // 获取所有滚动偏移量（包括所有容器）
        const totalScrollX = scrollOffsets.reduce((sum, o) => sum + o.x, 0);
        const totalScrollY = scrollOffsets.reduce((sum, o) => sum + o.y, 0);
        
        // 元素相对于最父级容器的位置（考虑所有子容器的布局位置）
        const relativeX = layout.x - totalScrollX - rootAncestorLayout.x;
        const relativeY = layout.y - totalScrollY - rootAncestorLayout.y;
        
        // 最父级容器的滚动偏移
        // const rootScroll = scrollOffsets[rootIndex];
        
        // 元素在最父级容器中的位置（考虑容器自身的滚动）
        // const inContainerX = relativeX - rootScroll.x;
        // const inContainerY = relativeY - rootScroll.y;
        
        // 最父级容器可视区域计算（使用累积偏移量并考虑父容器自身的位置偏移）
        const containerVisibleArea = {
            left: rootCumulativeOffset.left ,
            right: rootAncestorLayout.width - rootCumulativeOffset.right ,
            top: rootCumulativeOffset.top ,
            bottom: rootAncestorLayout.height - rootCumulativeOffset.bottom 
        };
        
        // 检查元素是否在最父级容器的可视区域内
        const isVisibleInRootAncestor = (
            Math.ceil(relativeX + layout.width) > containerVisibleArea.left &&
            Math.ceil(relativeX) < containerVisibleArea.right &&
            Math.ceil(relativeY + layout.height) > containerVisibleArea.top &&
            Math.floor(relativeY) < containerVisibleArea.bottom
        );
        
        // if (testID === 'ballItem5') {
        //     console.log(isVisibleInRootAncestor, JSON.stringify(scrollOffsets[1]), 'test ep 曝光111 ==>=============检查元素是否在最父级容器的可视区域内================')
        //     console.log('test ep 曝光111 ==> relativeX + layout.width', relativeX + layout.width, containerVisibleArea.left)
        //     console.log('test ep 曝光111 ==> relativeX', relativeX, containerVisibleArea.right)
        //     console.log('test ep 曝光111 ==> relativeY + layout.height', relativeY + layout.height, containerVisibleArea.top)
        //     console.log('test ep 曝光111 ==> relativeY', relativeY, containerVisibleArea.bottom)
        //     console.log('test ep 曝光111 ==>=============', isVisibleInRootAncestor)
        //     console.log('test ep 曝光111 ==> 3333', isVisibleInRootAncestor)
        // }
        
        if (!isVisibleInRootAncestor) {
            return false;
        }
        
        return true;
    }, []);

    // 处理可见性变化（带缓存检测）
    const handleVisibilityChange = useCallback((isVisible: boolean) => {
        if (isVisible === visibilityCacheRef.current) return;

        // 更新缓存并触发立即回调
        visibilityCacheRef.current = isVisible;
        isVisible ? onVisibleImmediate?.() : onInvisibleImmediate?.();

        // 清除之前的定时器
        if (exposureTimerRef.current) {
            clearTimeout(exposureTimerRef.current);
        }

        // 处理曝光延时逻辑
        if (isVisible) {
            exposureTimerRef.current = setTimeout(() => {
                onExposure();
            }, exposureThreshold);
        } else {
            onDisappear?.();
        }
    }, [exposureThreshold, onExposure, onDisappear]);

    // 执行可见性检查（带防抖）
    const checkVisibility = useMemo(() => {
        let lastCheck = 0;
        return () => {
            const now = Date.now();
            if (now - lastCheck < 16) return; // 60fps限制
            lastCheck = now;

            // 使用更新后的位置信息计算可见性
            const isVisible = calculateVisibility();
            handleVisibilityChange(isVisible);
        };
    }, [calculateVisibility, handleVisibilityChange]);

    // 订阅滚动事件
    useEffect(() => {
        // 订阅滚动变化事件，触发可见性检查
        const unsubscribe = scrollContext.subscribeToScrollChanges(checkVisibility);

        return () => {
            // 取消订阅
            unsubscribe();
        };
    }, [scrollContext]);

    useEffect(() => {
        return () => {
            isMounted.current = false;
            if (exposureTimerRef.current) {
                clearTimeout(exposureTimerRef.current);
            }
        };
    }, []);

    return (
        <View
            ref={viewRef}
            onLayout={measureAndCacheLayout}
            style={style}
            collapsable={false}
        >
            {children}
        </View>
    );
};

export { ExposureTracker, TrackedScrollView, TrackedFlatList };