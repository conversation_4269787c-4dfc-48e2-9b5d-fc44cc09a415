import React from 'react';
import View from '../../basic/View';
import Text from '../../basic/Text';
import { CSSProperties } from 'react';
import { TagType } from '../types';

/**
 * 文本标签属性接口
 */
export interface TextTagProps {
  /** 文本内容 */
  text: string;
  /** 文本样式 */
  textStyle?: CSSProperties;
  /** 标签类名 */
  className?: string;
  /** 标签样式 */
  style?: CSSProperties;
}

/**
 * 纯文本标签组件
 * @param props 组件属性
 */
const TextTag: React.FC<TextTagProps> = ({
  text,
  textStyle,
  className,
  style,
  ...props
}) => {
  // 构建类名
  const tagClassName = [
    'custom-tag',
    `custom-tag--${TagType.TEXT}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <View
      className={tagClassName}
      style={style}
      {...props}
    >
      <Text
        className="custom-tag__text"
        style={textStyle}
      >
        {text}
      </Text>
    </View>
  );
};

export default TextTag;
