# BasePrice 基础价格组件

用于原子化展示价格，支持分别设置货币符号、整数、小数部分的样式，以及整体删除线效果。

## 使用示例

```tsx
import { BasePrice } from '@/components/basic'; // 请根据实际导出路径调整

// 基本用法
<BasePrice price={123.45} />

// 不显示小数
<BasePrice price={99} decimalPlaces={0} />

// 自定义各部分颜色和大小
<BasePrice 
  price={12345.67} 
  symbolColor="blue" 
  symbolFontSize={20} // 统一使用数值，不带单位
  integerColor="green" 
  integerFontSize={24} 
  decimalColor="red" 
  decimalFontSize={16} 
/>

// 带删除线
<BasePrice price={88.88} strikethrough />

// 自定义货币符号，不显示小数部分
<BasePrice price={100} currencySymbol="$" showDecimal={false} />

// 传递 RN 特有的 TextStyle
<BasePrice 
  price={56.78} 
  symbolFontSize={12}
  integerFontSize={30}
  // #if RN
  symbolTextStyle={{fontWeight: 'bold'}}
  integerTextStyle={{fontStyle: 'italic'}}
  // #endif
/>
```

## Props

| 属性名             | 说明                                                                 | 类型                                     | 默认值    | 
|--------------------|----------------------------------------------------------------------|------------------------------------------|-----------|
| `price`            | 价格数值 (必填)                                                        | `number`                                 | -         |
| `currencySymbol`   | 货币符号                                                             | `string`                                 | `¥`       |
| `showDecimal`      | 是否显示小数部分。若 `price` 为整数，仍会按 `decimalPlaces` 补齐。        | `boolean`                                | `true`    |
| `decimalPlaces`    | 小数位数 (0, 1, 或 2)。若为0，则不显示小数部分。                        | `0 \| 1 \| 2`                            | `2`       |
| `symbolColor`      | 货币符号的颜色                                                         | `string`                                 | -         |
| `symbolFontSize`   | 货币符号的字体大小                                                     | `number`                                 | -         |
| `symbolTextStyle`  | **RN Only**: 货币符号的额外 `TextStyle` 对象                             | `TextStyle`                              | -         |
| `integerColor`     | 整数部分的颜色                                                         | `string`                                 | -         |
| `integerFontSize`  | 整数部分的字体大小                                                     | `number`                                 | -         |
| `integerTextStyle` | **RN Only**: 整数部分的额外 `TextStyle` 对象                             | `TextStyle`                              | -         |
| `decimalColor`     | 小数部分的颜色                                                         | `string`                                 | -         |
| `decimalFontSize`  | 小数部分的字体大小                                                     | `number`                                 | -         |
| `decimalTextStyle` | **RN Only**: 小数部分的额外 `TextStyle` 对象                             | `TextStyle`                              | -         |
| `strikethrough`    | 是否应用删除线效果                                                     | `boolean`                                | `false`   |
| `className`        | **Web/Taro Only**: 自定义根元素类名                                      | `string`                                 | -         |
| `style`            | **Web/Taro Only**: 自定义根元素内联样式 `React.CSSProperties`              | `React.CSSProperties`                    | -         |
| `containerStyle`   | **RN Only**: 自定义根元素 `ViewStyle` 对象                               | `ViewStyle`                              | -         |

## 注意事项

- 所有尺寸相关的属性（`symbolFontSize`、`integerFontSize`、`decimalFontSize`）统一传入数值，不带单位。
- 单位转换逻辑由基础组件内部处理，在 Web 环境中会自动添加 `px` 单位，在 RN 环境中直接使用数值。
- `symbolTextStyle`, `integerTextStyle`, `decimalTextStyle` 是 React Native 特有的 Props，用于传递更复杂的文本样式对象。
- Web/Taro 环境的根节点样式通过 `className` 和 `style` 控制。
- React Native 环境的根节点样式通过 `containerStyle` 控制。
- 删除线效果在 Web/Taro 上通过 CSS `text-decoration` 应用于父元素，在 RN 上通过 `textDecorationLine` 应用于各个 `Text` 子组件。 