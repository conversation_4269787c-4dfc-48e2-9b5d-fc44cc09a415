import { Input } from '@tarojs/components'
import { forwardRef, useImperativeHandle, useState } from 'react'

const TextInput = (props, ref) => {
    const { className, onChangeText, value, placeHolder, onSubmitEditing, ...others } = props
    const [focus, setFocus] = useState(true)
    const onInput = (e) => {
        onChangeText(e.detail.value)
    }

    useImperativeHandle(ref, () => {
        return {
            focus: () => {
                if (focus !== true) {
                    setFocus(true)
                }
            },
            blur: () => {
                if (focus !== false) {
                    setFocus(false)
                }
            }
        }
    })

    return (
        <Input ref={ref} onInput={onInput} focus={focus} className={className} type='text' placeholder={placeHolder} value={value} onConfirm={onSubmitEditing} {...others}/>
    )
}

export default forwardRef(TextInput);

