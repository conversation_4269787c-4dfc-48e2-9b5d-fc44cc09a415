import * as R from 'ramda';
import { TagProps, TagPosition } from './types';

/**
 * 处理标签数据，确保是数组格式
 * @param tag - 标签或标签数组
 * @param tagPosition - 默认标签位置
 * @returns 处理后的标签数组
 */
export const processTags = R.curry((tag: TagProps | TagProps[] | undefined, tagPosition: TagPosition): TagProps[] => {
  // 使用 R.ifElse 代替 if-else 结构
  return R.pipe(
    // 检查是否为空
    R.ifElse(
      R.isNil,
      R.always([]),
      // 转换为数组
      R.ifElse(
        Array.isArray,
        R.identity,
        (t) => [t]
      )
    ),
    // 确保每个标签都有位置属性
    R.map(R.mergeRight({ position: tagPosition }))
  )(tag);
});

/**
 * 验证标签位置是否有效
 * @param position - 位置字符串
 * @returns 有效的标签位置
 */
// 定义有效的位置常量
const VALID_POSITIONS = ['top', 'right', 'bottom', 'left', 'topLeft', 'topRight', 'bottomLeft', 'bottomRight'] as const;

/**
 * 验证标签位置是否有效
 * @param position - 位置字符串
 * @returns 有效的标签位置
 */
export const validatePosition = (position: string): TagPosition => {
  return R.ifElse(
    (pos) => R.includes(pos, VALID_POSITIONS),
    (pos) => pos as TagPosition,
    R.always('top' as TagPosition)
  )(position);
};

/**
 * 创建基础样式对象
 * @param size - 尺寸
 * @returns 基础样式对象
 */
const createBaseSizeStyle = (size: number) => ({
  width: size,
  height: size
});

/**
 * 获取容器样式
 * @param size - 容器尺寸
 * @param style - 自定义样式
 * @returns 合并后的样式对象
 */
export const getContainerStyle = (size: number, style: any = {}) => {
  const defaultStyle = R.pipe(
    createBaseSizeStyle,
    R.assoc('position', 'relative')
  )(size);
  
  return R.mergeRight(defaultStyle, style);
};

/**
 * 获取图片样式
 * @param size - 图片尺寸
 * @param borderRadius - 边框圆角
 * @param imageStyle - 自定义图片样式
 * @returns 合并后的图片样式
 */
export const getImageStyle = (size: number, borderRadius: number | undefined, imageStyle: any = {}) => {
  // 使用函数式方式计算默认边框圆角
  const calcBorderRadius = R.defaultTo(size / 2);
  
  const defaultStyle = R.pipe(
    createBaseSizeStyle,
    R.assoc('borderRadius', calcBorderRadius(borderRadius))
  )(size);
  
  return R.mergeRight(defaultStyle, imageStyle);
};

