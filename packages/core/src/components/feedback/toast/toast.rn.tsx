import { JDToast } from '@jdreact/jdreact-core-lib';
import withClassName from '../../basic/utils/withClassName.rn';
import { useScreen } from '../../../hooks/useScreen';

export default withClassName()((props: any) => {
  const { style, ...others } = props;
  const { scaleStyle } = useScreen();
  
  // 对style应用自动缩放（只处理对象类型的style）
  const scaledStyle = style && typeof style === 'object' && !Array.isArray(style) 
    ? scaleStyle(style as Record<string, any>) 
    : style;
  
  return <JDToast style={scaledStyle} {...others} />;
});
