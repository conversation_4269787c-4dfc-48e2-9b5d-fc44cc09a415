import { RuleTester } from '@typescript-eslint/utils/dist/eslint-utils';
import rule from '../../rules/no-fixed-position';

const ruleTester = new RuleTester({
  parser: require.resolve('@typescript-eslint/parser'),
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
});

ruleTester.run('no-fixed-position', rule, {
  valid: [
    {
      code: `
        import { StyleSheet } from 'react-native';
        const styles = StyleSheet.create({
          container: {
            position: 'absolute',
            top: 0,
            left: 0,
          },
        });
      `,
    },
    {
      code: `
        <View style={{ position: 'absolute' }} />
      `,
    },
  ],
  invalid: [
    {
      code: `
        import { StyleSheet } from 'react-native';
        const styles = StyleSheet.create({
          container: {
            position: 'fixed',
            top: 0,
            left: 0,
          },
        });
      `,
      errors: [{ messageId: 'noFixedPosition' }],
      output: `
        import { StyleSheet } from 'react-native';
        const styles = StyleSheet.create({
          container: {
            position: 'absolute',
            top: 0,
            left: 0,
          },
        });
      `,
    },
    {
      code: `
        <View style={{ position: 'fixed' }} />
      `,
      errors: [{ messageId: 'noFixedPosition' }],
      output: `
        <View style={{ position: 'absolute' }} />
      `,
    },
  ],
});