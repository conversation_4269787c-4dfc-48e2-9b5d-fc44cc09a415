import { usePrevious } from "../../../hooks/hooks";
import isEqual from 'lodash-es/isEqual'
import { forwardRef, useMemo, useRef } from 'react';
import { StyleSheet } from 'react-native';

/**
 * 高阶组件，用于处理样式变化, 只适用于React-Native组件
 * @param WrappedComponent 被包裹的组件
 * @returns 包装后的组件
 */
export default function withClassName() {
    return function (WrappedComponent) {
        return forwardRef<any, any>(function (props, ref) {
            const { style, ...restProps } = props;

            const refPrevStyle = usePrevious(style)

            const refDep = useRef<any[]>([])
            const isEqualStyle = isEqual(style, refPrevStyle)
            // if(props.isTest) {
            //     console.log('refPrevStyle, style', refPrevStyle, style, isEqualStyle)
            // }
            if (!isEqualStyle) { refDep.current = [Date.now()] }

            const { styleProps, textProps } = useMemo(() => {
                if (!style) return { styleProps: style, textProps: {} };
                
                // 处理 textOverflow 和 whiteSpace 属性
                const { 
                    textOverflow, 
                    whiteSpace, 
                    ...restStyle 
                } = StyleSheet.flatten(style) || {};
                
                // 处理文本溢出
                const ellipsizeMode = textOverflow === 'ellipsis' ? 'tail' : undefined;
                
                // 处理 whiteSpace
                const newTextProps: any = {};
                if (ellipsizeMode) {
                    newTextProps.ellipsizeMode = ellipsizeMode;
                }
                
                // 如果 whiteSpace 是 nowrap，则设置 numberOfLines 为 1
                if (whiteSpace === 'nowrap') {
                    newTextProps.numberOfLines = 1;
                }
                
                return {
                    styleProps: restStyle,
                    textProps: newTextProps
                };
            }, [refDep.current]);

            return (
                <WrappedComponent 
                    ref={ref} 
                    {...restProps} 
                    {...textProps}
                    style={styleProps} 
                />
            );
        });
    };
}
