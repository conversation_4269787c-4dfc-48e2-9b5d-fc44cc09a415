# 📦 包管理指南

## 🚀 快速开始

### 安装依赖
```bash
# 安装所有依赖
yarn install

# 清理并重新安装
yarn clean && yarn install
```

### 构建
```bash
# 构建所有包
yarn build

# 构建特定包
yarn build:core
yarn build:shared
yarn build:takeout
yarn build:groupon
yarn build:travel
yarn build:instant
```

## 📋 开发工作流

### 1. 添加新依赖
```bash
# 为特定包添加依赖
yarn workspace @jd/lifeui-core add react@^18.0.0
yarn workspace @jd/lifeui-business-shared add lodash

# 添加开发依赖到根目录
yarn add -D -W typescript@^5.0.0

# 添加 peer 依赖
yarn workspace @jd/lifeui-core add --peer react@^18.0.0
```

### 2. 开发模式
```bash
# 启动开发服务器
yarn dev:core
yarn dev:shared
# ... 其他包
```

### 3. 测试
```bash
# 运行所有测试
yarn test

# 运行特定包的测试
yarn workspace @jd/lifeui-core test
```

### 4. 代码检查
```bash
# 运行 ESLint
yarn lint

# 运行 Stylelint
yarn workspace @jd/lifeui-core stylelint "src/**/*.scss"
```

## 🔖 版本管理

### 查看变更
```bash
# 查看哪些包有变更
npx lerna changed

# 查看变更详情
npx lerna diff
```

### 版本升级
```bash
# 交互式版本升级
npx lerna version

# 预发布版本
npx lerna version prerelease --preid beta

# 指定版本类型
npx lerna version patch   # 1.0.0 -> 1.0.1
npx lerna version minor   # 1.0.0 -> 1.1.0
npx lerna version major   # 1.0.0 -> 2.0.0
```

## 📤 发布管理

### 发布到 npm
```bash
# 发布所有变更的包
yarn publish:all

# 发布特定包
yarn publish:core
yarn publish:shared
# ... 其他包

# 发布预发布版本
npx lerna publish --dist-tag beta
```

### 发布前检查
```bash
# 检查包内容
npx lerna exec -- npm pack --dry-run

# 检查发布配置
npx lerna exec -- npm config list
```

## 🔧 包依赖管理

### 内部包依赖
```bash
# 添加内部包依赖
yarn workspace @jd/lifeui-business-shared add @jd/lifeui-core@^1.0.0

# 更新内部包依赖
npx lerna exec -- yarn upgrade @jd/lifeui-core
```

### 依赖分析
```bash
# 查看依赖树
yarn workspace @jd/lifeui-core list --depth=0

# 查看过时的依赖
yarn workspace @jd/lifeui-core outdated
```

## 🧹 清理和维护

### 清理
```bash
# 清理 node_modules
yarn clean

# 清理构建产物
npx lerna exec -- rm -rf dist

# 清理并重新安装
yarn clean && yarn install
```

### 更新依赖
```bash
# 更新所有依赖
npx lerna exec -- yarn upgrade

# 更新特定依赖
npx lerna exec -- yarn upgrade react@latest
```

## 🔒 安全管理

### 安全审计
```bash
# 安全审计
yarn audit

# 修复安全问题
yarn audit --fix
```

## 📊 包大小分析

### 分析包大小
```bash
# 安装分析工具
yarn add -D -W bundlephobia-cli

# 分析包大小
npx bundlephobia @jd/lifeui-core
```

## 🚨 常见问题

### 1. 依赖冲突
```bash
# 查看重复依赖
yarn list --pattern "react"

# 解决方案：使用 resolutions
# 在 package.json 中添加：
{
  "resolutions": {
    "react": "^18.0.0"
  }
}
```

### 2. 构建失败
```bash
# 清理并重新构建
yarn clean
yarn install
yarn build
```

### 3. 发布权限问题
```bash
# 登录 npm
npm login

# 检查权限
npm whoami
npm access list packages @jd
```
