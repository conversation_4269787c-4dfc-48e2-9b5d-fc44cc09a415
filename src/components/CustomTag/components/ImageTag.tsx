import React from 'react';
import View from '../../basic/View';
import Image from '../../basic/Image';
import { CSSProperties } from 'react';
import { TagType } from '../types';

/**
 * 图片标签属性接口
 */
export interface ImageTagProps {
  /** 图片URL */
  tagImage: string;
  /** 图片样式 */
  tagImageStyle?: CSSProperties;
  /** 标签类名 */
  className?: string;
  /** 标签样式 */
  style?: CSSProperties;
}

/**
 * 图片标签组件
 * @param props 组件属性
 */
const ImageTag: React.FC<ImageTagProps> = ({
  tagImage,
  tagImageStyle,
  className,
  style,
  ...props
}) => {
  // 构建类名
  const tagClassName = [
    'custom-tag',
    `custom-tag--${TagType.IMAGE}`,
    className
  ].filter(Boolean).join(' ');

  return (
    <View
      className={tagClassName}
      style={style}
      {...props}
    >
      <Image
        className="custom-tag__image"
        style={tagImageStyle}
        src={tagImage}
        mode="aspectFit"
      />
    </View>
  );
};

export default ImageTag;
