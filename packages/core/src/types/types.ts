/**
 * Screen Scale 类型定义
 */

/**
 * 设计值类型（与项目全局类型保持一致）
 */
export type DesignValue = number | string | { 
  mobile: number | string; 
  pad: number | string | (number | string)[] 
};

/**
 * 屏幕信息接口
 */
export interface ScreenInfo {
  /** 屏幕宽度 */
  width: number;
  /** 屏幕高度 */
  height: number;
  /** 是否为平板设备 */
  isPad: boolean;
  /** 屏幕缩放方法（直接使用ToolLibs/device的scaleByScreen） */
  scaleByScreen: (value: DesignValue, forceScale?: boolean) => number;
  /** 样式对象缩放方法，自动识别并缩放尺寸相关属性 */
  scaleStyle: (style: Record<string, any>) => Record<string, any>;
} 