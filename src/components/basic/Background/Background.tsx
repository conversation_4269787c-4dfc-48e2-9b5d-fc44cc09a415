import React from 'react';
import View from '../../basic/View';
import Image from '../../basic/Image';
import { BackgroundProps, colorGroupStartPositionEnum } from './types';
import './Background.scss';

let LinearGradient: any;
if (process.env.TARO_ENV === 'rn') {
  LinearGradient = require('react-native-linear-gradient').default;
}

const angleToRNPoints = {
  LEFT:   { start: { x: 1, y: 0.5 }, end: { x: 0, y: 0.5 } },
  TOP:    { start: { x: 0.5, y: 1 }, end: { x: 0.5, y: 0 } },
};

const Background: React.FC<BackgroundProps> = ({
  backgroundWidth,
  backgroundHeight,
  backgroundColor = ['#E1251B'],
  backgroundImageURL,
  colorGroupStartPosition = 'LEFT',
  gradientConnectionEffects = 'TRANSITION',
  gradientPercent = 50,
  backgroundBorderRadius = 0,
  className
}) => {

  // 防止传入空
  backgroundColor?.length === 0 ? backgroundColor = ['#E1251B'] : backgroundColor;

  // 
  const rootClassName = [
    'background',
    className
  ].filter(Boolean).join(' ');

  const rootStyle: React.CSSProperties = {
    width: backgroundWidth,
    height: backgroundHeight,
    borderRadius: backgroundBorderRadius,
  };

  // 
  let getGradientAngle = colorGroupStartPositionEnum[colorGroupStartPosition];
  // const gradientStyle: React.CSSProperties = {
  //   background: `linear-gradient(${getGradientAngle}, ${backgroundColor[0]} 0, ${backgroundColor[0]} ${gradientPercent}%, ${backgroundColor[1]} ${100 - gradientPercent}%, ${backgroundColor[1]} 100%)`
  // };
  let gradientStyle: React.CSSProperties = gradientConnectionEffects === 'TRANSITION' ? {
    background: `linear-gradient(${getGradientAngle}, ${backgroundColor[0]} 0, ${backgroundColor[0]} ${gradientPercent}%, ${backgroundColor[1]} ${100 - gradientPercent}%, ${backgroundColor[1]} 100%)`
  } : {
    background: `linear-gradient(${getGradientAngle}, ${backgroundColor[0]} 0, ${backgroundColor[0]} ${gradientPercent}%, ${backgroundColor[1]} ${gradientPercent}%, ${backgroundColor[1]} 100%)`
  }

  if(gradientConnectionEffects === 'TRANSITION' && gradientPercent === 50) {
    gradientStyle = {
      background: `linear-gradient(${getGradientAngle}, ${backgroundColor[0]} 0, ${backgroundColor[1]} 100%)`
    }
  }

  return (
    <>
      <View className={rootClassName} style={rootStyle}>
        {backgroundColor?.length === 1 ? (
          <View className='background__item' style={{backgroundColor: backgroundColor[0]}} />
        ) : (
          process.env.TARO_ENV === 'rn' && LinearGradient ? (
            <View>
              <LinearGradient
                style={{ width: '100%', height: '100%' }}
                colors={[backgroundColor[1], backgroundColor[0]]}
                locations={gradientPercent ? [0, gradientPercent / 100] : undefined}
                start={angleToRNPoints[colorGroupStartPosition]?.start || { x: 1, y: 0.5 }}
                end={angleToRNPoints[colorGroupStartPosition]?.end || { x: 0, y: 0.5 }}
              />
            </View>
          ) : (
            <View className='background__item' style={gradientStyle} />
          )
        )}
        {backgroundImageURL && (
          <View className='background__item' style={{zIndex: 1}}>
            <Image
              src={backgroundImageURL}
              mode='aspectFit'
              style={{
                width: backgroundWidth ? backgroundWidth : '100%',
                height:  backgroundHeight ? backgroundHeight : '100%',
              }}
            />
          </View>
        )}
      </View>
    </>
  )
};

export default Background;