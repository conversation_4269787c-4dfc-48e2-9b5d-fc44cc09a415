# Price 价格组件

基于BasePrice组件封装的价格展示组件，支持两种展示样式：默认样式和促销样式。

## 使用场景

适用于电商场景中价格的展示，可以同时展示当前价格和原价，支持普通价格和促销价格两种展示形式。

## 样式示例

### 默认样式
当前价格红色，原价灰色带删除线
![默认样式展示](示例图片路径)

### 促销样式 
红底白字当前价格 + 闪电图标 + 红色原价，适用于促销场景
![促销样式展示](示例图片路径)

## 代码演示

```tsx
import { Price } from '@/components/display';

// 默认样式 - 红色当前价格和灰色原价
<Price 
  currentPrice={240} 
  originalPrice={1080} 
  type="default"
/>

// 促销样式 - 红底白字价格和红色原价
<Price 
  currentPrice={199} 
  originalPrice={399} 
  type="discount"
  lightningIconSrc="path/to/lightning-icon.svg"
/>

// 自定义字体大小和颜色
<Price 
  currentPrice={240} 
  originalPrice={1080} 
  type="default"
  currentPriceColor="#0066cc"
  currentPriceFontSize={30}
  currentPriceSymbolFontSize={20}
  originalPriceColor="#666666"
  originalPriceFontSize={16}
/>
```

## API

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| currentPrice | 当前价格 | `number` | - |
| originalPrice | 原价，如果提供则显示原价 | `number` | - |
| type | 价格展示样式类型 | `'default' \| 'discount'` | `'default'` |
| currencySymbol | 货币符号 | `string` | `'¥'` |
| decimalPlaces | 小数位数 | `0 \| 1 \| 2` | `2` |
| strikethrough | 是否给原价添加删除线 | `boolean` | `true` |
| currentPriceColor | 当前价格数字的颜色 | `string` | 根据type自动设置 |
| currentPriceFontSize | 当前价格的字体大小 | `number` | 根据type自动设置 |
| currentPriceSymbolFontSize | 当前价格货币符号的字体大小 | `number` | 根据type自动设置 |
| currentPriceDecimalFontSize | 当前价格小数部分的字体大小 | `number` | 根据type自动设置 |
| originalPriceColor | 原价数字的颜色 | `string` | 根据type自动设置 |
| originalPriceFontSize | 原价的字体大小 | `number` | 根据type自动设置 |
| originalPriceSymbolFontSize | 原价货币符号的字体大小 | `number` | 根据type自动设置 |
| originalPriceDecimalFontSize | 原价小数部分的字体大小 | `number` | 根据type自动设置 |
| lightningIconSrc | 促销类型中闪电图标的路径 | `string` | - |
| lightningIconWidth | 促销类型中闪电图标的宽度 | `number` | `20` |
| lightningIconHeight | 促销类型中闪电图标的高度 | `number` | `20` |
| className | 自定义根元素类名 (Taro/Web) | `string` | - |
| style | 自定义根元素内联样式 (Taro/Web) | `React.CSSProperties` | - |
| containerStyle | 自定义根元素样式 (RN) | `ViewStyle` | - |

## 注意事项

- **React Native 环境**：
  - `className` prop 无效。
  - `