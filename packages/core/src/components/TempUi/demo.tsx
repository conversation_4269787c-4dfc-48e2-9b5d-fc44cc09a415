import React from 'react';
import { View } from '@tarojs/components';
import Temp<PERSON> from './TempUi';

const styles = {
  container: {
    minHeight: '100vh',
    backgroundColor: '#f7f8fa',
  },
  header: {
    position: 'fixed' as const,
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
    height: '56px',
    backgroundColor: '#fff',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
  },
  headerTitle: {
    fontSize: '18px',
    fontWeight: 'bold' as const,
    textAlign: 'center' as const,
  },
  content: {
    padding: '16px',
    paddingTop: '72px',
    paddingBottom: '40px',
  },
  section: {
    marginBottom: '24px',
  },
  sectionTitle: {
    fontSize: '16px',
    color: '#666',
    margin: '16px 0 12px 4px',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: '12px',
    padding: '20px',
    marginBottom: '16px',
  },
  cardTitle: {
    fontSize: '14px',
    color: '#999',
    marginBottom: '12px',
  },
  customTemp: {
    padding: '8px 16px',
    background: '#f5f5f5',
    borderRadius: '4px',
  },
};

export default function TempUIDemo() {
  return (
    <View style={styles.container}>
      {/* 顶部标题栏 */}
      <View style={styles.header}>
        <View style={styles.headerTitle}>TempUI</View>
      </View>

      {/* 内容区域 */}
      <View style={styles.content}>
        {/* 基础用法 */}
        <View style={styles.section}>
          <View style={styles.sectionTitle}>基础用法</View>
          <View style={styles.card}>
            <View style={styles.cardTitle}>默认样式</View>
            <TempUI>基础内容</TempUI>
          </View>
        </View>

        {/* 自定义样式 */}
        <View style={styles.section}>
          <View style={styles.sectionTitle}>自定义样式</View>
          <View style={styles.card}>
            <View style={styles.cardTitle}>自定义样式</View>
            <TempUI style={{ color: 'red', backgroundColor: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
              自定义样式内容
            </TempUI>
          </View>
        </View>
      </View>
    </View>
  );
} 