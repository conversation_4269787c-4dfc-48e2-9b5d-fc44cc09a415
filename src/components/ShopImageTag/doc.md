# ShopImageTag 商品图片标签组件

用于在商品图片上添加各种位置的标签，支持文本、图片和图标标签。

## 基础用法

```jsx
<ShopImageTag 
  src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
  size={100}
  tag={{
    tagComponent: <CustomTag
      type={TagType.TEXT}
      text="热卖"
      backgroundColor="#ff4d4f"
      textStyle={{ color: '#fff' }}
    />,
    position: 'topLeft'
  }}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| src | 图片资源地址 | `string` | - |
| size | 图片尺寸（宽高相等） | `number` | `64` |
| style | 图片容器自定义样式 | `CSSProperties` (Taro)<br/>`StyleProp<ViewStyle>` (RN) | `{}` |
| imageStyle | 图片自定义样式 | `CSSProperties` (Taro)<br/>`StyleProp<ImageStyle>` (RN) | `{}` |
| tag | 标签配置，可以是单个标签或标签数组 | `TagProps \| TagProps[]` | - |
| tagPosition | 默认标签位置，当标签未指定位置时使用 | `TagPosition` | `'top'` |
| tagStyle | 所有标签的共享样式 | `CSSProperties` (Taro)<br/>`StyleProp<ViewStyle>` (RN) | `{}` |
| textStyle | 所有标签文本的共享样式 | `CSSProperties` (Taro)<br/>`StyleProp<TextStyle>` (RN) | `{}` |
| borderRadius | 图片圆角半径 | `number` | `0` |
| className | 自定义类名 (Taro/Web) | `string` | `''` |
| onError | 图片加载失败回调 | `() => void` | - |
| onLoad | 图片加载成功回调 | `() => void` | - |

### TagProps

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| tagComponent | 自定义标签组件 | `ReactNode` | - |
| position | 标签位置 | `TagPosition` | 继承父组件的 `tagPosition` |
| style | 标签容器自定义样式 | `CSSProperties` (Taro)<br/>`StyleProp<ViewStyle>` (RN) | `{}` |

### TagPosition

标签位置可选值：`'top'` | `'right'` | `'bottom'` | `'left'` | `'topLeft'` | `'topRight'` | `'bottomLeft'` | `'bottomRight'`

## 示例

### 基础图片标签

```jsx
<ShopImageTag 
  src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
  size={100}
  tag={{
    tagComponent: <CustomTag
      type={TagType.TEXT}
      text="热卖"
      backgroundColor="#ff4d4f"
      textStyle={{ color: '#fff' }}
    />,
    position: 'topLeft'
  }}
/>
```

### 多个标签位置示例

```jsx
<ShopImageTag 
  src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
  size={120}
  tag={[
    { 
      tagComponent: <CustomTag
        type={TagType.TEXT}
        text="限时"
        backgroundColor="#ff4d4f"
        textStyle={{ color: '#fff' }}
      />,
      position: 'topLeft'
    },
    { 
      tagComponent: <CustomTag
        type={TagType.TEXT}
        text="新品"
        backgroundColor="#52c41a"
        textStyle={{ color: '#fff' }}
      />,
      position: 'topRight'
    },
    { 
      tagComponent: <CustomTag
        type={TagType.TEXT}
        text="优惠"
        backgroundColor="#faad14"
        textStyle={{ color: '#fff' }}
      />,
      position: 'bottomLeft'
    },
    { 
      tagComponent: <CustomTag
        type={TagType.TEXT}
        text="爆款"
        backgroundColor="#1890ff"
        textStyle={{ color: '#fff' }}
      />,
      position: 'bottomRight'
    }
  ]}
/>
```

### 图标标签示例

```jsx
<ShopImageTag 
  src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
  size={100}
  tag={{
    tagComponent: <CustomTag
      type={TagType.ICON_PREFIX}
      text="官方"
      prefixIcon="https://img12.360buyimg.com/imagetools/jfs/t1/180776/26/8417/1985/60c33124E7e26e4a7/05d5c0d0df6e8a6a.png"
      backgroundColor="#fff0f0"
      textStyle={{ color: '#e93b3d' }}
    />,
    position: 'bottom'
  }}
/>
```

### 更多标签类型示例

```jsx
<View style={{ display: 'flex', flexWrap: 'wrap', justifyContent: 'space-around' }}>
  <ShopImageTag 
    src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
    size={80}
    tag={{
      tagComponent: <CustomTag 
        type={TagType.IMAGE}
        tagImage="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
      />,
      position: 'topLeft'
    }}
  />
  
  <ShopImageTag 
    src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
    size={80}
    tag={{
      tagComponent: <CustomTag 
        type={TagType.ICON_SUFFIX}
        text="超值" 
        suffixIcon="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
        backgroundColor="#e6f7ff"
        textStyle={{ color: '#1890ff' }}
      />,
      position: 'topRight'
    }}
  />
</View>
```
