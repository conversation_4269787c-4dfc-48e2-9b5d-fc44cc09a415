import React from 'react';
import {View} from '../../../../core/src/components/basic/View';
import {Image} from '../../../../core/src/components/basic/Image';
import { Title } from '../../../../core/src/components/display/title';
import { CustomTag } from '../../../../core/src/components/display/CustomTag';
import { TagType } from '../../../../core/src/components/display/CustomTag/types';
import { 
  ShopHeaderProps, 
  LineConfig,
  ContentType,
  Content,
  TitleContent,
  TagContent
} from './types';
import './shopHeader.scss';

// 标题默认样式
const defaultTitleStyle = {
  flexShrink: 0,
  width: 263,
  fontSize: 15,
  fontFamily: 'PingFang SC',
  fontWeight: 600,
  color: '#1A1A1A',
  textAlign: 'left' as const
};

// 标签默认样式
const defaultTagStyle = {
  fontSize: 11,
  fontFamily: 'PingFang SC',
  color: '#8C8C8C',
};

/**
 * 渲染内容 - 根据类型渲染组件
 */
const renderContent = (content?: Content) => {
  if (!content) return null;
  
  // 如果是React节点，直接渲染
  if (React.isValidElement(content)) {
    return content;
  }
  
  // 如果是对象，根据类型渲染
  if (typeof content === 'object' && 'type' in content) {
    const typedContent = content as (TitleContent | TagContent);

    switch (typedContent.type) {
      case ContentType.TITLE:
        const titleContent = typedContent as TitleContent;
        
        // 合并样式和默认样式
        const mergedStyle = {
          ...defaultTitleStyle,
          ...(titleContent.style || {})
        };
        
        // 转换标签为Title组件期望的格式
        const formattedTags = titleContent.tags?.map(tag => ({
          type: TagType.TEXT, // 默认使用TEXT类型
          text: tag.text,
          style: { backgroundColor: tag.backgroundColor },
          textStyle: tag.textStyle
        }));
        
        // 构建Title组件属性
        const titleProps = {
          fontSize: titleContent.fontSize || defaultTitleStyle.fontSize,
          style: mergedStyle,
          className: titleContent.className,
          textAlign: titleContent.textAlign,
          lines: 1,
          tags: formattedTags,
          ...(titleContent.props || {})
        };
        console.log("titleProps===>",titleProps,titleContent);
        return (
          <Title {...titleProps}>
            {titleContent.text}
          </Title>
        );
      
      case ContentType.TAG:
        const tagContent = typedContent as TagContent;
        console.log("titleProps===>222",tagContent.textStyle);

        // 合并标签文本样式
        const mergedTagTextStyle = {
          fontSize: defaultTagStyle.fontSize,
          fontFamily: defaultTagStyle.fontFamily,
          color: defaultTagStyle.color,
          ...(tagContent.textStyle || {})
        };
        
        // 确定标签类型
        let tagType = TagType.TEXT; // 默认为TEXT类型
        if (tagContent.tagImage) {
          tagType = TagType.IMAGE;
        } else if (tagContent.icon) {
          tagType = TagType.ICON_PREFIX; // 兼容旧版本的icon属性
        }
        
        // 构建标签组件属性
        const tagProps = {
          type: tagType,
          text: tagContent.text,
          textStyle: mergedTagTextStyle,
          className: tagContent.className,
          style: tagContent.style,
          tagImage: tagContent.tagImage,
          prefixIcon: tagContent.icon, // 兼容旧版本
          ...(tagContent.props || {})
        }; 
        return <CustomTag {...tagProps} />;
        
      default:
        return null;
    }
  }
  
  // 其他情况返回null
  return null;
};

/**
 * 渲染信息行项目
 */
const renderInfoItems = (items?: Content[], containerClassName: string = 'shop-header__slot') => {
  if (!items || items.length === 0) {
    return null;
  }

  return (
    <View className={containerClassName}>
      {items.map((item, index) => {
        if (!item) return null;
        
        // 判断内容类型是否为Title
        const isTitle = typeof item === 'object' && 
                        !React.isValidElement(item) && 
                        'type' in item && 
                        (item as any).type === ContentType.TITLE;
        
        return (
          <View key={index} className={isTitle ? "shop-header__titleitem" : "shop-header__item"}>
            {renderContent(item)}
          </View>
        );
      })}
    </View>
  );
};

/**
 * 店铺头部信息组件 - 支持Taro/RN平台
 */
const ShopHeader: React.FC<ShopHeaderProps> = (props) => {
  const {
    imageUrl,
    imageWidth = 72,
    imageHeight = 72,
    lines = [],
    lineSpacing = 2,
    lineHeight,
    className = '',
    style = {},
    imageStyle,
    onClick,
  } = props;
  // 基础行样式，基于lineSpacing
  const getRowStyle = (lineConfig?: LineConfig) => {
    const rowStyle: Record<string, any> = {
      marginTop: lineSpacing, //暂时写死不需要间距
    };
    
    // 如果有指定高度，添加到样式中
    if (lineConfig?.height || lineHeight) {
      rowStyle.height = lineConfig?.height || lineHeight;
    }
    
    return rowStyle;
  };

  return (
    <View className={`shop-header ${className}`} style={style as React.CSSProperties} onClick={onClick}>
      {/* 左侧店铺图片 */}
      <View className="shop-header__image-container" style={{ width: imageWidth, height: imageHeight }}>
        <Image 
          className="shop-header__image"
          src={imageUrl}
          style={imageStyle as any}
        />
      </View>
      
      {/* 右侧信息内容 */}
      <View className="shop-header__content">
        {/* 使用lines数组 */}
        {lines.map((lineConfig, index) => (
          <View 
            key={index}
            className={index === 0 ? 'shop-header__title-row' : 'shop-header__info-row'}
            style={index === 0 ? undefined : getRowStyle(lineConfig)}
          >
            {renderInfoItems(lineConfig.left, 'shop-header__slot-left')}
            {renderInfoItems(lineConfig.right, 'shop-header__slot-right')}
          </View>
        ))}
      </View>
    </View>
  );
};

export default ShopHeader; 