import textMustBeInTextComponent from './rules/text-must-be-in-text-component';
import noStyleInheritance from './rules/no-style-inheritance';
import requireImageSize from './rules/require-image-size';
import noUnsupportedStyleProps from './rules/no-unsupported-style-props';
import noInlineStyles from './rules/no-inline-styles';
import explicitFlexDirection from './rules/explicit-flex-direction';
import noFixedPosition from './rules/no-fixed-position';
import noUnsupportedSelectors from './rules/no-unsupported-selectors';

export = {
  rules: {
    'text-must-be-in-text-component': textMustBeInTextComponent,
    'no-style-inheritance': noStyleInheritance,
    'require-image-size': requireImageSize,
    'no-unsupported-style-props': noUnsupportedStyleProps,
    'no-inline-styles': noInlineStyles,
    'explicit-flex-direction': explicitFlexDirection,
    'no-fixed-position': noFixedPosition,
    'no-unsupported-selectors': noUnsupportedSelectors,
  },
  configs: {
    recommended: {
      plugins: ['taro-rn-best-practices'],
      rules: {
        'taro-rn-best-practices/text-must-be-in-text-component': 'error',
        'taro-rn-best-practices/no-style-inheritance': 'warn',
        'taro-rn-best-practices/require-image-size': 'error',
        'taro-rn-best-practices/no-unsupported-style-props': 'error',
        'taro-rn-best-practices/no-inline-styles': 'warn',
        'taro-rn-best-practices/explicit-flex-direction': 'warn',
        'taro-rn-best-practices/no-fixed-position': 'error',
        'taro-rn-best-practices/no-unsupported-selectors': 'error',
      },
    },
  },
};