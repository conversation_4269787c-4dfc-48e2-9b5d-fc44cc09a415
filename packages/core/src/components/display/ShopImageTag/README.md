# ShopLogo 商店Logo组件

一个支持在上下左右添加标签的商店Logo组件，适用于React Native和Taro项目。

## 功能特点

- 支持在Logo的上、下、左、右四个位置添加标签
- 支持同时添加多个标签
- 支持自定义Logo大小和形状
- 支持自定义标签样式和位置
- 函数式设计，使用Ramda优化代码

## 安装依赖

```bash
# React Native版本依赖
npm install ramda

# Taro版本依赖
npm install ramda @tarojs/components
```

## 使用方法

### React Native版本

```tsx
import React from 'react';
import { View } from 'react-native';
import ShopLogo from 'path/to/components/shopLogo';

const MyComponent = () => {
  return (
    <View style={{ padding: 20 }}>
      {/* 基本用法 */}
      <ShopLogo 
        source={require('../../assets/logo.png')} 
        size={80} 
      />
      
      {/* 添加单个标签 */}
      <ShopLogo 
        source={require('../../assets/logo.png')} 
        size={80}
        tag={{
          text: "品牌",
          position: "top",
          backgroundColor: "#FF5B00"
        }} 
      />
      
      {/* 添加多个标签 */}
      <ShopLogo 
        source={require('../../assets/logo.png')} 
        size={80}
        tag={[
          {
            text: "4周年",
            position: "top",
            backgroundColor: "#FF5B00"
          },
          {
            text: "放肆玩",
            position: "right",
            backgroundColor: "#00A0FF"
          }
        ]} 
      />
    </View>
  );
};

export default MyComponent;
```

### Taro版本

```tsx
import React from 'react';
import { View } from '@tarojs/components';
import ShopLogo from 'path/to/components/shopLogo';

const MyComponent = () => {
  return (
    <View style={{ padding: 20 }}>
      {/* 基本用法 */}
      <ShopLogo 
        source="https://example.com/logo.png" 
        size={80} 
      />
      
      {/* 添加单个标签 */}
      <ShopLogo 
        source="https://example.com/logo.png" 
        size={80}
        tag={{
          text: "品牌",
          position: "top",
          backgroundColor: "#FF5B00"
        }} 
      />
      
      {/* 添加多个标签和自定义圆角 */}
      <ShopLogo 
        source="https://example.com/logo.png" 
        size={80}
        borderRadius={10}  // 设置方形圆角
        tag={[
          {
            text: "4周年",
            position: "top",
            backgroundColor: "#FF5B00"
          },
          {
            text: "放肆玩",
            position: "right",
            backgroundColor: "#00A0FF"
          }
        ]} 
      />
    </View>
  );
};

export default MyComponent;
```

## 属性说明

### ShopLogoProps

| 属性 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| source | any/string | - | 图片源，RN版本使用require引入，Taro版本使用网络图片 |
| size | number | 64 | Logo尺寸，宽高相等 |
| style | ViewStyle/CSSProperties | - | 容器样式 |
| imageStyle | ImageStyle/CSSProperties | - | 图片样式 |
| tag | TagProps/TagProps[] | - | 标签配置，可以是单个标签或多个标签数组 |
| borderRadius | number | size/2 | 边框圆角，默认为尺寸一半(圆形) |
| className | string | - | 自定义类名(仅Taro版本) |

### TagProps

| 属性 | 类型 | 默认值 | 说明 |
| --- | --- | --- | --- |
| text | string | - | 标签文本 |
| position | 'top'/'right'/'bottom'/'left' | - | 标签位置 |
| style | ViewStyle/CSSProperties | - | 标签容器样式 |
| textStyle | TextStyle/CSSProperties | - | 标签文本样式 |
| backgroundColor | string | '#FF5B00' | 标签背景色 |

## 开发团队

- UI组件开发团队

## 版本历史

- v1.0.0 (2025-05-16): 初始版本，支持RN和Taro
