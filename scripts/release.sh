#!/bin/bash

# 发布脚本
# 使用方法: ./scripts/release.sh [patch|minor|major|prerelease]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 获取版本类型参数
VERSION_TYPE=${1:-patch}

echo -e "${GREEN}🚀 开始发布流程...${NC}"

# 1. 检查工作目录是否干净
if [[ -n $(git status --porcelain) ]]; then
  echo -e "${RED}❌ 工作目录不干净，请先提交或暂存更改${NC}"
  exit 1
fi

# 2. 检查当前分支
CURRENT_BRANCH=$(git branch --show-current)
if [[ "$CURRENT_BRANCH" != "main" && "$CURRENT_BRANCH" != "master" ]]; then
  echo -e "${YELLOW}⚠️  当前不在主分支 ($CURRENT_BRANCH)，是否继续? (y/N)${NC}"
  read -r response
  if [[ ! "$response" =~ ^[Yy]$ ]]; then
    exit 1
  fi
fi

# 3. 拉取最新代码
echo -e "${GREEN}📥 拉取最新代码...${NC}"
git pull origin $CURRENT_BRANCH

# 4. 安装依赖
echo -e "${GREEN}📦 安装依赖...${NC}"
yarn install

# 5. 运行测试
echo -e "${GREEN}🧪 运行测试...${NC}"
yarn test

# 6. 运行代码检查
echo -e "${GREEN}🔍 运行代码检查...${NC}"
yarn lint

# 7. 构建所有包
echo -e "${GREEN}🔨 构建所有包...${NC}"
yarn build

# 8. 版本升级
echo -e "${GREEN}📈 升级版本 ($VERSION_TYPE)...${NC}"
npx lerna version $VERSION_TYPE --conventional-commits --yes

# 9. 发布
echo -e "${GREEN}📤 发布到 npm...${NC}"
npx lerna publish from-git --yes

echo -e "${GREEN}✅ 发布完成！${NC}"

# 10. 推送标签
echo -e "${GREEN}🏷️  推送标签到远程仓库...${NC}"
git push --follow-tags

echo -e "${GREEN}🎉 发布流程全部完成！${NC}"
