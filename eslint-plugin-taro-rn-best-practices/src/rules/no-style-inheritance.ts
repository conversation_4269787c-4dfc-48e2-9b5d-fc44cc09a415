import { ESLintUtils } from '@typescript-eslint/utils';

const createRule = ESLintUtils.RuleCreator(
  (name) => `https://docs.taro.zone/docs/react-native-remind#${name}`
);

export default createRule({
  name: 'no-style-inheritance',
  meta: {
    type: 'problem',
    docs: {
      description: 'Disallow style inheritance as it does not work in React Native.',
      recommended: 'warn',
    },
    messages: {
      noStyleInheritance: 'Style properties are not inherited in React Native. Apply styles directly to this component.',
    },
    schema: [],
  },
  defaultOptions: [],
  create(context) {
    return {
      JSXElement(node) {
        // 只检查 Text 组件
        if (
          node.openingElement.name.type !== 'JSXIdentifier' ||
          node.openingElement.name.name !== 'Text' ||
          node.children.length === 0
        ) {
          return;
        }
        // 检查当前 Text 是否有 style 属性
        const hasStyleProp = node.openingElement.attributes.some(
          (attr) =>
            attr.type === 'JSXAttribute' &&
            attr.name.name === 'style'
        );
        if (!hasStyleProp) {
          return;
        }
        // 检查直接子节点中的 Text 是否缺少 style 属性
        for (const child of node.children) {
          if (
            child.type === 'JSXElement' &&
            child.openingElement.name.type === 'JSXIdentifier' &&
            child.openingElement.name.name === 'Text'
          ) {
            const childHasStyleProp = child.openingElement.attributes.some(
              (attr) =>
                attr.type === 'JSXAttribute' &&
                attr.name.name === 'style'
            );
            if (!childHasStyleProp) {
              context.report({
                node: child,
                messageId: 'noStyleInheritance',
              });
            }
          }
        }
      },
    };
  },
});