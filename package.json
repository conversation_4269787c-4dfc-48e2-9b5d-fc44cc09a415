{"name": "@jd/lifeui", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "lerna run build --stream", "build:multi": "node scripts/build-multiplatform.js", "build:core:multi": "node scripts/build-multiplatform.js packages/core", "build:shared:multi": "node scripts/build-multiplatform.js packages/business-shared", "test": "lerna run test", "lint": "lerna run lint", "dev": "lerna run dev --parallel", "clean": "lerna clean && rm -rf node_modules", "reset": "yarn clean && yarn install", "quick": "./scripts/quick-release.sh", "quick:patch": "./scripts/quick-release.sh patch", "quick:minor": "./scripts/quick-release.sh minor", "quick:major": "./scripts/quick-release.sh major", "release": "./scripts/release.sh", "release:patch": "./scripts/release.sh patch", "release:minor": "./scripts/release.sh minor", "release:major": "./scripts/release.sh major", "release:beta": "./scripts/release.sh prerelease", "check": "./scripts/ci-check.sh", "check:changed": "lerna changed", "deps:check": "./scripts/manage-internal-deps.sh check", "deps:fix": "./scripts/fix-internal-deps.sh", "audit": "yarn audit --level moderate"}, "devDependencies": {"@types/react": "^19.1.4", "@types/react-native": "^0.72.8", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "@vitejs/plugin-react": "^4.0.0", "eslint": "^9.0.0", "jest": "^29.0.0", "lerna": "^8.0.0", "sass-embedded": "1.89.0", "stylelint": "^16.19.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-order": "^6.0.4", "stylelint-scss": "^6.12.0", "typescript": "^5.0.0", "vite": "^5.0.0", "vite-plugin-dts": "^4.0.0", "vite-plugin-libcss": "^1.0.6"}, "dependencies": {"@tarojs/taro": "4.1.1", "lodash-es": "4.17.21", "ramda": "0.30.1", "react-native-linear-gradient": "^2.8.3"}}