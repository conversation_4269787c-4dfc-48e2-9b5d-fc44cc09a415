#!/usr/bin/env node

const { ESLint } = require('eslint');
const path = require('path');
const fs = require('fs');

async function main() {
  try {
    const args = process.argv.slice(2);
    
    if (args.length === 0 || args.includes('--help') || args.includes('-h')) {
      console.log(`
Taro React Native Best Practices Linter

Usage:
  taro-rn-lint [options] [file/dir]

Options:
  --fix           Automatically fix problems
  --config, -c    Use specific config file
  --help, -h      Show this help message
      `);
      process.exit(0);
    }
    
    // 解析参数
    const fixArg = args.includes('--fix');
    const configArgIndex = args.findIndex(arg => arg === '--config' || arg === '-c');
    let configPath = null;
    
    if (configArgIndex !== -1 && args.length > configArgIndex + 1) {
      configPath = args[configArgIndex + 1];
      args.splice(configArgIndex, 2);
    }
    
    if (fixArg) {
      args.splice(args.indexOf('--fix'), 1);
    }
    
    // 确定要检查的文件/目录
    const patterns = args.length > 0 ? args : ['.'];
    
    // 创建默认配置
    const defaultConfig = {
      plugins: ['taro-rn-best-practices'],
      extends: ['plugin:taro-rn-best-practices/recommended'],
      parserOptions: {
        ecmaVersion: 2018,
        sourceType: 'module',
        ecmaFeatures: {
          jsx: true,
        },
      },
    };
    
    // 加载配置文件
    let config = defaultConfig;
    if (configPath) {
      const configFile = path.resolve(process.cwd(), configPath);
      if (fs.existsSync(configFile)) {
        config = require(configFile);
      } else {
        console.error(`Config file not found: ${configFile}`);
        process.exit(1);
      }
    } else {
      // 尝试加载项目中的 .eslintrc 文件
      const eslintrcPaths = [
        '.eslintrc',
        '.eslintrc.js',
        '.eslintrc.json',
        '.eslintrc.yml',
      ];
      
      for (const rcPath of eslintrcPaths) {
        const fullPath = path.resolve(process.cwd(), rcPath);
        if (fs.existsSync(fullPath)) {
          try {
            const userConfig = rcPath.endsWith('.js') 
              ? require(fullPath)
              : JSON.parse(fs.readFileSync(fullPath, 'utf8'));
            
            // 合并用户配置和默认配置
            config = {
              ...userConfig,
              plugins: [...(userConfig.plugins || []), 'taro-rn-best-practices'],
              rules: {
                ...(userConfig.rules || {}),
                ...defaultConfig.rules,
              },
            };
            break;
          } catch (e) {
            console.warn(`Failed to load config from ${rcPath}: ${e.message}`);
          }
        }
      }
    }
    
    // 创建 ESLint 实例
    const eslint = new ESLint({
      useEslintrc: false,
      fix: fixArg,
      overrideConfig: config,
      resolvePluginsRelativeTo: path.resolve(__dirname, '..'),
    });
    
    // 执行 lint
    const results = await eslint.lintFiles(patterns);
    
    // 应用自动修复
    if (fixArg) {
      await ESLint.outputFixes(results);
    }
    
    // 格式化结果
    const formatter = await eslint.loadFormatter('stylish');
    const resultText = formatter.format(results);
    
    // 输出结果
    console.log(resultText);
    
    // 如果有错误，退出码为 1
    const errorCount = results.reduce((acc, result) => acc + result.errorCount, 0);
    if (errorCount > 0) {
      process.exit(1);
    }
  } catch (error) {
    console.error('Error running linter:', error);
    process.exit(1);
  }
}

main();