import { StyleSheet, ViewStyle, TextStyle, ImageStyle } from 'react-native';

interface ShopHeaderStyles {
  container: ViewStyle;
  imageContainer: ViewStyle;
  image: ImageStyle;
  content: ViewStyle;
  titleRow: ViewStyle;
  titleContainer: ViewStyle;
  infoRow: ViewStyle;
  tagLeft: ViewStyle;
  tagRight: ViewStyle;
}

const styles = StyleSheet.create<ShopHeaderStyles>({
  container: {
    flexDirection: 'row',
    width: '100%',
    padding: 16,
    backgroundColor: '#FFFFFF',
  },
  imageContainer: {
    flexShrink: 0,
    marginRight: 12,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 4,
  },
  content: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  titleContainer: {
    flex: 1,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 4,
  },
  tagLeft: {
    flex: 1,
  },
  tagRight: {
    flexShrink: 0,
    marginLeft: 8,
  },
});

export default styles; 