{"name": "@jd/lifeui", "private": true, "workspaces": ["packages/*"], "scripts": {"build": "lerna run build --stream", "build:core": "lerna run build --scope @jd/lifeui-core", "build:shared": "lerna run build --scope @jd/lifeui-business-shared", "build:takeout": "lerna run build --scope @jd/lifeui-business-takeout", "build:groupon": "lerna run build --scope @jd/lifeui-business-groupon", "build:travel": "lerna run build --scope @jd/lifeui-business-travel", "build:instant": "lerna run build --scope @jd/lifeui-business-instant", "dev:core": "cd packages/core && yarn dev", "dev:shared": "cd packages/business-shared && yarn dev", "dev:takeout": "cd packages/business-takeout && yarn dev", "dev:groupon": "cd packages/business-groupon && yarn dev", "dev:travel": "cd packages/business-travel && yarn dev", "dev:instant": "cd packages/business-instant && yarn dev", "test": "lerna run test", "lint": "lerna run lint", "clean": "lerna clean", "bootstrap": "lerna bootstrap", "publish:core": "lerna publish --scope @jd/lifeui-core", "publish:shared": "lerna publish --scope @jd/lifeui-business-shared", "publish:takeout": "lerna publish --scope @jd/lifeui-business-takeout", "publish:groupon": "lerna publish --scope @jd/lifeui-business-groupon", "publish:travel": "lerna publish --scope @jd/lifeui-business-travel", "publish:instant": "lerna publish --scope @jd/lifeui-business-instant", "publish:all": "lerna <PERSON>"}, "devDependencies": {"@types/react": "^19.1.4", "@types/react-native": "^0.72.8", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "eslint": "^8.57.1", "stylelint": "^16.19.1", "stylelint-config-standard-scss": "^14.0.0", "stylelint-order": "^6.0.4", "stylelint-scss": "^6.12.0", "lerna": "^4.0.0", "typescript": "^4.0.0", "jest": "^27.0.0", "vite": "^4.3.0", "vite-plugin-dts": "^2.3.0", "vite-plugin-libcss": "^1.0.6", "@vitejs/plugin-react": "^4.0.0"}, "dependencies": {"react-native-linear-gradient": "^2.8.3"}}