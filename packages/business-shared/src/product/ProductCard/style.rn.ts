import { StyleSheet } from 'react-native';

// React Native样式配置
export const productCardStyles = StyleSheet.create({
  container: {
    display: 'flex',
    flexDirection: 'column',
    width: 'auto',
  },
  
  imageContainer: {
    position: 'relative',
    width: '100%',
    overflow: 'hidden',
  },
  
  content: {
    display: 'flex',
    flexDirection: 'column',
    paddingVertical: 8,
    paddingHorizontal: 0,
  },
  
  titleContainer: {
    marginVertical: 8,
  },
  
  priceContainer: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
});

export default productCardStyles;
