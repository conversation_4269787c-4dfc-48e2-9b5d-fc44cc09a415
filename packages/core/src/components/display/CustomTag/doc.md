# CustomTag 自定义标签组件

用于展示各种风格的标签，支持纯文字、纯图片、前置图标+文字/图片、后置图标+文字/图片等多种展示形式。

## 基础用法

```jsx
<CustomTag type={TagType.TEXT} text="基础标签" />
```

## 特性

- 函数式编程实现，使用 Ramda 库
- 组件化设计，每种类型独立封装
- 支持多端（React Native/Taro）
- 遵循 BEM 命名规范
- 四种标签类型：纯文本、纯图片、前置图标、后置图标

## API

### 通用 Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| type | 标签类型 | `TagType` | **必填** |
| style | 自定义标签容器样式 | `CSSProperties` (Taro)<br/>`StyleProp<ViewStyle>` (RN) | - |
| className | 自定义容器类名 (Taro/Web) | `string` | - |
| showBorder | 是否显示边框 | `boolean` | `false` |
| borderColor | 边框颜色 | `string` | `'#e93b3d'` |
| backgroundColor | 背景色 | `string` | - |

### TagType 枚举

```typescript
export enum TagType {
  /** 纯文字版本 */
  TEXT = 'text',
  /** 图片版本 */
  IMAGE = 'image',
  /** 前面icon + 文字/图片版本 */
  ICON_PREFIX = 'iconPrefix',
  /** 后面icon + 文字/图片版本 */
  ICON_SUFFIX = 'iconSuffix'
}
```

### 类型特定 Props

#### TEXT 类型

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| text | 标签文本内容 | `string` | **必填** |
| textStyle | 自定义标签文本样式 | `CSSProperties` (Taro)<br/>`StyleProp<TextStyle>` (RN) | - |

#### IMAGE 类型

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| tagImage | 标签图片地址 | `string` | **必填** |
| tagImageStyle | 自定义图片样式 | `CSSProperties` (Taro)<br/>`StyleProp<ImageStyle>` (RN) | - |

#### ICON_PREFIX 类型

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| prefixIcon | 前置图标地址 | `string` | **必填** |
| text | 标签文本内容 | `string` | - |
| textStyle | 自定义标签文本样式 | `CSSProperties` (Taro)<br/>`StyleProp<TextStyle>` (RN) | - |
| tagImage | 标签图片地址 | `string` | - |
| tagImageStyle | 自定义图片样式 | `CSSProperties` (Taro)<br/>`StyleProp<ImageStyle>` (RN) | - |
| iconStyle | 自定义图标样式 | `CSSProperties` (Taro)<br/>`StyleProp<ImageStyle>` (RN) | - |

#### ICON_SUFFIX 类型

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| suffixIcon | 后置图标地址 | `string` | **必填** |
| text | 标签文本内容 | `string` | - |
| textStyle | 自定义标签文本样式 | `CSSProperties` (Taro)<br/>`StyleProp<TextStyle>` (RN) | - |
| tagImage | 标签图片地址 | `string` | - |
| tagImageStyle | 自定义图片样式 | `CSSProperties` (Taro)<br/>`StyleProp<ImageStyle>` (RN) | - |
| iconStyle | 自定义图标样式 | `CSSProperties` (Taro)<br/>`StyleProp<ImageStyle>` (RN) | - |

## 示例

### 基础标签

```jsx
<CustomTag text="基础标签" />
```

### 自定义颜色

```jsx
<CustomTag 
  text="促销标签" 
  backgroundColor="#ff4d4f"
  textStyle={{ color: '#fff' }}
/>
```

### 带边框标签

```jsx
<CustomTag 
  text="会员专享" 
  showBorder
  borderColor="#faad14"
  textStyle={{ color: '#faad14' }}
/>
```

### 带图标标签

```jsx
<CustomTag 
  text="新品" 
  icon="https://img.example.com/icon/new.png"
/>
```

### 组合使用

```jsx
<CustomTag 
  text="限时促销" 
  icon="https://img.example.com/icon/clock.png"
  showBorder
  backgroundColor="#fff0f0"
  textStyle={{ color: '#e93b3d' }}
/>
```

## 使用注意

- 标签内容将固定显示为单行文本，过长时不会自动换行
- 容器宽度由内容自适应，可通过 style 进行自定义调整
- 图标默认尺寸为 12px × 12px，可通过 iconStyle 进行调整
- 可以使用 Ramda 的 `mergeRight` 或 `mergeDeepRight` 函数进行样式合并，确保自定义样式优先级更高
- 边框和背景色可以同时设置，以实现不同的视觉效果
