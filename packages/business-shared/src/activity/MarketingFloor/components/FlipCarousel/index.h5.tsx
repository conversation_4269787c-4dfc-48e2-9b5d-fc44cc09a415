import React, { useEffect, useLayoutEffect, useRef, useState } from 'react';
import { FlipCarouselProps } from './types';
import { View } from '../../../../../../core/src/components/basic/View';
import './index.scss';

// 使用共享类型定义
// interface _FlipCarouselProps extends FlipCarouselProps {
//     style?: React.CSSProperties;
//     contentContainerStyle?: React.CSSProperties;
//     pageContentStyle?: React.CSSProperties;
// }

/**
 * 3D翻转轮播组件
 * 支持自动轮播和手动滑动切换
 */
const FlipCarousel: React.FC<FlipCarouselProps> = ({
    data,
    itemsPerPage = 4,
    autoPlayInterval = 3000,
    animationDuration = 500,
    renderItem,
    pageHeight = 100,
    style,
    contentContainerStyle,
    pageContentStyle,
    onPageChange,
}) => {
    // 当前页码状态
    const [currentPage, setCurrentPage] = useState(0);
    // 动画状态 - 使用ref而非state，避免不必要的重新渲染
    const isFlippingRef = useRef(false);
    // 定时器引用
    const timerRef = useRef<NodeJS.Timeout>();
    // 动画元素引用
    const frontPageRef = useRef<HTMLDivElement>(null);
    const backPageRef = useRef<HTMLDivElement>(null);

    // 计算总页数
    const totalPages = Math.ceil(data.length / itemsPerPage);

    /**
     * 获取当前页的数据
     */
    const getCurrentPageData = () => {
        const start = currentPage * itemsPerPage;
        const end = start + itemsPerPage;
        return data.slice(start, end);
    };

    /**
     * 获取下一页的数据
     */
    const getNextPageData = () => {
        const nextPage = (currentPage + 1) % totalPages;
        const start = nextPage * itemsPerPage;
        const end = start + itemsPerPage;
        return data.slice(start, end);
    };

    /**
     * 执行翻页动画
     */
    const flipPage = () => {
        if (isFlippingRef.current || totalPages <= 1) return;

        isFlippingRef.current = true;

        // 设置背面页的初始状态
        if (backPageRef.current) {
            backPageRef.current.style.transform = `rotateX(90deg) translateY(${pageHeight / 2}px)`;
            backPageRef.current.style.opacity = '1';
        }

        // 设置正面页的初始状态
        if (frontPageRef.current) {
            frontPageRef.current.style.transform = 'rotateX(0deg) translateY(0px)';
            frontPageRef.current.style.opacity = '1';
        }

        // 添加动画类
        setTimeout(() => {
            if (frontPageRef.current) {
                frontPageRef.current.style.transition = `transform ${animationDuration}ms`;
                frontPageRef.current.style.transform = `rotateX(-90deg) translateY(0) scaleY(0)`;
                // frontPageRef.current.style.opacity = '0';
            }

            if (backPageRef.current) {
                backPageRef.current.style.transition = `transform ${animationDuration}ms`;
                backPageRef.current.style.transform = 'rotateX(0deg) translateY(0px)';
            }

            // 动画结束后切换页面
            setTimeout(() => {
                // 先标记动画已结束
                isFlippingRef.current = false;

                // 然后切换页面
                const nextPage = (currentPage + 1) % totalPages;
                setCurrentPage(nextPage);

                // 触发翻页完成回调
                if (onPageChange) {
                    const start = nextPage * itemsPerPage;
                    const end = start + itemsPerPage;
                    onPageChange(data.slice(start, end));
                }

                // 在下一个渲染周期后再重置样式，避免闪动
            }, animationDuration);
        }, 50); // 等待一帧确保初始样式生效
    };

    useLayoutEffect(() => {
        requestAnimationFrame(() => {
            // 重置过渡属性
            if (frontPageRef.current) {
                frontPageRef.current.style.transition = 'none';
                frontPageRef.current.style.transform = 'rotateX(0deg) translateY(0px)';
            }

            if (backPageRef.current) {
                backPageRef.current.style.transition = 'none';
                backPageRef.current.style.transform = `rotateX(90deg) translateY(${pageHeight / 2}px)`;
            }
        });
    }, [currentPage]);

    /**
     * 自动轮播效果
     */
    useEffect(() => {
        if (autoPlayInterval && totalPages > 1) {
            timerRef.current = setTimeout(() => {
                flipPage();
            }, autoPlayInterval);

            return () => {
                if (timerRef.current) {
                    clearTimeout(timerRef.current);
                }
            };
        }
    }, [currentPage, totalPages, autoPlayInterval]);

    /**
     * 组件卸载时清理定时器
     */
    useEffect(() => {
        return () => {
            if (timerRef.current) {
                clearTimeout(timerRef.current);
            }
        };
    }, []);

    // 当前页数据
    const currentData = getCurrentPageData();
    // 下一页数据
    const nextData = getNextPageData();

    return (
        <View className="flip-carousel__container" style={style}>
            <div
                className="flip-carousel__content"
                style={{
                    height: pageHeight,
                    ...contentContainerStyle
                }}
            >
                {/* 当前页 */}
                <div
                    ref={frontPageRef}
                    className="flip-carousel__page flip-carousel__front-page"
                    style={{
                        transformOrigin: 'top center',
                        transform: 'rotateX(0deg) translateY(0px)',
                        backfaceVisibility: 'hidden',
                        opacity: 1,
                        transition: 'none',
                    }}
                >
                    <div 
                        className="flip-carousel__page-content" 
                        style={pageContentStyle}
                    >
                        {currentData.map((item, index) => (
                            renderItem(item, currentPage * itemsPerPage + index)
                        ))}
                    </div>
                </div>

                {/* 下一页（背面） */}
                <div
                    ref={backPageRef}
                    className="flip-carousel__page flip-carousel__back-page"
                    style={{
                        transform: `rotateX(90deg) translateY(${pageHeight / 2}px)`,
                        backfaceVisibility: 'hidden',
                        transition: 'none',
                    }}
                >
                    <div 
                        className="flip-carousel__page-content"
                        style={pageContentStyle}
                    >
                        {nextData.map((item, index) => (
                            renderItem(item, ((currentPage + 1) % totalPages) * itemsPerPage + index)
                        ))}
                    </div>
                </div>
            </div>

            {/* 分页指示器（已注释） */}
            {/* {totalPages > 1 && (
                <div className="flip-carousel__pagination">
                    {Array.from({ length: totalPages }).map((_, index) => (
                        <div 
                            key={index}
                            className={`flip-carousel__pagination-dot ${
                                index === currentPage ? 'flip-carousel__pagination-dot--active' : ''
                            }`}
                        />
                    ))}
                </div>
            )} */}
        </View>
    );
};

// 样式已迁移到 index.scss 文件

export default FlipCarousel;