import { Platform, Dimensions } from "react-native";
import { JDDevice } from '@jdreact/jdreact-core-lib'

const { width: w, height: h } = Dimensions.get('window')
const width = Math.min(w, h)
export const isPad: boolean = isTablet();

export function scaleByScreen(value: DesignValue, forceScale = false): number {
    if (!value) return 0

    if (typeof value === 'number') {
        return !forceScale && isPad ? scaleForPad(value) : scaleForMobile(value);
    }

    if (typeof value === 'string') {
        return parseFloat(value) || 0
    }

    const dv = value[isPad ? 'pad' : 'mobile'];

    if (Array.isArray(dv)) {
        if (dv.length === 2) {
            if (isPad && width > 720) {
                return scaleByScreen(dv[1], forceScale)
            }
        }

        return scaleByScreen(dv[0], forceScale)
    }

    return scaleByScreen(dv, forceScale)
}

// 手机缩放
function scaleForMobile(value: number) {
    return value * width / 375;
}

// PAD 缩放
function scaleForPad(value: number) {
    return width > 720
        ? value * width / 720 * 1.2
        : value * 1.2;
}

// 判断是否为 PAD
function isTablet() {
    try {
        if (Platform.OS === 'ios') {
            if (Platform.isPad) {
                return true
            }
        }
    
        if (JDDevice?.isTabletDevice && typeof JDDevice?.isTabletDevice === 'function') {
            try {
                return JDDevice.isTabletDevice()
            } catch (e) {
                return false
            }
        }
    
        return false
    } catch (error) {
        return false
    }
}
