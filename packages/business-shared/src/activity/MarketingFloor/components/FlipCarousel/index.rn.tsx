import React, { useEffect, useRef, useState } from 'react';
import { Animated } from 'react-native';
import {View} from '../../../../../../core/src/components/basic/View';
import {AnimatedView} from '../../../../../../core/src/components/basic/AnimatedView';
import { FlipCarouselProps } from './types';
import './index.scss';

// 使用共享类型定义



/**
 * 3D翻转轮播组件
 * 支持自动轮播和手动滑动切换
 */
const FlipCarousel: React.FC<FlipCarouselProps> = ({
  data,
  itemsPerPage = 4,
  autoPlayInterval = 3000,
  animationDuration = 500,
  renderItem,
  pageHeight = 100,
  style,
  contentContainerStyle,
  pageContentStyle,
  onPageChange,
}) => {
  const [currentPage, setCurrentPage] = useState(0);
  const isAnimatingRef = useRef(false);
  const flipAnim = useRef(new Animated.Value(0)).current;
  const timerRef = useRef<NodeJS.Timeout>();

  // 计算总页数
  const totalPages = Math.ceil(data.length / itemsPerPage);
  // 获取当前页的数据
  const getCurrentPageData = () => {
    const start = currentPage * itemsPerPage;
    const end = start + itemsPerPage;
    return data.slice(start, end);
  };

  // 获取下一页的数据
  const getNextPageData = () => {
    const nextPage = (currentPage + 1) % totalPages;
    const start = nextPage * itemsPerPage;
    const end = start + itemsPerPage;
    return data.slice(start, end);
  };

  // 执行翻页动画
  const flipPage = () => {
    if (isAnimatingRef.current || totalPages <= 1) return;

    isAnimatingRef.current = true;
    flipAnim.setValue(0); // Ensure animation starts from 0

    Animated.timing(flipAnim, {
      toValue: 1,
      duration: animationDuration,
      useNativeDriver: false, // Disable native driver for debugging translateZ issue
    }).start(() => {
      const nextPage = (currentPage + 1) % totalPages;
      setCurrentPage(nextPage);
      flipAnim.setValue(0); // Reset animation progress for the next cycle
      isAnimatingRef.current = false;
      
      // 触发翻页完成回调
      if (onPageChange) {
        const start = nextPage * itemsPerPage;
        const end = start + itemsPerPage;
        onPageChange(data.slice(start, end));
      }
    });
  };

  // 自动轮播
  useEffect(() => {
    if (autoPlayInterval && totalPages > 1) {
      timerRef.current = setTimeout(() => {
        flipPage();
      }, autoPlayInterval);

      return () => {
        if (timerRef.current) {
          clearTimeout(timerRef.current);
        }
      };
    }
  }, [currentPage, totalPages, autoPlayInterval]); // 将 autoPlayInterval 添加到依赖数组

  // 清理定时器
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, []);

  const calculateSimpleTranslateY = (rotationDegrees: number) => {
    // 简单线性计算
    return -pageHeight * 0.66 * (Math.abs(rotationDegrees) / 90);
  };
  // 计算正面页的3D变换
  const getFrontPageTransform = () => {
    const rotateXVal = flipAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ['0deg', '-90deg'], // 向上翻转 (从0度到-90度)
    });
    const translateYVal = flipAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [0, calculateSimpleTranslateY(-90)], // 向上移动半个页面高度，使其围绕底部边缘旋转
    });
    const scaleYVal = flipAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [1, 0], // 缩放
    });
    return {
      transform: [
        // { perspective: 1000 }, // 暂时移除 perspective
        { translateY: translateYVal },
        { rotateX: rotateXVal },
        { scaleY: scaleYVal },
      ],
    };
  };

  // 计算背面页的3D变换
  const getBackPageTransform = () => {
    const rotateXVal = flipAnim.interpolate({
      inputRange: [0, 1],
      outputRange: ['90deg', '0deg'], // 从下方90度翻转至0度
    });
    const translateYVal = flipAnim.interpolate({
      inputRange: [0, 1],
      outputRange: [pageHeight / 2, 0], // 从下方半个页面高度处移动到正常位置
    });
    return {
      transform: [
        // { perspective: 1000 }, // 暂时移除 perspective
        { translateY: translateYVal },
        { rotateX: rotateXVal },
      ],
    };
  };

  const currentData = getCurrentPageData();
  const nextData = getNextPageData();

  return (
    <View className="flip-carousel__container" style={style}>
      <View
        className="flip-carousel__content"
        style={[
          { height: pageHeight },
          contentContainerStyle
        ]}
      >
        {/* 当前页 */}
        <AnimatedView
          className="flip-carousel__page flip-carousel__front-page"
          style={[
            getFrontPageTransform(),
            { backfaceVisibility: 'hidden' as any },
          ] as any}
        >
          <View className="flip-carousel__page-content" style={pageContentStyle}>
            {currentData.map((item, index) => (
              renderItem(item, currentPage * itemsPerPage + index)
            ))}
          </View>
        </AnimatedView>

        {/* 下一页（背面） */}
        <AnimatedView
          className="flip-carousel__page flip-carousel__back-page"
          style={[
            getBackPageTransform(),
            { backfaceVisibility: 'hidden' as any },
          ] as any}
        >
          <View className="flip-carousel__page-content" style={pageContentStyle}>
            {nextData.map((item, index) => (
              renderItem(item, ((currentPage + 1) % totalPages) * itemsPerPage + index)
            ))}
          </View>
        </AnimatedView>
      </View>

      {/* 分页指示器 */}
      {/* {totalPages > 1 && (
        <View style={styles.pagination}>
          {Array.from({ length: totalPages }).map((_, index) => (
            <View 
              key={index}
              style={[
                styles.paginationDot,
                index === currentPage && styles.paginationDotActive,
              ]}
            />
          ))}
        </View>
      )} */}
    </View>
  );
};

export default FlipCarousel;
