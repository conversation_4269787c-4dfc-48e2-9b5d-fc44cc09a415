
export interface BackgroundProps {
  /** */
  backgroundWidth: number,
  backgroundHeight: number,
  backgroundColor?: string[],
  backgroundImageURL?: string,
  backgroundBorderRadius?: number,

  /** */
  colorGroupStartPosition?: colorGroupStartPositionType,
  gradientConnectionEffects?: colorGroupStartPositionTypeType,
  gradientPercent?: number,

  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
}

export type colorGroupStartPositionType = 'TOP' | 'LEFT';
export enum colorGroupStartPositionEnum {
  'TOP' = '180deg',
  'LEFT' = '90deg'
}
export type colorGroupStartPositionTypeType = 'TRANSITION' | 'DIRECT'