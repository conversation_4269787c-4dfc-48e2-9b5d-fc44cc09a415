import View from '../../basic/View';
import Text from '../../basic/Text';
import { Background } from './index';

export default function PriceDemo() {
  return (
    <View style={{ background: '#f5f5f5', minHeight: '100vh', padding: '16px' }}>
      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>默认样式 - 指定单一颜色，默认红</Text>
        <Background 
          backgroundWidth={130} 
          backgroundHeight={40}
        />
      </View>
      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>默认样式 - 指定背景图，默认填充</Text>
        <Background 
          backgroundWidth={130} 
          backgroundHeight={40}
          backgroundBorderRadius={12}
          backgroundImageURL='https://img30.360buyimg.com/img/jfs/t1/301190/25/8889/652/682ee8f7F17feb63d/c33d3db673a40a68.png'
        />
      </View>
      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>双色渐变-上下</Text>
        <Background 
          backgroundWidth={130} 
          backgroundHeight={40}
          colorGroupStartPosition={'TOP'}
          backgroundColor={['#E1251B', '#FF5252']}
        />
      </View>
      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>双色渐变-左右</Text>
        <Background 
          backgroundWidth={130} 
          backgroundHeight={40}
          // colorGroupStartPosition={'TOP'}
          backgroundColor={['#E1251B', '#FF5252']}
          gradientPercent={30}
        />
      </View>
      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>拼色</Text>
        <Background 
          backgroundWidth={130} 
          backgroundHeight={40}
          // colorGroupStartPosition={'TOP'}
          backgroundColor={['#E1251B', '#FF5252']}
          gradientPercent={30}
          gradientConnectionEffects={'DIRECT'}
        />
      </View>
    </View>
  );
} 