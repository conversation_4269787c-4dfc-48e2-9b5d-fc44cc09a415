// Activity components
export { default as MarketingFloor } from './activity/MarketingFloor';

// Product components
export { default as OneLineTwoBiz } from './product/OneLineTwoBiz';
export { default as ProductCard } from './product/ProductCard';

// Shop components
export { CouponCard } from './shop/CouponCard';
export { default as HorizontalList } from './shop/HorizontalList';
export { default as ShopHeader } from './shop/ShopHeader';
export { ShopProductCard } from './shop/ShopProductCard';

// 重新导出主题相关，确保使用同一个 Context
export { default as ThemeProvider, useTheme } from '@jd/lifeui-core';
export type { ThemeMode, Theme } from '@jd/lifeui-core';
export { defaultTheme } from '@jd/lifeui-core';

// Export types
export type { MarketingFloorProps } from './activity/MarketingFloor/types';
export type { OneLineTwoBizProps } from './product/OneLineTwoBiz/types';
export type { ProductCardProps } from './product/ProductCard/types';
export type { CouponCardProps } from './shop/CouponCard/types';
export type { HorizontalListProps } from './shop/HorizontalList/types';
export type { ShopHeaderProps } from './shop/ShopHeader/types';
export type { ShopProductCardProps } from './shop/ShopProductCard/types';
