import { HorizontalListProps } from './types';

/**
 * 计算HorizontalList组件的高度
 * @param props HorizontalList的props
 * @returns 计算后的高度
 */
export const calculateHorizontalListHeight = (props: HorizontalListProps): number => {
  const { items = [] } = props;

  // 如果列表为空，返回0
  if (!items || items.length === 0) {
    return 0;
  }

  let maxHeight = 0;
  
  // 遍历所有items，找出最高的卡片
  items.forEach(item => {
    if (item.type === 'product') {
      // ProductCard默认高度：168px
      maxHeight = Math.max(maxHeight, 110);
    } else if (item.type === 'coupon') {
      // CouponCard默认高度：110px（可通过data.height自定义）
      const couponHeight = item.data?.height || 110;
      maxHeight = Math.max(maxHeight, couponHeight);
    }
  });

  return maxHeight;
}; 