import React from 'react';
import { ViewStyle, TextStyle } from 'react-native';

export interface CouponCardProps {
  /** 优惠券标题 */
  title: string;
  /** 优惠券金额 */
  amount: number;
  /** 使用条件文案 */
  condition?: string;
  /** 货币符号 */
  currencySymbol?: string;
  /** 背景图片URL */
  backgroundImage?: string;
  /** 标题颜色 */
  titleColor?: string;
  /** 金额颜色 */
  amountColor?: string;
  /** 条件文字颜色 */
  conditionColor?: string;
  /** 标题字体大小 */
  titleFontSize?: number;
  /** 金额字体大小 */
  amountFontSize?: number; 
  /** 金额符号字体大小 */
  amountSymbolFontSize?: number;
  /** 条件字体大小 */
  conditionFontSize?: number;
  /** 优惠券宽度 */
  width?: number;
  /** 优惠券高度 */
  height?: number;
  /** 点击事件 */
  onClick?: () => void;
  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
  /** 自定义根元素内联样式 (Taro/Web) */
  style?: React.CSSProperties;
  /** 自定义根元素样式 (RN) */
  containerStyle?: ViewStyle;
} 