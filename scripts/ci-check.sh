#!/bin/bash

# CI/CD 检查脚本
# 用于在持续集成中检查项目状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🔍 开始 CI/CD 检查...${NC}"

# 1. 检查 Node.js 版本
echo -e "${GREEN}📋 检查 Node.js 版本...${NC}"
node_version=$(node --version)
echo "Node.js 版本: $node_version"

if [[ "$node_version" < "v18.0.0" ]]; then
  echo -e "${RED}❌ Node.js 版本过低，需要 >= 18.0.0${NC}"
  exit 1
fi

# 2. 安装依赖
echo -e "${GREEN}📦 安装依赖...${NC}"
npm install

# 3. 验证内部依赖配置
echo -e "${GREEN}🔗 验证内部依赖配置...${NC}"
./scripts/manage-internal-deps.sh validate

# 4. 运行代码检查
echo -e "${GREEN}🔍 运行代码检查...${NC}"
npm run lint || {
  echo -e "${RED}❌ 代码检查失败${NC}"
  exit 1
}

# 5. 运行测试
echo -e "${GREEN}🧪 运行测试...${NC}"
npm run test || {
  echo -e "${RED}❌ 测试失败${NC}"
  exit 1
}

# 6. 构建所有包
echo -e "${GREEN}🔨 构建所有包...${NC}"
npm run build || {
  echo -e "${RED}❌ 构建失败${NC}"
  exit 1
}

# 7. 检查包大小
echo -e "${GREEN}📊 检查包大小...${NC}"
for package in packages/*/dist; do
  if [ -d "$package" ]; then
    package_name=$(basename "$(dirname "$package")")
    size=$(du -sh "$package" | cut -f1)
    echo "  📦 $package_name: $size"
    
    # 检查包大小是否过大 (超过 5MB 警告)
    size_bytes=$(du -s "$package" | cut -f1)
    if [ "$size_bytes" -gt 5120 ]; then  # 5MB = 5120KB
      echo -e "${YELLOW}⚠️  $package_name 包大小较大 ($size)，请检查是否包含不必要的文件${NC}"
    fi
  fi
done

# 8. 安全审计
echo -e "${GREEN}🔒 运行安全审计...${NC}"
npm audit --audit-level moderate || {
  echo -e "${YELLOW}⚠️  发现安全问题，请检查并修复${NC}"
  # 不退出，只是警告
}

# 9. 检查重复依赖
echo -e "${GREEN}🔍 检查重复依赖...${NC}"
duplicate_count=$(npm ls --depth=0 2>/dev/null | grep -c "UNMET PEER DEPENDENCY" || true)
if [ "$duplicate_count" -gt 0 ]; then
  echo -e "${YELLOW}⚠️  发现 $duplicate_count 个未满足的 peer dependency${NC}"
fi

# 10. 验证发布配置
echo -e "${GREEN}📤 验证发布配置...${NC}"
for package in packages/*/package.json; do
  package_dir=$(dirname "$package")
  package_name=$(basename "$package_dir")
  
  # 检查必要字段
  if ! grep -q '"main":' "$package"; then
    echo -e "${RED}❌ $package_name: 缺少 main 字段${NC}"
    exit 1
  fi
  
  if ! grep -q '"module":' "$package"; then
    echo -e "${RED}❌ $package_name: 缺少 module 字段${NC}"
    exit 1
  fi
  
  if ! grep -q '"types":' "$package"; then
    echo -e "${RED}❌ $package_name: 缺少 types 字段${NC}"
    exit 1
  fi
done

echo -e "${GREEN}✅ 所有检查通过！${NC}"
echo -e "${BLUE}🎉 项目状态良好，可以进行发布${NC}"
