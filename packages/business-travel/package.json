{"name": "@jd/lifeui-business-travel", "version": "1.0.0", "description": "本地生活旅游业务组件库", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/types/src/index.d.ts", "react-native": "./dist/index.rn.js", "scripts": {"dev": "vite --config vite.config.dev.js", "build": "vite build", "test": "jest", "lint": "eslint src"}, "files": ["dist", "src"], "dependencies": {"@jd/lifeui-core": "^1.0.0", "@jd/lifeui-business-shared": "^1.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-native": "^0.72.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-native": "^0.72.0"}, "exports": {".": {"react-native": "./dist/index.rn.js", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json", "./rn": {"import": "./dist/index.rn.js", "require": "./dist/index.rn.cjs", "default": "./dist/index.rn.js"}, "./h5": {"import": "./dist/index.h5.js", "require": "./dist/index.h5.cjs", "default": "./dist/index.h5.js"}, "./weapp": {"import": "./dist/index.weapp.js", "require": "./dist/index.weapp.cjs", "default": "./dist/index.weapp.js"}}}