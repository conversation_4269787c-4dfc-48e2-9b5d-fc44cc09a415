import React from 'react';
import { View } from '@tarojs/components';
import ShopProductCard from './shopProductCard';
import { ContentType } from '../ShopHeader/types';
import { ShopHeaderProps } from '../ShopHeader/types';
import { HorizontalListProps } from '../HorizontalList/types';
import './demo.scss';

/**
 * ShopProductCard组件示例
 */
const ShopProductCardDemo: React.FC = () => {
  // 示例店铺信息 - 下酒店铺卡片
  const shopInfo = {
    imageUrl: "https://img10.360buyimg.com/img/jfs/t1/313656/11/2760/13502/682d8341Fd74611c4/d647a2c4bcdf2c76.png",
    imageWidth: 72,
    imageHeight: 72,
    lines: [
      {
        left: [
          {
            type: ContentType.TITLE,
            text: "下酒 (林肯公园店)",
            fontSize: 20,
            style: { 
              color: '#333',
              fontWeight: 'bold'
            }
          }
        ],
        right: [
          {
            type: ContentType.TAG,
            text: '已收藏',
            backgroundColor: '#fff9f0',
            textStyle: { 
              color: '#996633' 
            }
          }
        ]
      },
      {
        left: [
          {
            type: ContentType.TAG,
            text: '4.9分',  
            backgroundColor: '#fff0f0',
            textStyle: { 
              color: '#e93b3d', 
              fontWeight: 'bold' 
            }
          },
          {
            type: ContentType.TAG,
            text: '2372条评价', 
            backgroundColor: '#f5f5f5',
            textStyle: { 
              color: '#999' 
            }
          },
          {
            type: ContentType.TAG,
            text: '￥95/人', 
            backgroundColor: '#f5f5f5',
            textStyle: { 
              color: '#999' 
            }
          }
        ],
        right: [
          {
            type: ContentType.TAG,
            text: '营业至1:00',
            backgroundColor: '#fff9f0',
            textStyle: { 
              color: '#996633' 
            }
          }
        ]
      },
      {
        left: [
          {
            type: ContentType.TAG,
            text: '烧烤',  
            backgroundColor: '#f5f5f5',
            textStyle: { 
              color: '#999' 
            }
          },
          {
            type: ContentType.TAG,
            text: '亦庄', 
            backgroundColor: '#f5f5f5',
            textStyle: { 
              color: '#999' 
            }
          }
        ],
        right: [
          {
            type: ContentType.TAG,
            text: '距您步行321m',
            backgroundColor: '#f5f5f5',
            textStyle: { 
              color: '#999' 
            }
          }
        ]
      }
    ],
    lineSpacing: 6
  } as Omit<ShopHeaderProps, 'className' | 'style' | 'containerStyle'>;

  // 示例商品列表
  const productList = {
    items: [
      {
        type: 'product' as const,
        data: {
          productName: '下班快乐必点',
          imageSrc: 'https://img20.360buyimg.com/img/jfs/t1/318002/15/2760/108308/682ec06bFb40cfc8b/34c3805909b47633.png',
          currentPrice: 128,
          originalPrice: 512
        }
      },
      {
        type: 'product' as const,
        data: {
          productName: '下班快乐3-4人套餐',
          imageSrc: 'https://img20.360buyimg.com/img/jfs/t1/318002/15/2760/108308/682ec06bFb40cfc8b/34c3805909b47633.png',
          currentPrice: 302,
          originalPrice: 630
        }
      },
      {
        type: 'product' as const,
        data: {
          productName: '秒一份好肉！',
          imageSrc: 'https://img20.360buyimg.com/img/jfs/t1/318002/15/2760/108308/682ec06bFb40cfc8b/34c3805909b47633.png',
          currentPrice: 32,
          originalPrice: 53
        }
      },
      {
        type: 'product' as const,
        data: {
          productName: '3-4人聚餐套餐',
          imageSrc: 'https://img20.360buyimg.com/img/jfs/t1/318002/15/2760/108308/682ec06bFb40cfc8b/34c3805909b47633.png',
          currentPrice: 389,
          originalPrice: 699
        }
      }
    ],
    itemSpacing: 12,
    horizontalPadding: 0
  } as Omit<HorizontalListProps, 'className' | 'style' | 'containerStyle'>;

  // 混合内容列表
  const mixedProductList = {
    items: [
      {
        type: 'coupon' as const,
        data: {
          title: '限时优惠券',
          amount: 15,
          condition: '满100元可用',
          backgroundImage: 'https://img30.360buyimg.com/img/jfs/t1/301412/39/8773/2666/682ec0b4F9ae44574/2a32516250ee2053.png',
        }
      },
      {
        type: 'product' as const,
        data: {
          productName: '下班快乐必点',
          imageSrc: 'https://img20.360buyimg.com/img/jfs/t1/318002/15/2760/108308/682ec06bFb40cfc8b/34c3805909b47633.png',
          currentPrice: 128,
          originalPrice: 512
        }
      },
      {
        type: 'product' as const,
        data: {
          productName: '下班快乐3-4人套餐',
          imageSrc: 'https://img20.360buyimg.com/img/jfs/t1/318002/15/2760/108308/682ec06bFb40cfc8b/34c3805909b47633.png',
          currentPrice: 302,
          originalPrice: 630
        }
      }
    ],
    // itemWidth: 120,
    // itemSpacing: 12,
    horizontalPadding: 0
  } as Omit<HorizontalListProps, 'className' | 'style' | 'containerStyle'>;

  return (
    <View className="shop-product-card-demo">
      <View className="page-title">店铺商品卡片组件</View>
      
      <View className="section-title">基础示例 - 店铺信息 + 商品列表</View>
      <View className="demo-section">
        <ShopProductCard 
          shopInfo={shopInfo}
          productList={productList}
          borderRadius={12}
          padding={16}
          contentSpacing={16}
          backgroundColor="#FFFFFF"
          onClick={() => console.log('卡片被点击')}
        />
      </View>

      <View className="section-title">混合内容示例 - 店铺信息 + 优惠券 + 商品</View>
      <View className="demo-section">
        <ShopProductCard
          shopInfo={shopInfo}
          productList={mixedProductList}
          contentSpacing={20}
          backgroundColor="#FFFFFF"
          onClick={() => console.log('卡片被点击')}
        />
      </View>
    </View>
  );
};

export default ShopProductCardDemo; 