#!/bin/bash

# 一键发布脚本 - 从开发到发布的完整自动化流程
# 使用方法: ./scripts/release.sh [patch|minor|major|prerelease]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 获取版本类型参数
VERSION_TYPE=${1:-patch}

echo -e "${BLUE}🎯 一键发布流程开始...${NC}"
echo -e "${GREEN}📋 版本类型: $VERSION_TYPE${NC}"
echo ""

# 1. 检查工作目录是否干净
if [[ -n $(git status --porcelain) ]]; then
  echo -e "${RED}❌ 工作目录不干净，请先提交或暂存更改${NC}"
  exit 1
fi

# 2. 检查当前分支
CURRENT_BRANCH=$(git branch --show-current)
if [[ "$CURRENT_BRANCH" != "main" && "$CURRENT_BRANCH" != "master" ]]; then
  echo -e "${YELLOW}⚠️  当前不在主分支 ($CURRENT_BRANCH)，是否继续? (y/N)${NC}"
  read -r response
  if [[ ! "$response" =~ ^[Yy]$ ]]; then
    exit 1
  fi
fi

# 3. 拉取最新代码
echo -e "${GREEN}📥 拉取最新代码...${NC}"
git pull origin $CURRENT_BRANCH

# 4. 运行完整的 CI 检查 (包含安装依赖、测试、代码检查、构建等)
echo -e "${GREEN}🔍 运行完整检查 (安装依赖、测试、代码检查、构建)...${NC}"
export PATH="/usr/local/n/versions/node/20.18.0/bin:$PATH"
npx yarn install
./scripts/ci-check.sh

# 5. 检查哪些包有变更
echo -e "${GREEN}📋 检查变更的包...${NC}"
CHANGED_PACKAGES=$(npx lerna changed --loglevel silent)
if [ -z "$CHANGED_PACKAGES" ]; then
  echo -e "${YELLOW}⚠️  没有检测到包变更，是否继续发布? (y/N)${NC}"
  read -r response
  if [[ ! "$response" =~ ^[Yy]$ ]]; then
    echo -e "${BLUE}🚫 发布已取消${NC}"
    exit 0
  fi
else
  echo -e "${GREEN}📦 将要发布的包:${NC}"
  echo "$CHANGED_PACKAGES" | sed 's/^/  - /'
  echo ""
fi

# 6. 版本升级
echo -e "${GREEN}📈 升级版本 ($VERSION_TYPE)...${NC}"
npx lerna version $VERSION_TYPE --conventional-commits --yes

# 7. 发布到 npm
echo -e "${GREEN}📤 发布到 npm...${NC}"
npx lerna publish from-git --yes

# 8. 推送标签
echo -e "${GREEN}🏷️  推送标签到远程仓库...${NC}"
git push --follow-tags

# 9. 显示发布结果
echo ""
echo -e "${GREEN}✅ 发布完成！${NC}"
echo -e "${BLUE}📊 发布摘要:${NC}"
echo -e "${GREEN}  🎯 版本类型: $VERSION_TYPE${NC}"
echo -e "${GREEN}  📦 发布包数: $(echo "$CHANGED_PACKAGES" | wc -l)${NC}"
echo -e "${GREEN}  🕐 发布时间: $(date)${NC}"
echo ""
echo -e "${BLUE}🎉 一键发布流程全部完成！${NC}"
