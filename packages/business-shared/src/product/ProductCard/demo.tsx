import React from 'react';
import { View } from '../../../../core/src/components/basic/View';
import ProductCard from './productCard';

const Demo: React.FC = () => {
  return (
    <View>
      <View style={{ padding: 16, backgroundColor: '#f5f5f5' }}>
        <ProductCard
          imageSrc="https://img10.360buyimg.com/imagetools/jfs/t1/117501/29/35430/48693/6426c724F8ee870cb/d1074c30f8a9beda.jpg"
          title="u9531u5e97u8c6au534e6u4eba"
          currentPrice={240}
          originalPrice={1080}
          tag={[{ text: "u4fc3u9500", position: "topLeft", backgroundColor: "#FF5B00" }]}
        />
      </View>
      
      <View style={{ padding: 16, marginTop: 16, backgroundColor: '#f5f5f5' }}>
        <ProductCard
          imageSrc="https://img10.360buyimg.com/imagetools/jfs/t1/117501/29/35430/48693/6426c724F8ee870cb/d1074c30f8a9beda.jpg"
          title="u9531u5e97u7cbeu81f4u5355u4ebau5957u9910"
          currentPrice={88}
          imageSize={160}
          titleLines={1}
        />
      </View>
      
      <View style={{ padding: 16, marginTop: 16, backgroundColor: '#f5f5f5' }}>
        <ProductCard
          imageSrc="https://img10.360buyimg.com/imagetools/jfs/t1/117501/29/35430/48693/6426c724F8ee870cb/d1074c30f8a9beda.jpg"
          title="u9531u5e97u7279u4ef7u5957u9910u9650u65f6u4fc3u9500u6d3bu52a8u5546u54c1"
          currentPrice={299}
          originalPrice={599}
          priceType="discount"
          tag={[
            { text: "u9650u65f6", position: "topLeft", backgroundColor: "#FF0000" },
            { text: "u7279u4ef7", position: "topRight", backgroundColor: "#FF9500" }
          ]}
        />
      </View>
    </View>
  );
};

export default Demo;
