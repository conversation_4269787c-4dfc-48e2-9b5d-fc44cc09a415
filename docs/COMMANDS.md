# 📋 项目命令参考

## 🚀 一键发布 (最常用)

```bash
# 日常开发发布 - 最常用 ⭐
yarn quick:patch        # 修复版本 (1.0.0 -> 1.0.1)
yarn quick:minor        # 功能版本 (1.0.0 -> 1.1.0)
yarn quick:major        # 重大版本 (1.0.0 -> 2.0.0)

# 完整发布流程 - 生产环境
yarn release:patch      # 修复版本 + 完整检查
yarn release:minor      # 功能版本 + 完整检查
yarn release:major      # 重大版本 + 完整检查
yarn release:beta       # 预发布版本
```

## 🛠️ 开发命令

```bash
# 构建
yarn build              # 构建所有包 (单平台)
yarn build:multi        # 多端构建 (rn, h5, weapp 等)
yarn build:core:multi   # 只构建 core 包的多端版本

# 测试
yarn test               # 运行所有测试

# 代码检查
yarn lint               # 运行代码检查

# 开发模式
yarn dev                # 启动所有包的开发模式
```

## 🔍 检查命令

```bash
# 完整检查
yarn check              # 运行完整 CI/CD 检查

# 变更检查
yarn check:changed      # 查看哪些包有变更

# 依赖检查
yarn deps:check         # 检查内部依赖状态
yarn deps:fix           # 修复依赖问题

# 安全检查
yarn audit              # 安全审计
```

## 🧹 维护命令

```bash
# 清理
yarn clean              # 清理所有 node_modules

# 重置
yarn reset              # 清理 + 重新安装依赖
```

## 📊 命令使用频率

### 日常开发 (90%)
```bash
yarn quick:patch        # ⭐⭐⭐⭐⭐ 最常用
yarn build              # ⭐⭐⭐⭐
yarn test               # ⭐⭐⭐⭐
yarn dev                # ⭐⭐⭐
```

### 偶尔使用 (10%)
```bash
yarn release:major      # ⭐⭐ 重大版本
yarn check              # ⭐⭐ 发布前检查
yarn deps:check         # ⭐ 依赖问题排查
yarn clean              # ⭐ 环境问题排查
```

## 🔄 典型工作流

### 修复 Bug
```bash
# 1. 修改代码
# 2. 一键发布
yarn quick:patch
```

### 新功能开发
```bash
# 1. 开发功能
yarn dev                # 开发模式
yarn test               # 测试

# 2. 发布
yarn quick:minor
```

### 重大更新
```bash
# 1. 开发
yarn dev

# 2. 完整检查
yarn check

# 3. 发布
yarn release:major
```

## 🚨 问题排查

### 构建失败
```bash
yarn clean              # 清理环境
yarn build              # 重新构建
```

### 依赖问题
```bash
yarn deps:check         # 检查依赖
yarn deps:fix           # 修复依赖
```

### 发布失败
```bash
yarn check              # 完整检查
yarn audit              # 安全检查
```

## 💡 命令优化说明

### 移除的冗余命令
- ❌ `build:core`, `build:shared` 等 - 用 `yarn build` 替代
- ❌ `dev:core`, `dev:shared` 等 - 用 `yarn dev` 替代
- ❌ `publish:*` 系列 - 用 `yarn quick:*` 或 `yarn release:*` 替代
- ❌ `version:*` 系列 - 已集成到发布脚本中
- ❌ `bootstrap` - 现代 yarn workspaces 不需要
- ❌ 重复的检查命令 - 合并为 `yarn check`

### 保留的核心命令
- ✅ **发布命令** - 最重要的功能
- ✅ **开发命令** - 日常开发必需
- ✅ **检查命令** - 问题排查必需
- ✅ **维护命令** - 环境管理必需

### 命令设计原则
1. **简洁性** - 减少命令数量，避免选择困难
2. **一致性** - 命名规范统一
3. **实用性** - 保留最常用的命令
4. **可发现性** - 命令名称直观易懂

## 📚 相关文档

- 🚀 [开发工作流指南](./DEVELOPMENT_WORKFLOW.md)
- 📦 [包管理指南](./PACKAGE_MANAGEMENT.md)
- 🔗 [内部依赖管理](./INTERNAL_DEPENDENCIES.md)
