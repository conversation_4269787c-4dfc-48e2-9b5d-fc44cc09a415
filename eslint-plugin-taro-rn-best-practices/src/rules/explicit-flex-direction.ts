import { ESLintUtils } from '@typescript-eslint/utils';

const createRule = ESLintUtils.RuleCreator(
  (name) => `https://docs.taro.zone/docs/react-native-remind#${name}`
);

export default createRule({
  name: 'explicit-flex-direction',
  meta: {
    type: 'suggestion',
    docs: {
      description: 'Enforce explicit flexDirection for View components with display:flex.',
      recommended: 'warn',
    },
    messages: {
      explicitFlexDirection: 'Explicitly set flexDirection for cross-platform consistency (RN defaults to column, web defaults to row)',
    },
    schema: [],
    fixable: 'code',
  },
  defaultOptions: [],
  create(context) {
    // 检查 style={{ display: 'flex', ... }}
    return {
      JSXAttribute(node) {
        if (
          node.name && node.name.name === 'style' &&
          node.value &&
          node.value.type === 'JSXExpressionContainer' &&
          node.value.expression &&
          node.value.expression.type === 'ObjectExpression'
        ) {
          const styleObj = node.value.expression;
          let hasDisplayFlex = false;
          let hasFlexDirection = false;
          let hasFlexProperty = false;
          for (const prop of styleObj.properties) {
            if (prop.type !== 'Property' || prop.key.type !== 'Identifier') continue;
            if (
              prop.key.name === 'display' &&
              prop.value.type === 'Literal' &&
              prop.value.value === 'flex'
            ) {
              hasDisplayFlex = true;
            }
            if (prop.key.name === 'flexDirection') {
              hasFlexDirection = true;
            }
            if (
              prop.key.name === 'flex' ||
              prop.key.name === 'flexGrow' ||
              prop.key.name === 'flexShrink' ||
              prop.key.name === 'flexBasis'
            ) {
              hasFlexProperty = true;
            }
          }
          if ((hasDisplayFlex || hasFlexProperty) && !hasFlexDirection) {
            context.report({
              node,
              messageId: 'explicitFlexDirection',
            });
          }
        }
      },
      // 检查 StyleSheet.create({ ... })
      CallExpression(node) {
        if (
          node.callee &&
          node.callee.type === 'MemberExpression' &&
          node.callee.object.type === 'Identifier' &&
          node.callee.object.name === 'StyleSheet' &&
          node.callee.property.type === 'Identifier' &&
          node.callee.property.name === 'create' &&
          node.arguments &&
          node.arguments.length > 0 &&
          node.arguments[0].type === 'ObjectExpression'
        ) {
          const styleObj = node.arguments[0];
          for (const prop of styleObj.properties) {
            if (prop.type !== 'Property' || prop.value.type !== 'ObjectExpression') continue;
            let hasDisplayFlex = false;
            let hasFlexDirection = false;
            let hasFlexProperty = false;
            for (const styleProp of prop.value.properties) {
              if (styleProp.type !== 'Property' || styleProp.key.type !== 'Identifier') continue;
              if (
                styleProp.key.name === 'display' &&
                styleProp.value.type === 'Literal' &&
                styleProp.value.value === 'flex'
              ) {
                hasDisplayFlex = true;
              }
              if (styleProp.key.name === 'flexDirection') {
                hasFlexDirection = true;
              }
              if (
                styleProp.key.name === 'flex' ||
                styleProp.key.name === 'flexGrow' ||
                styleProp.key.name === 'flexShrink' ||
                styleProp.key.name === 'flexBasis'
              ) {
                hasFlexProperty = true;
              }
            }
            if ((hasDisplayFlex || hasFlexProperty) && !hasFlexDirection) {
              context.report({
                node: prop.value,
                messageId: 'explicitFlexDirection',
              });
            }
          }
        }
      },
    };
  },
});