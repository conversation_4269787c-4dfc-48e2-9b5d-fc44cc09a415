# CustomTagList 标签列表组件

标签列表组件用于展示多个标签，支持设置最大显示行数，超出时可以截断显示，并提供"更多"按钮或省略号。

## 组件特性

- 支持展示多个CustomTag标签
- 支持设置容器宽度
- 支持限制显示行数
- 支持超出行数后的截断方式（更多按钮、省略号）
- 支持自定义标签间距
- 支持标签自动换行
- 支持标签拉伸填满容器宽度
- 跨平台兼容（Taro/RN）

## 基础用法

```tsx
import { CustomTagList } from '@uicomponents/components/CustomTagList';

// 基础用法
<CustomTagList 
  tags={[
    { text: '热门' },
    { text: '推荐' },
    { text: '特惠' }
  ]}
  maxLines={1}
/>

// 多行展示
<CustomTagList 
  tags={[
    { text: '热门' },
    { text: '推荐' },
    { text: '特惠' },
    { text: '满减活动' },
    { text: '新品上市' }
  ]}
  maxLines={2}
  truncateMode="ellipsis"
/>

// 自定义标签样式
<CustomTagList 
  tags={[
    {
      text: '新品',
      backgroundColor: '#e6f7ff',
      textStyle: { color: '#1890ff' }
    },
    {
      text: '热卖',
      backgroundColor: '#fff2e8',
      textStyle: { color: '#fa541c' }
    },
    {
      text: '促销',
      backgroundColor: '#f6ffed',
      textStyle: { color: '#52c41a' }
    }
  ]}
  maxLines={1}
  truncateMode="more"
  moreText="查看全部"
  onMoreClick={() => console.log('点击了更多')}
/>

// 拉伸填满容器
<CustomTagList 
  tags={[
    { text: '推荐' },
    { text: '特惠' },
    { text: '热门' }
  ]}
  stretch={true}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| tags | 标签列表 | `CustomTagProps[]` | `[]` |
| maxLines | 最大显示行数 | `number` | `1` |
| lineHeight | 行高 | `number` | `32` |
| gap | 标签水平间距 | `number` | `8` |
| verticalGap | 标签垂直间距 | `number` | `8` |
| stretch | 是否拉伸标签填满容器 | `boolean` | `false` |
| truncateMode | 截断模式 | `'more' \| 'ellipsis' \| 'none'` | `'more'` |
| moreText | 更多按钮文本 | `string` | `'更多'` |
| onMoreClick | 点击更多按钮的回调 | `() => void` | - |
| width | 容器宽度 | `string \| number` (Taro)<br/>`number` (RN) | - |
| className | 容器类名 | `string` | - |
| style | 容器样式 | `CSSProperties` (Taro)<br/>`StyleProp<ViewStyle>` (RN) | - |
| tagsContainerClassName | 标签容器类名 | `string` | - |
| tagsContainerStyle | 标签容器样式 | `CSSProperties` (Taro)<br/>`StyleProp<ViewStyle>` (RN) | - |
| moreButtonStyle | 更多按钮样式 | `CSSProperties` (Taro)<br/>`StyleProp<TextStyle>` (RN) | - |

## 注意事项

1. 当设置 `maxLines={0}` 或 `maxLines={Infinity}` 时，会显示全部标签，不会截断
2. 当 `truncateMode="none"` 时，无论设置多少行数限制，都不会显示"更多"按钮或省略号
3. 在RN环境中，标签宽度是基于文本长度估算的，与实际可能有所差异 