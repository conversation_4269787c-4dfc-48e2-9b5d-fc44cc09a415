module.exports = {
  root: true,
  plugins: ['taro-rn-best-practices'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
    ecmaFeatures: { jsx: true },
  },
  env: {
    es2021: true,
    node: true,
  },
  rules: {
    'taro-rn-best-practices/text-must-be-in-text-component': 'error',
    'taro-rn-best-practices/no-style-inheritance': 'warn',
    'taro-rn-best-practices/require-image-size': 'error',
    'taro-rn-best-practices/no-unsupported-style-props': 'error',
    'taro-rn-best-practices/no-inline-styles': 'warn',
    'taro-rn-best-practices/explicit-flex-direction': 'warn',
    'taro-rn-best-practices/no-fixed-position': 'error',
    'taro-rn-best-practices/no-unsupported-selectors': 'error',
  },
}; 