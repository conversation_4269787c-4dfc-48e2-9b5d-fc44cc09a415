import { ESLintUtils } from '@typescript-eslint/utils';

const createRule = ESLintUtils.RuleCreator(
  (name) => `https://docs.taro.zone/docs/react-native-remind#${name}`
);

// 不支持的样式属性列表
const UNSUPPORTED_STYLE_PROPS = [
  'animation',
  'animationDelay',
  'animationDirection',
  'animationDuration',
  'animationFillMode',
  'animationIterationCount',
  'animationName',
  'animationPlayState',
  'animationTimingFunction',
  'float',
  'clear',
  'cursor',
  'clip',
  'filter',
  'backdropFilter',
  'mixBlendMode',
  'resize',
  'columnCount',
  'columnGap',
  'columnRule',
  'columns',
  'pageBreakAfter',
  'pageBreakBefore',
  'pageBreakInside',
  'gridArea',
  'gridAutoColumns',
  'gridAutoFlow',
  'gridAutoRows',
  'gridColumn',
  'gridColumnEnd',
  'gridColumnGap',
  'gridColumnStart',
  'gridGap',
  'gridRow',
  'gridRowEnd',
  'gridRowGap',
  'gridRowStart',
  'gridTemplate',
  'gridTemplateAreas',
  'gridTemplateColumns',
  'gridTemplateRows',
];

export default createRule({
  name: 'no-unsupported-style-props',
  meta: {
    type: 'problem',
    docs: {
      description: 'Disallow unsupported style properties in React Native.',
      recommended: 'error',
    },
    messages: {
      unsupportedStyleProp: "'{{ prop }}' style property is not supported in React Native",
    },
    schema: [],
  },
  defaultOptions: [],
  create(context) {
    return {
      // 检查 style={{ ... }}
      JSXAttribute(node) {
        if (
          node.name && node.name.name === 'style' &&
          node.value &&
          node.value.type === 'JSXExpressionContainer' &&
          node.value.expression &&
          node.value.expression.type === 'ObjectExpression'
        ) {
          for (const prop of node.value.expression.properties) {
            if (prop.type !== 'Property' || prop.key.type !== 'Identifier') continue;
            if (UNSUPPORTED_STYLE_PROPS.includes(prop.key.name)) {
              context.report({
                node: prop,
                messageId: 'unsupportedStyleProp',
                data: { prop: prop.key.name },
              });
            }
          }
        }
      },
      // 检查 StyleSheet.create({ ... })
      CallExpression(node) {
        if (
          node.callee &&
          node.callee.type === 'MemberExpression' &&
          node.callee.object.type === 'Identifier' &&
          node.callee.object.name === 'StyleSheet' &&
          node.callee.property.type === 'Identifier' &&
          node.callee.property.name === 'create' &&
          node.arguments &&
          node.arguments.length > 0 &&
          node.arguments[0].type === 'ObjectExpression'
        ) {
          const styleObj = node.arguments[0];
          for (const prop of styleObj.properties) {
            if (prop.type !== 'Property' || prop.value.type !== 'ObjectExpression') continue;
            for (const styleProp of prop.value.properties) {
              if (styleProp.type !== 'Property' || styleProp.key.type !== 'Identifier') continue;
              if (UNSUPPORTED_STYLE_PROPS.includes(styleProp.key.name)) {
                context.report({
                  node: styleProp,
                  messageId: 'unsupportedStyleProp',
                  data: { prop: styleProp.key.name },
                });
              }
            }
          }
        }
      },
    };
  },
});