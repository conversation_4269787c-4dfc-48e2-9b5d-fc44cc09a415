#!/usr/bin/env node

/**
 * 多端构建脚本
 * 参考 Taro 的构建方式，为不同平台生成独立的构建产物
 */

const { build } = require('vite');
const { resolve } = require('path');
const fs = require('fs-extra');
const path = require('path');

// 支持的平台
const PLATFORMS = ['rn', 'h5', 'weapp', 'alipay', 'swan', 'tt', 'qq', 'jd'];

// 颜色输出
const colors = {
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  red: '\x1b[31m',
  reset: '\x1b[0m'
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function buildPackage(packagePath, platforms = PLATFORMS) {
  const packageDir = resolve(process.cwd(), packagePath);
  const packageJsonPath = resolve(packageDir, 'package.json');

  if (!fs.existsSync(packageJsonPath)) {
    log('red', `❌ 找不到 package.json: ${packageJsonPath}`);
    return false;
  }

  const packageJson = await fs.readJson(packageJsonPath);
  log('blue', `📦 开始构建包: ${packageJson.name}`);

  // 清理 dist 目录
  const distDir = resolve(packageDir, 'dist');
  await fs.emptyDir(distDir);
  log('yellow', '🧹 清理 dist 目录');

  // 构建每个平台
  for (const platform of platforms) {
    try {
      log('blue', `🔨 构建平台: ${platform}`);
      await buildPlatform(packageDir, packageJson, platform);
      log('green', `✅ ${platform} 构建完成`);
    } catch (error) {
      log('red', `❌ ${platform} 构建失败: ${error.message}`);
      throw error;
    }
  }

  // 生成平台入口文件
  await generatePlatformEntries(packageDir, packageJson, platforms);

  // 更新 package.json 的导出字段
  await updatePackageExports(packageDir, packageJson, platforms);

  log('green', `🎉 ${packageJson.name} 多端构建完成!`);
  return true;
}

async function buildPlatform(packageDir, packageJson, platform) {
  console.log(`🔧 [${platform}] Starting build with platform: ${platform}`);
  const { defineConfig } = require('vite');
  const react = require('@vitejs/plugin-react');
  const dts = require('vite-plugin-dts');
  const libCss = require('vite-plugin-libcss');

  const { name, dependencies, peerDependencies } = packageJson;

  // 外部依赖
  const external = [
    ...Object.keys(dependencies || {}),
    ...Object.keys(peerDependencies || {}),
    'react/jsx-runtime'
  ];

  // 为不同平台创建特定的外部依赖配置
  function createPlatformExternals() {
    // 对于所有平台，我们都内联 @jd/lifeui-core 以确保使用正确的平台特定代码
    return external.filter(dep => dep !== '@jd/lifeui-core');
  }

  // 平台特定的外部依赖
  const platformExternals = {
    rn: [...createPlatformExternals(), 'react-native'],
    h5: [...createPlatformExternals(), 'react-dom', '@tarojs/components', '@tarojs/taro'],
    weapp: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    alipay: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    swan: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    tt: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    qq: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro'],
    jd: [...createPlatformExternals(), '@tarojs/components', '@tarojs/taro']
  };

  // 平台特定的解析条件
  const platformConditions = {
    rn: ['react-native', 'import', 'module', 'default'],
    h5: ['browser', 'import', 'module', 'default'],
    weapp: ['weapp', 'import', 'module', 'default'],
    alipay: ['alipay', 'import', 'module', 'default'],
    swan: ['swan', 'import', 'module', 'default'],
    tt: ['tt', 'import', 'module', 'default'],
    qq: ['qq', 'import', 'module', 'default'],
    jd: ['jd', 'import', 'module', 'default']
  };

  // 平台特定的文件扩展名
  const platformExtensions = {
    rn: ['.rn.tsx', '.rn.ts', '.rn.jsx', '.rn.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    h5: ['.h5.tsx', '.h5.ts', '.h5.jsx', '.h5.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    weapp: ['.weapp.tsx', '.weapp.ts', '.weapp.jsx', '.weapp.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    alipay: ['.alipay.tsx', '.alipay.ts', '.alipay.jsx', '.alipay.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    swan: ['.swan.tsx', '.swan.ts', '.swan.jsx', '.swan.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    tt: ['.tt.tsx', '.tt.ts', '.tt.jsx', '.tt.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    qq: ['.qq.tsx', '.qq.ts', '.qq.jsx', '.qq.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    jd: ['.jd.tsx', '.jd.ts', '.jd.jsx', '.jd.js', '.tsx', '.ts', '.jsx', '.js', '.json']
  };

  // 创建调试插件
  function createDebugPlugin(platform) {
    console.log(`🚀 [${platform}] Debug plugin initialized`);
    return {
      name: 'debug-resolution',
      resolveId(id, importer) {
        if (id.includes('screenUtils') && importer) {
          console.log(`🔍 [${platform}] Resolving:`, { id, importer });
          console.log(`🔧 [${platform}] Extensions:`, platformExtensions[platform]);

          // 对于非 h5 平台，如果是 screenUtils 的相对导入，强制解析到平台特定版本
          if (platform !== 'h5' && id === '../utils/screenUtils' && importer.includes('core/src')) {
            const path = require('path');
            const fs = require('fs');

            // 构造平台特定文件的绝对路径
            const importerDir = path.dirname(importer);
            const platformFile = path.resolve(importerDir, `../utils/screenUtils.${platform}.ts`);

            console.log(`🎯 [${platform}] Trying platform file:`, platformFile);

            if (fs.existsSync(platformFile)) {
              console.log(`✅ [${platform}] Found platform file, redirecting to:`, platformFile);
              return platformFile;
            }
          }
        }
        return null;
      },
      load(id) {
        if (id.includes('screenUtils')) {
          console.log(`📁 [${platform}] Loading file:`, id);

          // 对于非 h5 平台，如果加载的是 screenUtils.ts，直接返回 RN 版本的内容
          if (platform !== 'h5' && id.endsWith('screenUtils.ts')) {
            const fs = require('fs');
            const platformFile = id.replace('screenUtils.ts', `screenUtils.${platform}.ts`);

            console.log(`🎯 [${platform}] Checking platform file:`, platformFile);

            if (fs.existsSync(platformFile)) {
              console.log(`✅ [${platform}] Loading platform file content:`, platformFile);
              return fs.readFileSync(platformFile, 'utf-8');
            }
          }
        }
        return null;
      }
    };
  }

  // 创建平台特定的依赖重写插件
  function createPlatformDependencyPlugin(platform) {
    return {
      name: 'platform-dependency-rewrite',
      generateBundle(_, bundle) {
        // 只在非 h5 平台处理依赖重写
        if (platform === 'h5') return;

        Object.keys(bundle).forEach(fileName => {
          const chunk = bundle[fileName];
          if (chunk.type === 'chunk' && chunk.code) {
            // 重写对 @jd/lifeui-core 的导入
            chunk.code = chunk.code.replace(
              /from\s+['"]@jd\/lifeui-core['"]/g,
              `from '@jd/lifeui-core/${platform}'`
            );
            chunk.code = chunk.code.replace(
              /require\(['"]@jd\/lifeui-core['"]\)/g,
              `require('@jd/lifeui-core/${platform}')`
            );
          }
        });
      }
    };
  }

  const config = defineConfig({
    build: {
      lib: {
        entry: resolve(packageDir, 'src/index.ts'),
        name: name.replace('@jd/', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase()),
        fileName: (format) => `index.${platform}.${format}.js`,
        formats: ['es', 'cjs']
      },
      rollupOptions: {
        external: platformExternals[platform],
        output: {
          globals: {
            react: 'React',
            'react-dom': 'ReactDOM',
            'react-native': 'ReactNative',
            '@tarojs/taro': 'Taro',
            '@tarojs/components': 'TaroComponents'
          }
        }
      },
      outDir: resolve(packageDir, `dist/${platform}`),
      emptyOutDir: false,
      sourcemap: true
    },
    plugins: [
      react(),
      // 只为第一个平台生成类型定义
      ...(platform === 'rn' ? [dts.default({
        include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.d.ts'],
        outDir: resolve(packageDir, 'dist/types')
      })] : []),
      libCss(),
      // 添加调试插件
      createDebugPlugin(platform),
      // 添加平台特定的依赖重写插件
      createPlatformDependencyPlugin(platform)
    ],
    resolve: {
      extensions: platformExtensions[platform],
      conditions: platformConditions[platform],
      alias: {
        '@jd/lifeui-core': resolve(packageDir, '../core/src')
      }
    },
    define: {
      'process.env.TARO_ENV': JSON.stringify(platform === 'h5' ? 'h5' : platform === 'rn' ? 'rn' : platform)
    }
  });

  await build(config);
}

async function generatePlatformEntries(packageDir, packageJson, platforms) {
  log('blue', '📝 生成平台入口文件...');

  const distDir = resolve(packageDir, 'dist');

  // 为每个平台生成入口文件
  for (const platform of platforms) {
    const platformDir = resolve(distDir, platform);
    if (!fs.existsSync(platformDir)) continue;

    // 生成 ES 模块入口
    const esEntry = `export * from './${platform}/index.${platform}.es.js';`;
    await fs.writeFile(resolve(distDir, `index.${platform}.js`), esEntry);

    // 生成 CommonJS 入口
    const cjsEntry = `module.exports = require('./${platform}/index.${platform}.cjs.js');`;
    await fs.writeFile(resolve(distDir, `index.${platform}.cjs`), cjsEntry);
  }

  // 生成主入口文件 (默认使用 h5)
  const mainEntry = `export * from './h5/index.h5.es.js';`;
  await fs.writeFile(resolve(distDir, 'index.js'), mainEntry);

  const mainCjsEntry = `module.exports = require('./h5/index.h5.cjs.js');`;
  await fs.writeFile(resolve(distDir, 'index.cjs'), mainCjsEntry);
}

async function updatePackageExports(packageDir, packageJson, platforms) {
  log('blue', '📝 更新 package.json 导出字段...');

  const packageJsonPath = resolve(packageDir, 'package.json');

  // 构建导出字段
  const exports = {
    '.': {
      'react-native': './dist/index.rn.js',
      'import': './dist/index.js',
      'require': './dist/index.cjs',
      'default': './dist/index.js'
    },
    './package.json': './package.json'
  };

  // 为每个平台添加导出
  for (const platform of platforms) {
    const platformDir = resolve(packageDir, `dist/${platform}`);
    if (!fs.existsSync(platformDir)) continue;

    exports[`./${platform}`] = {
      'import': `./dist/index.${platform}.js`,
      'require': `./dist/index.${platform}.cjs`,
      'default': `./dist/index.${platform}.js`
    };
  }

  // 更新 package.json
  const updatedPackageJson = {
    ...packageJson,
    exports,
    main: './dist/index.cjs',
    module: './dist/index.js',
    'react-native': './dist/index.rn.js',
    types: './dist/types/src/index.d.ts'
  };

  await fs.writeJson(packageJsonPath, updatedPackageJson, { spaces: 2 });
}

// 主函数
async function main() {
  const args = process.argv.slice(2);
  const packagePath = args[0] || 'packages/core';
  const platforms = args[1] ? args[1].split(',') : PLATFORMS;

  log('blue', '🚀 开始多端构建...');
  log('yellow', `📦 包路径: ${packagePath}`);
  log('yellow', `🎯 目标平台: ${platforms.join(', ')}`);

  try {
    await buildPackage(packagePath, platforms);
    log('green', '🎉 多端构建全部完成!');
  } catch (error) {
    log('red', `❌ 构建失败: ${error.message}`);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { buildPackage };
