import React, { forwardRef, ForwardRefRenderFunction, useMemo } from 'react';
import { View as RNVIew, TouchableWithoutFeedback, StyleSheet } from "react-native";
import { ViewProps } from './type';
import { isObject } from '../utils/isType';
import withClassName from '../utils/withClassName.rn';
import { useScreen } from '../../../hooks/useScreen';
// import { transformRNStyle } from '../common/utils';

const View: ForwardRefRenderFunction<React.FC<ViewProps>, any> = (props, ref) => {
    const { style = {}, onClick, onLayout, ...others } = props;
    const { scaleStyle } = useScreen();

    // let styleObj = transformRNStyle(style);
    const styles = useMemo(() => {
        const originalStyle = isObject(style) ? StyleSheet.create({ style }).style : style;
        
        // 对style应用自动缩放（只处理对象类型的style）
        if (originalStyle && typeof originalStyle === 'object' && !Array.isArray(originalStyle)) {
            return scaleStyle(originalStyle as Record<string, any>);
        }
        
        return originalStyle;
    }, [style, scaleStyle]) as any
    if (!onClick) {
        return (
            <RNVIew ref={ref} style={[myStyle.defaultView, styles]} onLayout={onLayout} {...others}>
                {props.children}
            </RNVIew>
        )
    }

    return (
        <TouchableWithoutFeedback onPress={onClick} hitSlop={ { top: 5, left: 5, bottom: 5, right: 5 } }>
            <RNVIew ref={ref} style={[myStyle.defaultView, styles]} onLayout={onLayout} {...others}>
                {props.children}
            </RNVIew>
        </TouchableWithoutFeedback>
    )

}
const myStyle = StyleSheet.create({
    defaultView: {
        display: 'flex'
    }
})
export default withClassName()(forwardRef(View));