/**
 * Dimensions模块提供了一个用于获取设备屏幕尺寸信息的接口
 * 这个版本使用Taro实现，但与react-native的Dimensions接口保持一致
 */
import Taro from '@tarojs/taro';

// 屏幕尺寸信息类型定义
export type DisplayMetrics = {
  fontScale: number,
  height: number,
  scale: number,
  width: number
};

type DimensionsValue = {
  window: DisplayMetrics,
  screen: DisplayMetrics
};

type DimensionKey = 'window' | 'screen';

type DimensionEventListenerType = 'change';

// 初始化维度数据
const dimensions: DimensionsValue = {
  window: {
    fontScale: 1,
    height: 0,
    scale: 1,
    width: 0
  },
  screen: {
    fontScale: 1,
    height: 0,
    scale: 1,
    width: 0
  }
};

// 初始化系统信息
let systemInfo: Taro.getSystemInfoSync.Result | null = null;

/**
 * 初始化系统信息
 */
function initSystemInfo() {
  try {
    // 获取系统信息
    systemInfo = Taro.getSystemInfoSync();
    
    // 更新屏幕尺寸信息
    if (systemInfo) {
      // 窗口尺寸
      dimensions.window = {
        fontScale: 1,
        height: systemInfo.windowHeight,
        scale: systemInfo.pixelRatio || 1,
        width: systemInfo.windowWidth
      };
      
      // 屏幕尺寸
      dimensions.screen = {
        fontScale: 1,
        height: systemInfo.screenHeight,
        scale: systemInfo.pixelRatio || 1,
        width: systemInfo.screenWidth
      };
    }
  } catch (error) {
    console.error('获取系统信息失败', error);
  }
}

// 立即初始化系统信息
initSystemInfo();

// 事件监听器存储
const listeners = new Map();

/**
 * Dimensions类提供了与react-native兼容的接口
 */
export default class Dimensions {
  /**
   * 获取指定尺寸数据
   * @param dimension - 要获取的尺寸类型('window'或'screen')
   * @returns 对应的尺寸信息
   */
  static get(dimension: DimensionKey): DisplayMetrics {
    // 如果系统信息尚未初始化，则尝试初始化
    if (!systemInfo) {
      initSystemInfo();
    }
    
    if (!dimensions[dimension]) {
      console.warn(`没有为${dimension}设置尺寸`);
    }
    return dimensions[dimension];
  }

  /**
   * 设置初始尺寸数据，仅用于测试或特殊场景
   * @param initialDimensions - 初始尺寸数据
   */
  static set(initialDimensions: DimensionsValue): void {
    if (initialDimensions) {
      // Taro环境下我们仍然允许设置，这与RN不同，但更灵活
      if (initialDimensions.screen != null) {
        dimensions.screen = initialDimensions.screen;
      }
      if (initialDimensions.window != null) {
        dimensions.window = initialDimensions.window;
      }
    }
  }

  /**
   * 添加尺寸变化的事件监听器
   * @param type - 事件类型
   * @param handler - 事件处理函数
   * @returns 包含remove方法的对象，用于移除监听器
   */
  static addEventListener(
    _type: DimensionEventListenerType,
    handler: (dimensions: DimensionsValue) => void
  ) {
    // 在Taro中，可以监听小程序的窗口尺寸变化事件
    try {
      const callback = () => {
        // 重新获取系统信息
        initSystemInfo();
        // 调用处理函数
        handler(dimensions);
      };
      
      // 注册监听器
      Taro.onWindowResize(callback);
      listeners.set(handler, callback);
      
      return {
        remove: () => {
          // 移除监听器
          Taro.offWindowResize(callback);
          listeners.delete(handler);
        }
      };
    } catch (error) {
      console.error('添加窗口尺寸变化监听器失败', error);
      return { remove: () => {} };
    }
  }

  /**
   * 移除尺寸变化的事件监听器
   * @param _type - 事件类型
   * @param handler - 事件处理函数
   */
  static removeEventListener(
    _type: DimensionEventListenerType,
    handler: (dimensions: DimensionsValue) => void
  ): void {
    if (listeners.has(handler)) {
      const callback = listeners.get(handler);
      try {
        Taro.offWindowResize(callback);
        listeners.delete(handler);
      } catch (error) {
        console.error('移除窗口尺寸变化监听器失败', error);
      }
    }
  }
}
