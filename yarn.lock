# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@babel/code-frame@^7.0.0":
  version "7.27.1"
  resolved "http://registry.m.jd.com/@babel/code-frame/download/@babel/code-frame-7.27.1.tgz#200f715e66d52a23b221a9435534a91cc13ad5be"
  integrity sha1-IA9xXmbVKiOyIalDVTSpHME61b4=
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    js-tokens "^4.0.0"
    picocolors "^1.1.1"

"@babel/helper-validator-identifier@^7.27.1":
  version "7.27.1"
  resolved "http://registry.m.jd.com/@babel/helper-validator-identifier/download/@babel/helper-validator-identifier-7.27.1.tgz#a7054dcc145a967dd4dc8fee845a57c1316c9df8"
  integrity sha1-pwVNzBRaln3U3I/uhFpXwTFsnfg=

"@csstools/css-parser-algorithms@^3.0.4":
  version "3.0.4"
  resolved "http://registry.m.jd.com/@csstools/css-parser-algorithms/download/@csstools/css-parser-algorithms-3.0.4.tgz#74426e93bd1c4dcab3e441f5cc7ba4fb35d94356"
  integrity sha1-dEJuk70cTcqz5EH1zHuk+zXZQ1Y=

"@csstools/css-tokenizer@^3.0.3":
  version "3.0.3"
  resolved "http://registry.m.jd.com/@csstools/css-tokenizer/download/@csstools/css-tokenizer-3.0.3.tgz#a5502c8539265fecbd873c1e395a890339f119c2"
  integrity sha1-pVAshTkmX+y9hzweOVqJAznxGcI=

"@csstools/media-query-list-parser@^4.0.2":
  version "4.0.2"
  resolved "http://registry.m.jd.com/@csstools/media-query-list-parser/download/@csstools/media-query-list-parser-4.0.2.tgz#e80e17eba1693fceafb8d6f2cfc68c0e7a9ab78a"
  integrity sha1-6A4X66FpP86vuNbyz8aMDnqat4o=

"@csstools/selector-specificity@^5.0.0":
  version "5.0.0"
  resolved "http://registry.m.jd.com/@csstools/selector-specificity/download/@csstools/selector-specificity-5.0.0.tgz#037817b574262134cabd68fc4ec1a454f168407b"
  integrity sha1-A3gXtXQmITTKvWj8TsGkVPFoQHs=

"@dual-bundle/import-meta-resolve@^4.1.0":
  version "4.1.0"
  resolved "http://registry.m.jd.com/@dual-bundle/import-meta-resolve/download/@dual-bundle/import-meta-resolve-4.1.0.tgz#519c1549b0e147759e7825701ecffd25e5819f7b"
  integrity sha1-UZwVSbDhR3WeeCVwHs/9JeWBn3s=

"@eslint-community/eslint-utils@^4.2.0", "@eslint-community/eslint-utils@^4.7.0":
  version "4.7.0"
  resolved "http://registry.m.jd.com/@eslint-community/eslint-utils/download/@eslint-community/eslint-utils-4.7.0.tgz#607084630c6c033992a082de6e6fbc1a8b52175a"
  integrity sha1-YHCEYwxsAzmSoILebm+8GotSF1o=
  dependencies:
    eslint-visitor-keys "^3.4.3"

"@eslint-community/regexpp@^4.10.0", "@eslint-community/regexpp@^4.6.1":
  version "4.12.1"
  resolved "http://registry.m.jd.com/@eslint-community/regexpp/download/@eslint-community/regexpp-4.12.1.tgz#cfc6cffe39df390a3841cde2abccf92eaa7ae0e0"
  integrity sha1-z8bP/jnfOQo4Qc3iq8z5Lqp64OA=

"@eslint/eslintrc@^2.1.4":
  version "2.1.4"
  resolved "http://registry.m.jd.com/@eslint/eslintrc/download/@eslint/eslintrc-2.1.4.tgz#388a269f0f25c1b6adc317b5a2c55714894c70ad"
  integrity sha1-OIomnw8lwbatwxe1osVXFIlMcK0=
  dependencies:
    ajv "^6.12.4"
    debug "^4.3.2"
    espree "^9.6.0"
    globals "^13.19.0"
    ignore "^5.2.0"
    import-fresh "^3.2.1"
    js-yaml "^4.1.0"
    minimatch "^3.1.2"
    strip-json-comments "^3.1.1"

"@eslint/js@8.57.1":
  version "8.57.1"
  resolved "http://registry.m.jd.com/@eslint/js/download/@eslint/js-8.57.1.tgz#de633db3ec2ef6a3c89e2f19038063e8a122e2c2"
  integrity sha1-3mM9s+wu9qPIni8ZA4Bj6KEi4sI=

"@humanwhocodes/config-array@^0.13.0":
  version "0.13.0"
  resolved "http://registry.m.jd.com/@humanwhocodes/config-array/download/@humanwhocodes/config-array-0.13.0.tgz#fb907624df3256d04b9aa2df50d7aa97ec648748"
  integrity sha1-+5B2JN8yVtBLmqLfUNeql+xkh0g=
  dependencies:
    "@humanwhocodes/object-schema" "^2.0.3"
    debug "^4.3.1"
    minimatch "^3.0.5"

"@humanwhocodes/module-importer@^1.0.1":
  version "1.0.1"
  resolved "http://registry.m.jd.com/@humanwhocodes/module-importer/download/@humanwhocodes/module-importer-1.0.1.tgz#af5b2691a22b44be847b0ca81641c5fb6ad0172c"
  integrity sha1-r1smkaIrRL6EewyoFkHF+2rQFyw=

"@humanwhocodes/object-schema@^2.0.3":
  version "2.0.3"
  resolved "http://registry.m.jd.com/@humanwhocodes/object-schema/download/@humanwhocodes/object-schema-2.0.3.tgz#4a2868d75d6d6963e423bcf90b7fd1be343409d3"
  integrity sha1-Siho111taWPkI7z5C3/RvjQ0CdM=

"@keyv/serialize@^1.0.3":
  version "1.0.3"
  resolved "http://registry.m.jd.com/@keyv/serialize/download/@keyv/serialize-1.0.3.tgz#e0fe3710e2a379cb0490cd41e5a5ffa2bab58bf6"
  integrity sha1-4P43EOKjecsEkM1B5aX/orq1i/Y=
  dependencies:
    buffer "^6.0.3"

"@nodelib/fs.scandir@2.1.5":
  version "2.1.5"
  resolved "http://registry.m.jd.com/@nodelib/fs.scandir/download/@nodelib/fs.scandir-2.1.5.tgz#7619c2eb21b25483f6d167548b4cfd5a7488c3d5"
  integrity sha1-dhnC6yGyVIP20WdUi0z9WnSIw9U=
  dependencies:
    "@nodelib/fs.stat" "2.0.5"
    run-parallel "^1.1.9"

"@nodelib/fs.stat@2.0.5", "@nodelib/fs.stat@^2.0.2":
  version "2.0.5"
  resolved "http://registry.m.jd.com/@nodelib/fs.stat/download/@nodelib/fs.stat-2.0.5.tgz#5bd262af94e9d25bd1e71b05deed44876a222e8b"
  integrity sha1-W9Jir5Tp0lvR5xsF3u1Eh2oiLos=

"@nodelib/fs.walk@^1.2.3", "@nodelib/fs.walk@^1.2.8":
  version "1.2.8"
  resolved "http://registry.m.jd.com/@nodelib/fs.walk/download/@nodelib/fs.walk-1.2.8.tgz#e95737e8bb6746ddedf69c556953494f196fe69a"
  integrity sha1-6Vc36LtnRt3t9pxVaVNJTxlv5po=
  dependencies:
    "@nodelib/fs.scandir" "2.1.5"
    fastq "^1.6.0"

"@react-native/virtualized-lists@^0.72.4":
  version "0.72.8"
  resolved "http://registry.m.jd.com/@react-native/virtualized-lists/download/@react-native/virtualized-lists-0.72.8.tgz#a2c6a91ea0f1d40eb5a122fb063daedb92ed1dc3"
  integrity sha1-osapHqDx1A61oSL7Bj2u25LtHcM=
  dependencies:
    invariant "^2.2.4"
    nullthrows "^1.1.1"

"@types/react-native@^0.72.8":
  version "0.72.8"
  resolved "http://registry.m.jd.com/@types/react-native/download/@types/react-native-0.72.8.tgz#eb6238fab289f5f132f7ccf138bdfe6f21ed93e1"
  integrity sha1-62I4+rKJ9fEy98zxOL3+byHtk+E=
  dependencies:
    "@react-native/virtualized-lists" "^0.72.4"
    "@types/react" "*"

"@types/react@*", "@types/react@^19.1.4":
  version "19.1.4"
  resolved "http://registry.m.jd.com/@types/react/download/@types/react-19.1.4.tgz#4d125f014d6ac26b4759775698db118701e314fe"
  integrity sha1-TRJfAU1qwmtHWXdWmNsRhwHjFP4=
  dependencies:
    csstype "^3.0.2"

"@typescript-eslint/eslint-plugin@^8.32.1":
  version "8.32.1"
  resolved "http://registry.m.jd.com/@typescript-eslint/eslint-plugin/download/@typescript-eslint/eslint-plugin-8.32.1.tgz#9185b3eaa3b083d8318910e12d56c68b3c4f45b4"
  integrity sha1-kYWz6qOwg9gxiRDhLVbGizxPRbQ=
  dependencies:
    "@eslint-community/regexpp" "^4.10.0"
    "@typescript-eslint/scope-manager" "8.32.1"
    "@typescript-eslint/type-utils" "8.32.1"
    "@typescript-eslint/utils" "8.32.1"
    "@typescript-eslint/visitor-keys" "8.32.1"
    graphemer "^1.4.0"
    ignore "^7.0.0"
    natural-compare "^1.4.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/parser@^8.32.1":
  version "8.32.1"
  resolved "http://registry.m.jd.com/@typescript-eslint/parser/download/@typescript-eslint/parser-8.32.1.tgz#18b0e53315e0bc22b2619d398ae49a968370935e"
  integrity sha1-GLDlMxXgvCKyYZ05iuSaloNwk14=
  dependencies:
    "@typescript-eslint/scope-manager" "8.32.1"
    "@typescript-eslint/types" "8.32.1"
    "@typescript-eslint/typescript-estree" "8.32.1"
    "@typescript-eslint/visitor-keys" "8.32.1"
    debug "^4.3.4"

"@typescript-eslint/scope-manager@8.32.1":
  version "8.32.1"
  resolved "http://registry.m.jd.com/@typescript-eslint/scope-manager/download/@typescript-eslint/scope-manager-8.32.1.tgz#9a6bf5fb2c5380e14fe9d38ccac6e4bbe17e8afc"
  integrity sha1-mmv1+yxTgOFP6dOMysbku+F+ivw=
  dependencies:
    "@typescript-eslint/types" "8.32.1"
    "@typescript-eslint/visitor-keys" "8.32.1"

"@typescript-eslint/type-utils@8.32.1":
  version "8.32.1"
  resolved "http://registry.m.jd.com/@typescript-eslint/type-utils/download/@typescript-eslint/type-utils-8.32.1.tgz#b9292a45f69ecdb7db74d1696e57d1a89514d21e"
  integrity sha1-uSkqRfaezbfbdNFpblfRqJUU0h4=
  dependencies:
    "@typescript-eslint/typescript-estree" "8.32.1"
    "@typescript-eslint/utils" "8.32.1"
    debug "^4.3.4"
    ts-api-utils "^2.1.0"

"@typescript-eslint/types@8.32.1":
  version "8.32.1"
  resolved "http://registry.m.jd.com/@typescript-eslint/types/download/@typescript-eslint/types-8.32.1.tgz#b19fe4ac0dc08317bae0ce9ec1168123576c1d4b"
  integrity sha1-sZ/krA3Agxe64M6ewRaBI1dsHUs=

"@typescript-eslint/typescript-estree@8.32.1":
  version "8.32.1"
  resolved "http://registry.m.jd.com/@typescript-eslint/typescript-estree/download/@typescript-eslint/typescript-estree-8.32.1.tgz#9023720ca4ecf4f59c275a05b5fed69b1276face"
  integrity sha1-kCNyDKTs9PWcJ1oFtf7WmxJ2+s4=
  dependencies:
    "@typescript-eslint/types" "8.32.1"
    "@typescript-eslint/visitor-keys" "8.32.1"
    debug "^4.3.4"
    fast-glob "^3.3.2"
    is-glob "^4.0.3"
    minimatch "^9.0.4"
    semver "^7.6.0"
    ts-api-utils "^2.1.0"

"@typescript-eslint/utils@8.32.1":
  version "8.32.1"
  resolved "http://registry.m.jd.com/@typescript-eslint/utils/download/@typescript-eslint/utils-8.32.1.tgz#4d6d5d29b9e519e9a85e9a74e9f7bdb58abe9704"
  integrity sha1-TW1dKbnlGemoXpp06fe9tYq+lwQ=
  dependencies:
    "@eslint-community/eslint-utils" "^4.7.0"
    "@typescript-eslint/scope-manager" "8.32.1"
    "@typescript-eslint/types" "8.32.1"
    "@typescript-eslint/typescript-estree" "8.32.1"

"@typescript-eslint/visitor-keys@8.32.1":
  version "8.32.1"
  resolved "http://registry.m.jd.com/@typescript-eslint/visitor-keys/download/@typescript-eslint/visitor-keys-8.32.1.tgz#4321395cc55c2eb46036cbbb03e101994d11ddca"
  integrity sha1-QyE5XMVcLrRgNsu7A+EBmU0R3co=
  dependencies:
    "@typescript-eslint/types" "8.32.1"
    eslint-visitor-keys "^4.2.0"

"@ungap/structured-clone@^1.2.0":
  version "1.3.0"
  resolved "http://registry.m.jd.com/@ungap/structured-clone/download/@ungap/structured-clone-1.3.0.tgz#d06bbb384ebcf6c505fde1c3d0ed4ddffe0aaff8"
  integrity sha1-0Gu7OE689sUF/eHD0O1N3/4Kr/g=

acorn-jsx@^5.3.2:
  version "5.3.2"
  resolved "http://registry.m.jd.com/acorn-jsx/download/acorn-jsx-5.3.2.tgz#7ed5bb55908b3b2f1bc55c6af1653bada7f07937"
  integrity sha1-ftW7VZCLOy8bxVxq8WU7rafweTc=

acorn@^8.9.0:
  version "8.14.1"
  resolved "http://registry.m.jd.com/acorn/download/acorn-8.14.1.tgz#721d5dc10f7d5b5609a891773d47731796935dfb"
  integrity sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=

ajv@^6.12.4:
  version "6.12.6"
  resolved "http://registry.m.jd.com/ajv/download/ajv-6.12.6.tgz#baf5a62e802b07d977034586f8c3baf5adf26df4"
  integrity sha1-uvWmLoArB9l3A0WG+MO69a3ybfQ=
  dependencies:
    fast-deep-equal "^3.1.1"
    fast-json-stable-stringify "^2.0.0"
    json-schema-traverse "^0.4.1"
    uri-js "^4.2.2"

ajv@^8.0.1:
  version "8.17.1"
  resolved "http://registry.m.jd.com/ajv/download/ajv-8.17.1.tgz#37d9a5c776af6bc92d7f4f9510eba4c0a60d11a6"
  integrity sha1-N9mlx3ava8ktf0+VEOukwKYNEaY=
  dependencies:
    fast-deep-equal "^3.1.3"
    fast-uri "^3.0.1"
    json-schema-traverse "^1.0.0"
    require-from-string "^2.0.2"

ansi-regex@^5.0.1:
  version "5.0.1"
  resolved "http://registry.m.jd.com/ansi-regex/download/ansi-regex-5.0.1.tgz#082cb2c89c9fe8659a311a53bd6a4dc5301db304"
  integrity sha1-CCyyyJyf6GWaMRpTvWpNxTAdswQ=

ansi-styles@^4.0.0, ansi-styles@^4.1.0:
  version "4.3.0"
  resolved "http://registry.m.jd.com/ansi-styles/download/ansi-styles-4.3.0.tgz#edd803628ae71c04c85ae7a0906edad34b648937"
  integrity sha1-7dgDYornHATIWuegkG7a00tkiTc=
  dependencies:
    color-convert "^2.0.1"

argparse@^2.0.1:
  version "2.0.1"
  resolved "http://registry.m.jd.com/argparse/download/argparse-2.0.1.tgz#246f50f3ca78a3240f6c997e8a9bd1eac49e4b38"
  integrity sha1-JG9Q88p4oyQPbJl+ipvR6sSeSzg=

array-union@^2.1.0:
  version "2.1.0"
  resolved "http://registry.m.jd.com/array-union/download/array-union-2.1.0.tgz#b798420adbeb1de828d84acd8a2e23d3efe85e8d"
  integrity sha1-t5hCCtvrHego2ErNii4j0+/oXo0=

astral-regex@^2.0.0:
  version "2.0.0"
  resolved "http://registry.m.jd.com/astral-regex/download/astral-regex-2.0.0.tgz#483143c567aeed4785759c0865786dc77d7d2e31"
  integrity sha1-SDFDxWeu7UeFdZwIZXhtx319LjE=

balanced-match@^1.0.0:
  version "1.0.2"
  resolved "http://registry.m.jd.com/balanced-match/download/balanced-match-1.0.2.tgz#e83e3a7e3f300b34cb9d87f615fa0cbf357690ee"
  integrity sha1-6D46fj8wCzTLnYf2FfoMvzV2kO4=

balanced-match@^2.0.0:
  version "2.0.0"
  resolved "http://registry.m.jd.com/balanced-match/download/balanced-match-2.0.0.tgz#dc70f920d78db8b858535795867bf48f820633d9"
  integrity sha1-3HD5INeNuLhYU1eVhnv0j4IGM9k=

base64-js@^1.3.1:
  version "1.5.1"
  resolved "http://registry.m.jd.com/base64-js/download/base64-js-1.5.1.tgz#1b1b440160a5bf7ad40b650f095963481903930a"
  integrity sha1-GxtEAWClv3rUC2UPCVljSBkDkwo=

brace-expansion@^1.1.7:
  version "1.1.11"
  resolved "http://registry.m.jd.com/brace-expansion/download/brace-expansion-1.1.11.tgz#3c7fcbf529d87226f3d2f52b966ff5271eb441dd"
  integrity sha1-PH/L9SnYcibz0vUrlm/1Jx60Qd0=
  dependencies:
    balanced-match "^1.0.0"
    concat-map "0.0.1"

brace-expansion@^2.0.1:
  version "2.0.1"
  resolved "http://registry.m.jd.com/brace-expansion/download/brace-expansion-2.0.1.tgz#1edc459e0f0c548486ecf9fc99f2221364b9a0ae"
  integrity sha1-HtxFng8MVISG7Pn8mfIiE2S5oK4=
  dependencies:
    balanced-match "^1.0.0"

braces@^3.0.3:
  version "3.0.3"
  resolved "http://registry.m.jd.com/braces/download/braces-3.0.3.tgz#490332f40919452272d55a8480adc0c441358789"
  integrity sha1-SQMy9AkZRSJy1VqEgK3AxEE1h4k=
  dependencies:
    fill-range "^7.1.1"

buffer@^6.0.3:
  version "6.0.3"
  resolved "http://registry.m.jd.com/buffer/download/buffer-6.0.3.tgz#2ace578459cc8fbe2a70aaa8f52ee63b6a74c6c6"
  integrity sha1-Ks5XhFnMj74qcKqo9S7mO2p0xsY=
  dependencies:
    base64-js "^1.3.1"
    ieee754 "^1.2.1"

cacheable@^1.9.0:
  version "1.9.0"
  resolved "http://registry.m.jd.com/cacheable/download/cacheable-1.9.0.tgz#57e3565c311d66371eeb5f2070b6615d43b89711"
  integrity sha1-V+NWXDEdZjce618gcLZhXUO4lxE=
  dependencies:
    hookified "^1.8.2"
    keyv "^5.3.3"

callsites@^3.0.0:
  version "3.1.0"
  resolved "http://registry.m.jd.com/callsites/download/callsites-3.1.0.tgz#b3630abd8943432f54b3f0519238e33cd7df2f73"
  integrity sha1-s2MKvYlDQy9Us/BRkjjjPNffL3M=

chalk@^4.0.0:
  version "4.1.2"
  resolved "http://registry.m.jd.com/chalk/download/chalk-4.1.2.tgz#aac4e2b7734a740867aeb16bf02aad556a1e7a01"
  integrity sha1-qsTit3NKdAhnrrFr8CqtVWoeegE=
  dependencies:
    ansi-styles "^4.1.0"
    supports-color "^7.1.0"

color-convert@^2.0.1:
  version "2.0.1"
  resolved "http://registry.m.jd.com/color-convert/download/color-convert-2.0.1.tgz#72d3a68d598c9bdb3af2ad1e84f21d896abd4de3"
  integrity sha1-ctOmjVmMm9s68q0ehPIdiWq9TeM=
  dependencies:
    color-name "~1.1.4"

color-name@~1.1.4:
  version "1.1.4"
  resolved "http://registry.m.jd.com/color-name/download/color-name-1.1.4.tgz#c2a09a87acbde69543de6f63fa3995c826c536a2"
  integrity sha1-wqCah6y95pVD3m9j+jmVyCbFNqI=

colord@^2.9.3:
  version "2.9.3"
  resolved "http://registry.m.jd.com/colord/download/colord-2.9.3.tgz#4f8ce919de456f1d5c1c368c307fe20f3e59fb43"
  integrity sha1-T4zpGd5Fbx1cHDaMMH/iDz5Z+0M=

concat-map@0.0.1:
  version "0.0.1"
  resolved "http://registry.m.jd.com/concat-map/download/concat-map-0.0.1.tgz#d8a96bd77fd68df7793a73036a3ba0d5405d477b"
  integrity sha1-2Klr13/Wjfd5OnMDajug1UBdR3s=

cosmiconfig@^9.0.0:
  version "9.0.0"
  resolved "http://registry.m.jd.com/cosmiconfig/download/cosmiconfig-9.0.0.tgz#34c3fc58287b915f3ae905ab6dc3de258b55ad9d"
  integrity sha1-NMP8WCh7kV866QWrbcPeJYtVrZ0=
  dependencies:
    env-paths "^2.2.1"
    import-fresh "^3.3.0"
    js-yaml "^4.1.0"
    parse-json "^5.2.0"

cross-spawn@^7.0.2:
  version "7.0.6"
  resolved "http://registry.m.jd.com/cross-spawn/download/cross-spawn-7.0.6.tgz#8a58fe78f00dcd70c370451759dfbfaf03e8ee9f"
  integrity sha1-ilj+ePANzXDDcEUXWd+/rwPo7p8=
  dependencies:
    path-key "^3.1.0"
    shebang-command "^2.0.0"
    which "^2.0.1"

css-functions-list@^3.2.3:
  version "3.2.3"
  resolved "http://registry.m.jd.com/css-functions-list/download/css-functions-list-3.2.3.tgz#95652b0c24f0f59b291a9fc386041a19d4f40dbe"
  integrity sha1-lWUrDCTw9ZspGp/DhgQaGdT0Db4=

css-tree@^3.0.1, css-tree@^3.1.0:
  version "3.1.0"
  resolved "http://registry.m.jd.com/css-tree/download/css-tree-3.1.0.tgz#7aabc035f4e66b5c86f54570d55e05b1346eb0fd"
  integrity sha1-eqvANfTma1yG9UVw1V4FsTRusP0=
  dependencies:
    mdn-data "2.12.2"
    source-map-js "^1.0.1"

cssesc@^3.0.0:
  version "3.0.0"
  resolved "http://registry.m.jd.com/cssesc/download/cssesc-3.0.0.tgz#37741919903b868565e1c09ea747445cd18983ee"
  integrity sha1-N3QZGZA7hoVl4cCep0dEXNGJg+4=

csstype@^3.0.2:
  version "3.1.3"
  resolved "http://registry.m.jd.com/csstype/download/csstype-3.1.3.tgz#d80ff294d114fb0e6ac500fbf85b60137d7eff81"
  integrity sha1-2A/ylNEU+w5qxQD7+FtgE31+/4E=

debug@^4.3.1, debug@^4.3.2, debug@^4.3.4, debug@^4.3.7:
  version "4.4.1"
  resolved "http://registry.m.jd.com/debug/download/debug-4.4.1.tgz#e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

deep-is@^0.1.3:
  version "0.1.4"
  resolved "http://registry.m.jd.com/deep-is/download/deep-is-0.1.4.tgz#a6f2dce612fadd2ef1f519b73551f17e85199831"
  integrity sha1-pvLc5hL63S7x9Rm3NVHxfoUZmDE=

dir-glob@^3.0.1:
  version "3.0.1"
  resolved "http://registry.m.jd.com/dir-glob/download/dir-glob-3.0.1.tgz#56dbf73d992a4a93ba1584f4534063fd2e41717f"
  integrity sha1-Vtv3PZkqSpO6FYT0U0Bj/S5BcX8=
  dependencies:
    path-type "^4.0.0"

doctrine@^3.0.0:
  version "3.0.0"
  resolved "http://registry.m.jd.com/doctrine/download/doctrine-3.0.0.tgz#addebead72a6574db783639dc87a121773973961"
  integrity sha1-rd6+rXKmV023g2OdyHoSF3OXOWE=
  dependencies:
    esutils "^2.0.2"

emoji-regex@^8.0.0:
  version "8.0.0"
  resolved "http://registry.m.jd.com/emoji-regex/download/emoji-regex-8.0.0.tgz#e818fd69ce5ccfcb404594f842963bf53164cc37"
  integrity sha1-6Bj9ac5cz8tARZT4QpY79TFkzDc=

env-paths@^2.2.1:
  version "2.2.1"
  resolved "http://registry.m.jd.com/env-paths/download/env-paths-2.2.1.tgz#420399d416ce1fbe9bc0a07c62fa68d67fd0f8f2"
  integrity sha1-QgOZ1BbOH76bwKB8Yvpo1n/Q+PI=

error-ex@^1.3.1:
  version "1.3.2"
  resolved "http://registry.m.jd.com/error-ex/download/error-ex-1.3.2.tgz#b4ac40648107fdcdcfae242f428bea8a14d4f1bf"
  integrity sha1-tKxAZIEH/c3PriQvQovqihTU8b8=
  dependencies:
    is-arrayish "^0.2.1"

escape-string-regexp@^4.0.0:
  version "4.0.0"
  resolved "http://registry.m.jd.com/escape-string-regexp/download/escape-string-regexp-4.0.0.tgz#14ba83a5d373e3d311e5afca29cf5bfad965bf34"
  integrity sha1-FLqDpdNz49MR5a/KKc9b+tllvzQ=

eslint-scope@^7.2.2:
  version "7.2.2"
  resolved "http://registry.m.jd.com/eslint-scope/download/eslint-scope-7.2.2.tgz#deb4f92563390f32006894af62a22dba1c46423f"
  integrity sha1-3rT5JWM5DzIAaJSvYqItuhxGQj8=
  dependencies:
    esrecurse "^4.3.0"
    estraverse "^5.2.0"

eslint-visitor-keys@^3.4.1, eslint-visitor-keys@^3.4.3:
  version "3.4.3"
  resolved "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-3.4.3.tgz#0cd72fe8550e3c2eae156a96a4dddcd1c8ac5800"
  integrity sha1-DNcv6FUOPC6uFWqWpN3c0cisWAA=

eslint-visitor-keys@^4.2.0:
  version "4.2.0"
  resolved "http://registry.m.jd.com/eslint-visitor-keys/download/eslint-visitor-keys-4.2.0.tgz#687bacb2af884fcdda8a6e7d65c606f46a14cd45"
  integrity sha1-aHussq+IT83aim59ZcYG9GoUzUU=

eslint@^8.57.1:
  version "8.57.1"
  resolved "http://registry.m.jd.com/eslint/download/eslint-8.57.1.tgz#7df109654aba7e3bbe5c8eae533c5e461d3c6ca9"
  integrity sha1-ffEJZUq6fju+XI6uUzxeRh08bKk=
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.6.1"
    "@eslint/eslintrc" "^2.1.4"
    "@eslint/js" "8.57.1"
    "@humanwhocodes/config-array" "^0.13.0"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@nodelib/fs.walk" "^1.2.8"
    "@ungap/structured-clone" "^1.2.0"
    ajv "^6.12.4"
    chalk "^4.0.0"
    cross-spawn "^7.0.2"
    debug "^4.3.2"
    doctrine "^3.0.0"
    escape-string-regexp "^4.0.0"
    eslint-scope "^7.2.2"
    eslint-visitor-keys "^3.4.3"
    espree "^9.6.1"
    esquery "^1.4.2"
    esutils "^2.0.2"
    fast-deep-equal "^3.1.3"
    file-entry-cache "^6.0.1"
    find-up "^5.0.0"
    glob-parent "^6.0.2"
    globals "^13.19.0"
    graphemer "^1.4.0"
    ignore "^5.2.0"
    imurmurhash "^0.1.4"
    is-glob "^4.0.0"
    is-path-inside "^3.0.3"
    js-yaml "^4.1.0"
    json-stable-stringify-without-jsonify "^1.0.1"
    levn "^0.4.1"
    lodash.merge "^4.6.2"
    minimatch "^3.1.2"
    natural-compare "^1.4.0"
    optionator "^0.9.3"
    strip-ansi "^6.0.1"
    text-table "^0.2.0"

espree@^9.6.0, espree@^9.6.1:
  version "9.6.1"
  resolved "http://registry.m.jd.com/espree/download/espree-9.6.1.tgz#a2a17b8e434690a5432f2f8018ce71d331a48c6f"
  integrity sha1-oqF7jkNGkKVDLy+AGM5x0zGkjG8=
  dependencies:
    acorn "^8.9.0"
    acorn-jsx "^5.3.2"
    eslint-visitor-keys "^3.4.1"

esquery@^1.4.2:
  version "1.6.0"
  resolved "http://registry.m.jd.com/esquery/download/esquery-1.6.0.tgz#91419234f804d852a82dceec3e16cdc22cf9dae7"
  integrity sha1-kUGSNPgE2FKoLc7sPhbNwiz52uc=
  dependencies:
    estraverse "^5.1.0"

esrecurse@^4.3.0:
  version "4.3.0"
  resolved "http://registry.m.jd.com/esrecurse/download/esrecurse-4.3.0.tgz#7ad7964d679abb28bee72cec63758b1c5d2c9921"
  integrity sha1-eteWTWeauyi+5yzsY3WLHF0smSE=
  dependencies:
    estraverse "^5.2.0"

estraverse@^5.1.0, estraverse@^5.2.0:
  version "5.3.0"
  resolved "http://registry.m.jd.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://registry.m.jd.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

fast-deep-equal@^3.1.1, fast-deep-equal@^3.1.3:
  version "3.1.3"
  resolved "http://registry.m.jd.com/fast-deep-equal/download/fast-deep-equal-3.1.3.tgz#3a7d56b559d6cbc3eb512325244e619a65c6c525"
  integrity sha1-On1WtVnWy8PrUSMlJE5hmmXGxSU=

fast-glob@^3.2.9, fast-glob@^3.3.2, fast-glob@^3.3.3:
  version "3.3.3"
  resolved "http://registry.m.jd.com/fast-glob/download/fast-glob-3.3.3.tgz#d06d585ce8dba90a16b0505c543c3ccfb3aeb818"
  integrity sha1-0G1YXOjbqQoWsFBcVDw8z7OuuBg=
  dependencies:
    "@nodelib/fs.stat" "^2.0.2"
    "@nodelib/fs.walk" "^1.2.3"
    glob-parent "^5.1.2"
    merge2 "^1.3.0"
    micromatch "^4.0.8"

fast-json-stable-stringify@^2.0.0:
  version "2.1.0"
  resolved "http://registry.m.jd.com/fast-json-stable-stringify/download/fast-json-stable-stringify-2.1.0.tgz#874bf69c6f404c2b5d99c481341399fd55892633"
  integrity sha1-h0v2nG9ATCtdmcSBNBOZ/VWJJjM=

fast-levenshtein@^2.0.6:
  version "2.0.6"
  resolved "http://registry.m.jd.com/fast-levenshtein/download/fast-levenshtein-2.0.6.tgz#3d8a5c66883a16a30ca8643e851f19baa7797917"
  integrity sha1-PYpcZog6FqMMqGQ+hR8Zuqd5eRc=

fast-uri@^3.0.1:
  version "3.0.6"
  resolved "http://registry.m.jd.com/fast-uri/download/fast-uri-3.0.6.tgz#88f130b77cfaea2378d56bf970dea21257a68748"
  integrity sha1-iPEwt3z66iN41Wv5cN6iElemh0g=

fastest-levenshtein@^1.0.16:
  version "1.0.16"
  resolved "http://registry.m.jd.com/fastest-levenshtein/download/fastest-levenshtein-1.0.16.tgz#210e61b6ff181de91ea9b3d1b84fdedd47e034e5"
  integrity sha1-IQ5htv8YHekeqbPRuE/e3UfgNOU=

fastq@^1.6.0:
  version "1.19.1"
  resolved "http://registry.m.jd.com/fastq/download/fastq-1.19.1.tgz#d50eaba803c8846a883c16492821ebcd2cda55f5"
  integrity sha1-1Q6rqAPIhGqIPBZJKCHrzSzaVfU=
  dependencies:
    reusify "^1.0.4"

file-entry-cache@^10.0.8:
  version "10.1.0"
  resolved "http://registry.m.jd.com/file-entry-cache/download/file-entry-cache-10.1.0.tgz#54c0117fe76425e9f08a44a3a08bedde0cd93fe8"
  integrity sha1-VMARf+dkJenwikSjoIvt3gzZP+g=
  dependencies:
    flat-cache "^6.1.9"

file-entry-cache@^6.0.1:
  version "6.0.1"
  resolved "http://registry.m.jd.com/file-entry-cache/download/file-entry-cache-6.0.1.tgz#211b2dd9659cb0394b073e7323ac3c933d522027"
  integrity sha1-IRst2WWcsDlLBz5zI6w8kz1SICc=
  dependencies:
    flat-cache "^3.0.4"

fill-range@^7.1.1:
  version "7.1.1"
  resolved "http://registry.m.jd.com/fill-range/download/fill-range-7.1.1.tgz#44265d3cac07e3ea7dc247516380643754a05292"
  integrity sha1-RCZdPKwH4+p9wkdRY4BkN1SgUpI=
  dependencies:
    to-regex-range "^5.0.1"

find-up@^5.0.0:
  version "5.0.0"
  resolved "http://registry.m.jd.com/find-up/download/find-up-5.0.0.tgz#4c92819ecb7083561e4f4a240a86be5198f536fc"
  integrity sha1-TJKBnstwg1YeT0okCoa+UZj1Nvw=
  dependencies:
    locate-path "^6.0.0"
    path-exists "^4.0.0"

flat-cache@^3.0.4:
  version "3.2.0"
  resolved "http://registry.m.jd.com/flat-cache/download/flat-cache-3.2.0.tgz#2c0c2d5040c99b1632771a9d105725c0115363ee"
  integrity sha1-LAwtUEDJmxYydxqdEFclwBFTY+4=
  dependencies:
    flatted "^3.2.9"
    keyv "^4.5.3"
    rimraf "^3.0.2"

flat-cache@^6.1.9:
  version "6.1.9"
  resolved "http://registry.m.jd.com/flat-cache/download/flat-cache-6.1.9.tgz#6f512c4ab81c2057577fdb30c2f64022d43db2e7"
  integrity sha1-b1EsSrgcIFdXf9swwvZAItQ9suc=
  dependencies:
    cacheable "^1.9.0"
    flatted "^3.3.3"
    hookified "^1.8.2"

flatted@^3.2.9, flatted@^3.3.3:
  version "3.3.3"
  resolved "http://registry.m.jd.com/flatted/download/flatted-3.3.3.tgz#67c8fad95454a7c7abebf74bb78ee74a44023358"
  integrity sha1-Z8j62VRUp8er6/dLt47nSkQCM1g=

fs.realpath@^1.0.0:
  version "1.0.0"
  resolved "http://registry.m.jd.com/fs.realpath/download/fs.realpath-1.0.0.tgz#1504ad2523158caa40db4a2787cb01411994ea4f"
  integrity sha1-FQStJSMVjKpA20onh8sBQRmU6k8=

glob-parent@^5.1.2:
  version "5.1.2"
  resolved "http://registry.m.jd.com/glob-parent/download/glob-parent-5.1.2.tgz#869832c58034fe68a4093c17dc15e8340d8401c4"
  integrity sha1-hpgyxYA0/mikCTwX3BXoNA2EAcQ=
  dependencies:
    is-glob "^4.0.1"

glob-parent@^6.0.2:
  version "6.0.2"
  resolved "http://registry.m.jd.com/glob-parent/download/glob-parent-6.0.2.tgz#6d237d99083950c79290f24c7642a3de9a28f9e3"
  integrity sha1-bSN9mQg5UMeSkPJMdkKj3poo+eM=
  dependencies:
    is-glob "^4.0.3"

glob@^7.1.3:
  version "7.2.3"
  resolved "http://registry.m.jd.com/glob/download/glob-7.2.3.tgz#b8df0fb802bbfa8e89bd1d938b4e16578ed44f2b"
  integrity sha1-uN8PuAK7+o6JvR2Ti04WV47UTys=
  dependencies:
    fs.realpath "^1.0.0"
    inflight "^1.0.4"
    inherits "2"
    minimatch "^3.1.1"
    once "^1.3.0"
    path-is-absolute "^1.0.0"

global-modules@^2.0.0:
  version "2.0.0"
  resolved "http://registry.m.jd.com/global-modules/download/global-modules-2.0.0.tgz#997605ad2345f27f51539bea26574421215c7780"
  integrity sha1-mXYFrSNF8n9RU5vqJldEISFcd4A=
  dependencies:
    global-prefix "^3.0.0"

global-prefix@^3.0.0:
  version "3.0.0"
  resolved "http://registry.m.jd.com/global-prefix/download/global-prefix-3.0.0.tgz#fc85f73064df69f50421f47f883fe5b913ba9b97"
  integrity sha1-/IX3MGTfafUEIfR/iD/luRO6m5c=
  dependencies:
    ini "^1.3.5"
    kind-of "^6.0.2"
    which "^1.3.1"

globals@^13.19.0:
  version "13.24.0"
  resolved "http://registry.m.jd.com/globals/download/globals-13.24.0.tgz#8432a19d78ce0c1e833949c36adb345400bb1171"
  integrity sha1-hDKhnXjODB6DOUnDats0VAC7EXE=
  dependencies:
    type-fest "^0.20.2"

globby@^11.1.0:
  version "11.1.0"
  resolved "http://registry.m.jd.com/globby/download/globby-11.1.0.tgz#bd4be98bb042f83d796f7e3811991fbe82a0d34b"
  integrity sha1-vUvpi7BC+D15b344EZkfvoKg00s=
  dependencies:
    array-union "^2.1.0"
    dir-glob "^3.0.1"
    fast-glob "^3.2.9"
    ignore "^5.2.0"
    merge2 "^1.4.1"
    slash "^3.0.0"

globjoin@^0.1.4:
  version "0.1.4"
  resolved "http://registry.m.jd.com/globjoin/download/globjoin-0.1.4.tgz#2f4494ac8919e3767c5cbb691e9f463324285d43"
  integrity sha1-L0SUrIkZ43Z8XLtpHp9GMyQoXUM=

graphemer@^1.4.0:
  version "1.4.0"
  resolved "http://registry.m.jd.com/graphemer/download/graphemer-1.4.0.tgz#fb2f1d55e0e3a1849aeffc90c4fa0dd53a0e66c6"
  integrity sha1-+y8dVeDjoYSa7/yQxPoN1ToOZsY=

has-flag@^4.0.0:
  version "4.0.0"
  resolved "http://registry.m.jd.com/has-flag/download/has-flag-4.0.0.tgz#944771fd9c81c81265c4d6941860da06bb59479b"
  integrity sha1-lEdx/ZyByBJlxNaUGGDaBrtZR5s=

hookified@^1.8.2:
  version "1.9.0"
  resolved "http://registry.m.jd.com/hookified/download/hookified-1.9.0.tgz#271211f61c63b3a68a8ead9d9fddd72b5806c004"
  integrity sha1-JxIR9hxjs6aKjq2dn93XK1gGwAQ=

html-tags@^3.3.1:
  version "3.3.1"
  resolved "http://registry.m.jd.com/html-tags/download/html-tags-3.3.1.tgz#a04026a18c882e4bba8a01a3d39cfe465d40b5ce"
  integrity sha1-oEAmoYyILku6igGj05z+Rl1Atc4=

ieee754@^1.2.1:
  version "1.2.1"
  resolved "http://registry.m.jd.com/ieee754/download/ieee754-1.2.1.tgz#8eb7a10a63fff25d15a57b001586d177d1b0d352"
  integrity sha1-jrehCmP/8l0VpXsAFYbRd9Gw01I=

ignore@^5.2.0:
  version "5.3.2"
  resolved "http://registry.m.jd.com/ignore/download/ignore-5.3.2.tgz#3cd40e729f3643fd87cb04e50bf0eb722bc596f5"
  integrity sha1-PNQOcp82Q/2HywTlC/DrcivFlvU=

ignore@^7.0.0, ignore@^7.0.3:
  version "7.0.4"
  resolved "http://registry.m.jd.com/ignore/download/ignore-7.0.4.tgz#a12c70d0f2607c5bf508fb65a40c75f037d7a078"
  integrity sha1-oSxw0PJgfFv1CPtlpAx18DfXoHg=

import-fresh@^3.2.1, import-fresh@^3.3.0:
  version "3.3.1"
  resolved "http://registry.m.jd.com/import-fresh/download/import-fresh-3.3.1.tgz#9cecb56503c0ada1f2741dbbd6546e4b13b57ccf"
  integrity sha1-nOy1ZQPAraHydB271lRuSxO1fM8=
  dependencies:
    parent-module "^1.0.0"
    resolve-from "^4.0.0"

imurmurhash@^0.1.4:
  version "0.1.4"
  resolved "http://registry.m.jd.com/imurmurhash/download/imurmurhash-0.1.4.tgz#9218b9b2b928a238b13dc4fb6b6d576f231453ea"
  integrity sha1-khi5srkoojixPcT7a21XbyMUU+o=

inflight@^1.0.4:
  version "1.0.6"
  resolved "http://registry.m.jd.com/inflight/download/inflight-1.0.6.tgz#49bd6331d7d02d0c09bc910a1075ba8165b56df9"
  integrity sha1-Sb1jMdfQLQwJvJEKEHW6gWW1bfk=
  dependencies:
    once "^1.3.0"
    wrappy "1"

inherits@2:
  version "2.0.4"
  resolved "http://registry.m.jd.com/inherits/download/inherits-2.0.4.tgz#0fa2c64f932917c3433a0ded55363aae37416b7c"
  integrity sha1-D6LGT5MpF8NDOg3tVTY6rjdBa3w=

ini@^1.3.5:
  version "1.3.8"
  resolved "http://registry.m.jd.com/ini/download/ini-1.3.8.tgz#a29da425b48806f34767a4efce397269af28432c"
  integrity sha1-op2kJbSIBvNHZ6Tvzjlyaa8oQyw=

invariant@^2.2.4:
  version "2.2.4"
  resolved "http://registry.m.jd.com/invariant/download/invariant-2.2.4.tgz#610f3c92c9359ce1db616e538008d23ff35158e6"
  integrity sha1-YQ88ksk1nOHbYW5TgAjSP/NRWOY=
  dependencies:
    loose-envify "^1.0.0"

is-arrayish@^0.2.1:
  version "0.2.1"
  resolved "http://registry.m.jd.com/is-arrayish/download/is-arrayish-0.2.1.tgz#77c99840527aa8ecb1a8ba697b80645a7a926a9d"
  integrity sha1-d8mYQFJ6qOyxqLppe4BkWnqSap0=

is-extglob@^2.1.1:
  version "2.1.1"
  resolved "http://registry.m.jd.com/is-extglob/download/is-extglob-2.1.1.tgz#a88c02535791f02ed37c76a1b9ea9773c833f8c2"
  integrity sha1-qIwCU1eR8C7TfHahueqXc8gz+MI=

is-fullwidth-code-point@^3.0.0:
  version "3.0.0"
  resolved "http://registry.m.jd.com/is-fullwidth-code-point/download/is-fullwidth-code-point-3.0.0.tgz#f116f8064fe90b3f7844a38997c0b75051269f1d"
  integrity sha1-8Rb4Bk/pCz94RKOJl8C3UFEmnx0=

is-glob@^4.0.0, is-glob@^4.0.1, is-glob@^4.0.3:
  version "4.0.3"
  resolved "http://registry.m.jd.com/is-glob/download/is-glob-4.0.3.tgz#64f61e42cbbb2eec2071a9dac0b28ba1e65d5084"
  integrity sha1-ZPYeQsu7LuwgcanawLKLoeZdUIQ=
  dependencies:
    is-extglob "^2.1.1"

is-number@^7.0.0:
  version "7.0.0"
  resolved "http://registry.m.jd.com/is-number/download/is-number-7.0.0.tgz#7535345b896734d5f80c4d06c50955527a14f12b"
  integrity sha1-dTU0W4lnNNX4DE0GxQlVUnoU8Ss=

is-path-inside@^3.0.3:
  version "3.0.3"
  resolved "http://registry.m.jd.com/is-path-inside/download/is-path-inside-3.0.3.tgz#d231362e53a07ff2b0e0ea7fed049161ffd16283"
  integrity sha1-0jE2LlOgf/Kw4Op/7QSRYf/RYoM=

is-plain-object@^5.0.0:
  version "5.0.0"
  resolved "http://registry.m.jd.com/is-plain-object/download/is-plain-object-5.0.0.tgz#4427f50ab3429e9025ea7d52e9043a9ef4159344"
  integrity sha1-RCf1CrNCnpAl6n1S6QQ6nvQVk0Q=

isexe@^2.0.0:
  version "2.0.0"
  resolved "http://registry.m.jd.com/isexe/download/isexe-2.0.0.tgz#e8fbf374dc556ff8947a10dcb0572d633f2cfa10"
  integrity sha1-6PvzdNxVb/iUehDcsFctYz8s+hA=

"js-tokens@^3.0.0 || ^4.0.0", js-tokens@^4.0.0:
  version "4.0.0"
  resolved "http://registry.m.jd.com/js-tokens/download/js-tokens-4.0.0.tgz#19203fb59991df98e3a287050d4647cdeaf32499"
  integrity sha1-GSA/tZmR35jjoocFDUZHzerzJJk=

js-yaml@^4.1.0:
  version "4.1.0"
  resolved "http://registry.m.jd.com/js-yaml/download/js-yaml-4.1.0.tgz#c1fb65f8f5017901cdd2c951864ba18458a10602"
  integrity sha1-wftl+PUBeQHN0slRhkuhhFihBgI=
  dependencies:
    argparse "^2.0.1"

json-buffer@3.0.1:
  version "3.0.1"
  resolved "http://registry.m.jd.com/json-buffer/download/json-buffer-3.0.1.tgz#9338802a30d3b6605fbe0613e094008ca8c05a13"
  integrity sha1-kziAKjDTtmBfvgYT4JQAjKjAWhM=

json-parse-even-better-errors@^2.3.0:
  version "2.3.1"
  resolved "http://registry.m.jd.com/json-parse-even-better-errors/download/json-parse-even-better-errors-2.3.1.tgz#7c47805a94319928e05777405dc12e1f7a4ee02d"
  integrity sha1-fEeAWpQxmSjgV3dAXcEuH3pO4C0=

json-schema-traverse@^0.4.1:
  version "0.4.1"
  resolved "http://registry.m.jd.com/json-schema-traverse/download/json-schema-traverse-0.4.1.tgz#69f6a87d9513ab8bb8fe63bdb0979c448e684660"
  integrity sha1-afaofZUTq4u4/mO9sJecRI5oRmA=

json-schema-traverse@^1.0.0:
  version "1.0.0"
  resolved "http://registry.m.jd.com/json-schema-traverse/download/json-schema-traverse-1.0.0.tgz#ae7bcb3656ab77a73ba5c49bf654f38e6b6860e2"
  integrity sha1-rnvLNlard6c7pcSb9lTzjmtoYOI=

json-stable-stringify-without-jsonify@^1.0.1:
  version "1.0.1"
  resolved "http://registry.m.jd.com/json-stable-stringify-without-jsonify/download/json-stable-stringify-without-jsonify-1.0.1.tgz#9db7b59496ad3f3cfef30a75142d2d930ad72651"
  integrity sha1-nbe1lJatPzz+8wp1FC0tkwrXJlE=

keyv@^4.5.3:
  version "4.5.4"
  resolved "http://registry.m.jd.com/keyv/download/keyv-4.5.4.tgz#a879a99e29452f942439f2a405e3af8b31d4de93"
  integrity sha1-qHmpnilFL5QkOfKkBeOvizHU3pM=
  dependencies:
    json-buffer "3.0.1"

keyv@^5.3.3:
  version "5.3.3"
  resolved "http://registry.m.jd.com/keyv/download/keyv-5.3.3.tgz#ec2d723fbd7b908de5ee7f56b769d46dbbeaf8ba"
  integrity sha1-7C1yP717kI3l7n9Wt2nUbbvq+Lo=
  dependencies:
    "@keyv/serialize" "^1.0.3"

kind-of@^6.0.2:
  version "6.0.3"
  resolved "http://registry.m.jd.com/kind-of/download/kind-of-6.0.3.tgz#07c05034a6c349fa06e24fa35aa76db4580ce4dd"
  integrity sha1-B8BQNKbDSfoG4k+jWqdttFgM5N0=

known-css-properties@^0.35.0:
  version "0.35.0"
  resolved "http://registry.m.jd.com/known-css-properties/download/known-css-properties-0.35.0.tgz#f6f8e40ab4e5700fa32f5b2ef5218a56bc853bd6"
  integrity sha1-9vjkCrTlcA+jL1su9SGKVryFO9Y=

known-css-properties@^0.36.0:
  version "0.36.0"
  resolved "http://registry.m.jd.com/known-css-properties/download/known-css-properties-0.36.0.tgz#5c4365f3c9549ca2e813d2e729e6c47ef6a6cb60"
  integrity sha1-XENl88lUnKLoE9LnKebEfvamy2A=

levn@^0.4.1:
  version "0.4.1"
  resolved "http://registry.m.jd.com/levn/download/levn-0.4.1.tgz#ae4562c007473b932a6200d403268dd2fffc6ade"
  integrity sha1-rkViwAdHO5MqYgDUAyaN0v/8at4=
  dependencies:
    prelude-ls "^1.2.1"
    type-check "~0.4.0"

lines-and-columns@^1.1.6:
  version "1.2.4"
  resolved "http://registry.m.jd.com/lines-and-columns/download/lines-and-columns-1.2.4.tgz#eca284f75d2965079309dc0ad9255abb2ebc1632"
  integrity sha1-7KKE910pZQeTCdwK2SVauy68FjI=

locate-path@^6.0.0:
  version "6.0.0"
  resolved "http://registry.m.jd.com/locate-path/download/locate-path-6.0.0.tgz#55321eb309febbc59c4801d931a72452a681d286"
  integrity sha1-VTIeswn+u8WcSAHZMackUqaB0oY=
  dependencies:
    p-locate "^5.0.0"

lodash.merge@^4.6.2:
  version "4.6.2"
  resolved "http://registry.m.jd.com/lodash.merge/download/lodash.merge-4.6.2.tgz#558aa53b43b661e1925a0afdfa36a9a1085fe57a"
  integrity sha1-VYqlO0O2YeGSWgr9+japoQhf5Xo=

lodash.truncate@^4.4.2:
  version "4.4.2"
  resolved "http://registry.m.jd.com/lodash.truncate/download/lodash.truncate-4.4.2.tgz#5a350da0b1113b837ecfffd5812cbe58d6eae193"
  integrity sha1-WjUNoLERO4N+z//VgSy+WNbq4ZM=

loose-envify@^1.0.0:
  version "1.4.0"
  resolved "http://registry.m.jd.com/loose-envify/download/loose-envify-1.4.0.tgz#71ee51fa7be4caec1a63839f7e682d8132d30caf"
  integrity sha1-ce5R+nvkyuwaY4OffmgtgTLTDK8=
  dependencies:
    js-tokens "^3.0.0 || ^4.0.0"

mathml-tag-names@^2.1.3:
  version "2.1.3"
  resolved "http://registry.m.jd.com/mathml-tag-names/download/mathml-tag-names-2.1.3.tgz#4ddadd67308e780cf16a47685878ee27b736a0a3"
  integrity sha1-TdrdZzCOeAzxakdoWHjuJ7c2oKM=

mdn-data@2.12.2:
  version "2.12.2"
  resolved "http://registry.m.jd.com/mdn-data/download/mdn-data-2.12.2.tgz#9ae6c41a9e65adf61318b32bff7b64fbfb13f8cf"
  integrity sha1-mubEGp5lrfYTGLMr/3tk+/sT+M8=

mdn-data@^2.15.0, mdn-data@^2.21.0:
  version "2.21.0"
  resolved "http://registry.m.jd.com/mdn-data/download/mdn-data-2.21.0.tgz#f3a495e8b1e60cb4fbeaf9136aefba2f987a56e1"
  integrity sha1-86SV6LHmDLT76vkTau+6L5h6VuE=

meow@^13.2.0:
  version "13.2.0"
  resolved "http://registry.m.jd.com/meow/download/meow-13.2.0.tgz#6b7d63f913f984063b3cc261b6e8800c4cd3474f"
  integrity sha1-a31j+RP5hAY7PMJhtuiADEzTR08=

merge2@^1.3.0, merge2@^1.4.1:
  version "1.4.1"
  resolved "http://registry.m.jd.com/merge2/download/merge2-1.4.1.tgz#4368892f885e907455a6fd7dc55c0c9d404990ae"
  integrity sha1-Q2iJL4hekHRVpv19xVwMnUBJkK4=

micromatch@^4.0.8:
  version "4.0.8"
  resolved "http://registry.m.jd.com/micromatch/download/micromatch-4.0.8.tgz#d66fa18f3a47076789320b9b1af32bd86d9fa202"
  integrity sha1-1m+hjzpHB2eJMgubGvMr2G2fogI=
  dependencies:
    braces "^3.0.3"
    picomatch "^2.3.1"

minimatch@^3.0.5, minimatch@^3.1.1, minimatch@^3.1.2:
  version "3.1.2"
  resolved "http://registry.m.jd.com/minimatch/download/minimatch-3.1.2.tgz#19cd194bfd3e428f049a70817c038d89ab4be35b"
  integrity sha1-Gc0ZS/0+Qo8EmnCBfAONiatL41s=
  dependencies:
    brace-expansion "^1.1.7"

minimatch@^9.0.4:
  version "9.0.5"
  resolved "http://registry.m.jd.com/minimatch/download/minimatch-9.0.5.tgz#d74f9dd6b57d83d8e98cfb82133b03978bc929e5"
  integrity sha1-10+d1rV9g9jpjPuCEzsDl4vJKeU=
  dependencies:
    brace-expansion "^2.0.1"

ms@^2.1.3:
  version "2.1.3"
  resolved "http://registry.m.jd.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

nanoid@^3.3.8:
  version "3.3.11"
  resolved "http://registry.m.jd.com/nanoid/download/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

natural-compare@^1.4.0:
  version "1.4.0"
  resolved "http://registry.m.jd.com/natural-compare/download/natural-compare-1.4.0.tgz#4abebfeed7541f2c27acfb29bdbbd15c8d5ba4f7"
  integrity sha1-Sr6/7tdUHywnrPspvbvRXI1bpPc=

normalize-path@^3.0.0:
  version "3.0.0"
  resolved "http://registry.m.jd.com/normalize-path/download/normalize-path-3.0.0.tgz#0dcd69ff23a1c9b11fd0978316644a0388216a65"
  integrity sha1-Dc1p/yOhybEf0JeDFmRKA4ghamU=

nullthrows@^1.1.1:
  version "1.1.1"
  resolved "http://registry.m.jd.com/nullthrows/download/nullthrows-1.1.1.tgz#7818258843856ae971eae4208ad7d7eb19a431b1"
  integrity sha1-eBgliEOFaulx6uQgitfX6xmkMbE=

once@^1.3.0:
  version "1.4.0"
  resolved "http://registry.m.jd.com/once/download/once-1.4.0.tgz#583b1aa775961d4b113ac17d9c50baef9dd76bd1"
  integrity sha1-WDsap3WWHUsROsF9nFC6753Xa9E=
  dependencies:
    wrappy "1"

optionator@^0.9.3:
  version "0.9.4"
  resolved "http://registry.m.jd.com/optionator/download/optionator-0.9.4.tgz#7ea1c1a5d91d764fb282139c88fe11e182a3a734"
  integrity sha1-fqHBpdkddk+yghOciP4R4YKjpzQ=
  dependencies:
    deep-is "^0.1.3"
    fast-levenshtein "^2.0.6"
    levn "^0.4.1"
    prelude-ls "^1.2.1"
    type-check "^0.4.0"
    word-wrap "^1.2.5"

p-limit@^3.0.2:
  version "3.1.0"
  resolved "http://registry.m.jd.com/p-limit/download/p-limit-3.1.0.tgz#e1daccbe78d0d1388ca18c64fea38e3e57e3706b"
  integrity sha1-4drMvnjQ0TiMoYxk/qOOPlfjcGs=
  dependencies:
    yocto-queue "^0.1.0"

p-locate@^5.0.0:
  version "5.0.0"
  resolved "http://registry.m.jd.com/p-locate/download/p-locate-5.0.0.tgz#83c8315c6785005e3bd021839411c9e110e6d834"
  integrity sha1-g8gxXGeFAF470CGDlBHJ4RDm2DQ=
  dependencies:
    p-limit "^3.0.2"

parent-module@^1.0.0:
  version "1.0.1"
  resolved "http://registry.m.jd.com/parent-module/download/parent-module-1.0.1.tgz#691d2709e78c79fae3a156622452d00762caaaa2"
  integrity sha1-aR0nCeeMefrjoVZiJFLQB2LKqqI=
  dependencies:
    callsites "^3.0.0"

parse-json@^5.2.0:
  version "5.2.0"
  resolved "http://registry.m.jd.com/parse-json/download/parse-json-5.2.0.tgz#c76fc66dee54231c962b22bcc8a72cf2f99753cd"
  integrity sha1-x2/Gbe5UIxyWKyK8yKcs8vmXU80=
  dependencies:
    "@babel/code-frame" "^7.0.0"
    error-ex "^1.3.1"
    json-parse-even-better-errors "^2.3.0"
    lines-and-columns "^1.1.6"

path-exists@^4.0.0:
  version "4.0.0"
  resolved "http://registry.m.jd.com/path-exists/download/path-exists-4.0.0.tgz#513bdbe2d3b95d7762e8c1137efa195c6c61b5b3"
  integrity sha1-UTvb4tO5XXdi6METfvoZXGxhtbM=

path-is-absolute@^1.0.0:
  version "1.0.1"
  resolved "http://registry.m.jd.com/path-is-absolute/download/path-is-absolute-1.0.1.tgz#174b9268735534ffbc7ace6bf53a5a9e1b5c5f5f"
  integrity sha1-F0uSaHNVNP+8es5r9TpanhtcX18=

path-key@^3.1.0:
  version "3.1.1"
  resolved "http://registry.m.jd.com/path-key/download/path-key-3.1.1.tgz#581f6ade658cbba65a0d3380de7753295054f375"
  integrity sha1-WB9q3mWMu6ZaDTOA3ndTKVBU83U=

path-type@^4.0.0:
  version "4.0.0"
  resolved "http://registry.m.jd.com/path-type/download/path-type-4.0.0.tgz#84ed01c0a7ba380afe09d90a8c180dcd9d03043b"
  integrity sha1-hO0BwKe6OAr+CdkKjBgNzZ0DBDs=

picocolors@^1.1.1:
  version "1.1.1"
  resolved "http://registry.m.jd.com/picocolors/download/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.3.1:
  version "2.3.1"
  resolved "http://registry.m.jd.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

postcss-media-query-parser@^0.2.3:
  version "0.2.3"
  resolved "http://registry.m.jd.com/postcss-media-query-parser/download/postcss-media-query-parser-0.2.3.tgz#27b39c6f4d94f81b1a73b8f76351c609e5cef244"
  integrity sha1-J7Ocb02U+Bsac7j3Y1HGCeXO8kQ=

postcss-resolve-nested-selector@^0.1.6:
  version "0.1.6"
  resolved "http://registry.m.jd.com/postcss-resolve-nested-selector/download/postcss-resolve-nested-selector-0.1.6.tgz#3d84dec809f34de020372c41b039956966896686"
  integrity sha1-PYTeyAnzTeAgNyxBsDmVaWaJZoY=

postcss-safe-parser@^7.0.1:
  version "7.0.1"
  resolved "http://registry.m.jd.com/postcss-safe-parser/download/postcss-safe-parser-7.0.1.tgz#36e4f7e608111a0ca940fd9712ce034718c40ec0"
  integrity sha1-NuT35ggRGgypQP2XEs4DRxjEDsA=

postcss-scss@^4.0.9:
  version "4.0.9"
  resolved "http://registry.m.jd.com/postcss-scss/download/postcss-scss-4.0.9.tgz#a03c773cd4c9623cb04ce142a52afcec74806685"
  integrity sha1-oDx3PNTJYjywTOFCpSr87HSAZoU=

postcss-selector-parser@^7.1.0:
  version "7.1.0"
  resolved "http://registry.m.jd.com/postcss-selector-parser/download/postcss-selector-parser-7.1.0.tgz#4d6af97eba65d73bc4d84bcb343e865d7dd16262"
  integrity sha1-TWr5frpl1zvE2EvLND6GXX3RYmI=
  dependencies:
    cssesc "^3.0.0"
    util-deprecate "^1.0.2"

postcss-sorting@^8.0.2:
  version "8.0.2"
  resolved "http://registry.m.jd.com/postcss-sorting/download/postcss-sorting-8.0.2.tgz#6393385ece272baf74bee9820fb1b58098e4eeca"
  integrity sha1-Y5M4Xs4nK690vumCD7G1gJjk7so=

postcss-value-parser@^4.2.0:
  version "4.2.0"
  resolved "http://registry.m.jd.com/postcss-value-parser/download/postcss-value-parser-4.2.0.tgz#723c09920836ba6d3e5af019f92bc0971c02e514"
  integrity sha1-cjwJkgg2um0+WvAZ+SvAlxwC5RQ=

postcss@^8.4.32, postcss@^8.5.3:
  version "8.5.3"
  resolved "http://registry.m.jd.com/postcss/download/postcss-8.5.3.tgz#1463b6f1c7fb16fe258736cba29a2de35237eafb"
  integrity sha1-FGO28cf7Fv4lhzbLopot41I36vs=
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

prelude-ls@^1.2.1:
  version "1.2.1"
  resolved "http://registry.m.jd.com/prelude-ls/download/prelude-ls-1.2.1.tgz#debc6489d7a6e6b0e7611888cec880337d316396"
  integrity sha1-3rxkidem5rDnYRiIzsiAM30xY5Y=

punycode@^2.1.0:
  version "2.3.1"
  resolved "http://registry.m.jd.com/punycode/download/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

queue-microtask@^1.2.2:
  version "1.2.3"
  resolved "http://registry.m.jd.com/queue-microtask/download/queue-microtask-1.2.3.tgz#4929228bbc724dfac43e0efb058caf7b6cfb6243"
  integrity sha1-SSkii7xyTfrEPg77BYyve2z7YkM=

react-native-linear-gradient@^2.8.3:
  version "2.8.3"
  resolved "http://registry.m.jd.com/react-native-linear-gradient/download/react-native-linear-gradient-2.8.3.tgz#9a116649f86d74747304ee13db325e20b21e564f"
  integrity sha1-mhFmSfhtdHRzBO4T2zJeILIeVk8=

require-from-string@^2.0.2:
  version "2.0.2"
  resolved "http://registry.m.jd.com/require-from-string/download/require-from-string-2.0.2.tgz#89a7fdd938261267318eafe14f9c32e598c36909"
  integrity sha1-iaf92TgmEmcxjq/hT5wy5ZjDaQk=

resolve-from@^4.0.0:
  version "4.0.0"
  resolved "http://registry.m.jd.com/resolve-from/download/resolve-from-4.0.0.tgz#4abcd852ad32dd7baabfe9b40e00a36db5f392e6"
  integrity sha1-SrzYUq0y3Xuqv+m0DgCjbbXzkuY=

resolve-from@^5.0.0:
  version "5.0.0"
  resolved "http://registry.m.jd.com/resolve-from/download/resolve-from-5.0.0.tgz#c35225843df8f776df21c57557bc087e9dfdfc69"
  integrity sha1-w1IlhD3493bfIcV1V7wIfp39/Gk=

reusify@^1.0.4:
  version "1.1.0"
  resolved "http://registry.m.jd.com/reusify/download/reusify-1.1.0.tgz#0fe13b9522e1473f51b558ee796e08f11f9b489f"
  integrity sha1-D+E7lSLhRz9RtVjueW4I8R+bSJ8=

rimraf@^3.0.2:
  version "3.0.2"
  resolved "http://registry.m.jd.com/rimraf/download/rimraf-3.0.2.tgz#f1a5402ba6220ad52cc1282bac1ae3aa49fd061a"
  integrity sha1-8aVAK6YiCtUswSgrrBrjqkn9Bho=
  dependencies:
    glob "^7.1.3"

run-parallel@^1.1.9:
  version "1.2.0"
  resolved "http://registry.m.jd.com/run-parallel/download/run-parallel-1.2.0.tgz#66d1368da7bdf921eb9d95bd1a9229e7f21a43ee"
  integrity sha1-ZtE2jae9+SHrnZW9GpIp5/IaQ+4=
  dependencies:
    queue-microtask "^1.2.2"

semver@^7.6.0:
  version "7.7.2"
  resolved "http://registry.m.jd.com/semver/download/semver-7.7.2.tgz#67d99fdcd35cec21e6f8b87a7fd515a33f982b58"
  integrity sha1-Z9mf3NNc7CHm+Lh6f9UVoz+YK1g=

shebang-command@^2.0.0:
  version "2.0.0"
  resolved "http://registry.m.jd.com/shebang-command/download/shebang-command-2.0.0.tgz#ccd0af4f8835fbdc265b82461aaf0c36663f34ea"
  integrity sha1-zNCvT4g1+9wmW4JGGq8MNmY/NOo=
  dependencies:
    shebang-regex "^3.0.0"

shebang-regex@^3.0.0:
  version "3.0.0"
  resolved "http://registry.m.jd.com/shebang-regex/download/shebang-regex-3.0.0.tgz#ae16f1644d873ecad843b0307b143362d4c42172"
  integrity sha1-rhbxZE2HPsrYQ7AwexQzYtTEIXI=

signal-exit@^4.0.1:
  version "4.1.0"
  resolved "http://registry.m.jd.com/signal-exit/download/signal-exit-4.1.0.tgz#952188c1cbd546070e2dd20d0f41c0ae0530cb04"
  integrity sha1-lSGIwcvVRgcOLdIND0HArgUwywQ=

slash@^3.0.0:
  version "3.0.0"
  resolved "http://registry.m.jd.com/slash/download/slash-3.0.0.tgz#6539be870c165adbd5240220dbe361f1bc4d4634"
  integrity sha1-ZTm+hwwWWtvVJAIg2+Nh8bxNRjQ=

slice-ansi@^4.0.0:
  version "4.0.0"
  resolved "http://registry.m.jd.com/slice-ansi/download/slice-ansi-4.0.0.tgz#500e8dd0fd55b05815086255b3195adf2a45fe6b"
  integrity sha1-UA6N0P1VsFgVCGJVsxla3ypF/ms=
  dependencies:
    ansi-styles "^4.0.0"
    astral-regex "^2.0.0"
    is-fullwidth-code-point "^3.0.0"

source-map-js@^1.0.1, source-map-js@^1.2.1:
  version "1.2.1"
  resolved "http://registry.m.jd.com/source-map-js/download/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

string-width@^4.2.3:
  version "4.2.3"
  resolved "http://registry.m.jd.com/string-width/download/string-width-4.2.3.tgz#269c7117d27b05ad2e536830a8ec895ef9c6d010"
  integrity sha1-JpxxF9J7Ba0uU2gwqOyJXvnG0BA=
  dependencies:
    emoji-regex "^8.0.0"
    is-fullwidth-code-point "^3.0.0"
    strip-ansi "^6.0.1"

strip-ansi@^6.0.1:
  version "6.0.1"
  resolved "http://registry.m.jd.com/strip-ansi/download/strip-ansi-6.0.1.tgz#9e26c63d30f53443e9489495b2105d37b67a85d9"
  integrity sha1-nibGPTD1NEPpSJSVshBdN7Z6hdk=
  dependencies:
    ansi-regex "^5.0.1"

strip-json-comments@^3.1.1:
  version "3.1.1"
  resolved "http://registry.m.jd.com/strip-json-comments/download/strip-json-comments-3.1.1.tgz#31f1281b3832630434831c310c01cccda8cbe006"
  integrity sha1-MfEoGzgyYwQ0gxwxDAHMzajL4AY=

stylelint-config-recommended-scss@^14.1.0:
  version "14.1.0"
  resolved "http://registry.m.jd.com/stylelint-config-recommended-scss/download/stylelint-config-recommended-scss-14.1.0.tgz#1a5855655cddcb5f77c10f38c76567adf2bb9aa3"
  integrity sha1-GlhVZVzdy193wQ84x2VnrfK7mqM=
  dependencies:
    postcss-scss "^4.0.9"
    stylelint-config-recommended "^14.0.1"
    stylelint-scss "^6.4.0"

stylelint-config-recommended@^14.0.1:
  version "14.0.1"
  resolved "http://registry.m.jd.com/stylelint-config-recommended/download/stylelint-config-recommended-14.0.1.tgz#d25e86409aaf79ee6c6085c2c14b33c7e23c90c6"
  integrity sha1-0l6GQJqvee5sYIXCwUszx+I8kMY=

stylelint-config-standard-scss@^14.0.0:
  version "14.0.0"
  resolved "http://registry.m.jd.com/stylelint-config-standard-scss/download/stylelint-config-standard-scss-14.0.0.tgz#d2a3dde0eeae3601ccdd734a63a0e3be12430a7e"
  integrity sha1-0qPd4O6uNgHM3XNKY6DjvhJDCn4=
  dependencies:
    stylelint-config-recommended-scss "^14.1.0"
    stylelint-config-standard "^36.0.1"

stylelint-config-standard@^36.0.1:
  version "36.0.1"
  resolved "http://registry.m.jd.com/stylelint-config-standard/download/stylelint-config-standard-36.0.1.tgz#727cbb2a1ef3e210f5ce8329cde531129f156609"
  integrity sha1-cny7Kh7z4hD1zoMpzeUxEp8VZgk=
  dependencies:
    stylelint-config-recommended "^14.0.1"

stylelint-order@^6.0.4:
  version "6.0.4"
  resolved "http://registry.m.jd.com/stylelint-order/download/stylelint-order-6.0.4.tgz#3e80d876c61a98d2640de181433686f24284748b"
  integrity sha1-PoDYdsYamNJkDeGBQzaG8kKEdIs=
  dependencies:
    postcss "^8.4.32"
    postcss-sorting "^8.0.2"

stylelint-scss@^6.12.0:
  version "6.12.0"
  resolved "http://registry.m.jd.com/stylelint-scss/download/stylelint-scss-6.12.0.tgz#38cf41c3b8a76f34cd7267e4c30e7e66d35619c2"
  integrity sha1-OM9Bw7inbzTNcmfkww5+ZtNWGcI=
  dependencies:
    css-tree "^3.0.1"
    is-plain-object "^5.0.0"
    known-css-properties "^0.36.0"
    mdn-data "^2.21.0"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.6"
    postcss-selector-parser "^7.1.0"
    postcss-value-parser "^4.2.0"

stylelint-scss@^6.4.0:
  version "6.11.1"
  resolved "http://registry.m.jd.com/stylelint-scss/download/stylelint-scss-6.11.1.tgz#03860aab250112825b2deb77ca7ff1e2ba3a5414"
  integrity sha1-A4YKqyUBEoJbLet3yn/x4ro6VBQ=
  dependencies:
    css-tree "^3.0.1"
    is-plain-object "^5.0.0"
    known-css-properties "^0.35.0"
    mdn-data "^2.15.0"
    postcss-media-query-parser "^0.2.3"
    postcss-resolve-nested-selector "^0.1.6"
    postcss-selector-parser "^7.1.0"
    postcss-value-parser "^4.2.0"

stylelint@^16.19.1:
  version "16.19.1"
  resolved "http://registry.m.jd.com/stylelint/download/stylelint-16.19.1.tgz#486b95fa7518a3077ee2802bc6dda2174bc097bb"
  integrity sha1-SGuV+nUYowd+4oArxt2iF0vAl7s=
  dependencies:
    "@csstools/css-parser-algorithms" "^3.0.4"
    "@csstools/css-tokenizer" "^3.0.3"
    "@csstools/media-query-list-parser" "^4.0.2"
    "@csstools/selector-specificity" "^5.0.0"
    "@dual-bundle/import-meta-resolve" "^4.1.0"
    balanced-match "^2.0.0"
    colord "^2.9.3"
    cosmiconfig "^9.0.0"
    css-functions-list "^3.2.3"
    css-tree "^3.1.0"
    debug "^4.3.7"
    fast-glob "^3.3.3"
    fastest-levenshtein "^1.0.16"
    file-entry-cache "^10.0.8"
    global-modules "^2.0.0"
    globby "^11.1.0"
    globjoin "^0.1.4"
    html-tags "^3.3.1"
    ignore "^7.0.3"
    imurmurhash "^0.1.4"
    is-plain-object "^5.0.0"
    known-css-properties "^0.36.0"
    mathml-tag-names "^2.1.3"
    meow "^13.2.0"
    micromatch "^4.0.8"
    normalize-path "^3.0.0"
    picocolors "^1.1.1"
    postcss "^8.5.3"
    postcss-resolve-nested-selector "^0.1.6"
    postcss-safe-parser "^7.0.1"
    postcss-selector-parser "^7.1.0"
    postcss-value-parser "^4.2.0"
    resolve-from "^5.0.0"
    string-width "^4.2.3"
    supports-hyperlinks "^3.2.0"
    svg-tags "^1.0.0"
    table "^6.9.0"
    write-file-atomic "^5.0.1"

supports-color@^7.0.0, supports-color@^7.1.0:
  version "7.2.0"
  resolved "http://registry.m.jd.com/supports-color/download/supports-color-7.2.0.tgz#1b7dcdcb32b8138801b3e478ba6a51caa89648da"
  integrity sha1-G33NyzK4E4gBs+R4umpRyqiWSNo=
  dependencies:
    has-flag "^4.0.0"

supports-hyperlinks@^3.2.0:
  version "3.2.0"
  resolved "http://registry.m.jd.com/supports-hyperlinks/download/supports-hyperlinks-3.2.0.tgz#b8e485b179681dea496a1e7abdf8985bd3145461"
  integrity sha1-uOSFsXloHepJah56vfiYW9MUVGE=
  dependencies:
    has-flag "^4.0.0"
    supports-color "^7.0.0"

svg-tags@^1.0.0:
  version "1.0.0"
  resolved "http://registry.m.jd.com/svg-tags/download/svg-tags-1.0.0.tgz#58f71cee3bd519b59d4b2a843b6c7de64ac04764"
  integrity sha1-WPcc7jvVGbWdSyqEO2x95krAR2Q=

table@^6.9.0:
  version "6.9.0"
  resolved "http://registry.m.jd.com/table/download/table-6.9.0.tgz#50040afa6264141c7566b3b81d4d82c47a8668f5"
  integrity sha1-UAQK+mJkFBx1ZrO4HU2CxHqGaPU=
  dependencies:
    ajv "^8.0.1"
    lodash.truncate "^4.4.2"
    slice-ansi "^4.0.0"
    string-width "^4.2.3"
    strip-ansi "^6.0.1"

text-table@^0.2.0:
  version "0.2.0"
  resolved "http://registry.m.jd.com/text-table/download/text-table-0.2.0.tgz#7f5ee823ae805207c00af2df4a84ec3fcfa570b4"
  integrity sha1-f17oI66AUgfACvLfSoTsP8+lcLQ=

to-regex-range@^5.0.1:
  version "5.0.1"
  resolved "http://registry.m.jd.com/to-regex-range/download/to-regex-range-5.0.1.tgz#1648c44aae7c8d988a326018ed72f5b4dd0392e4"
  integrity sha1-FkjESq58jZiKMmAY7XL1tN0DkuQ=
  dependencies:
    is-number "^7.0.0"

ts-api-utils@^2.1.0:
  version "2.1.0"
  resolved "http://registry.m.jd.com/ts-api-utils/download/ts-api-utils-2.1.0.tgz#595f7094e46eed364c13fd23e75f9513d29baf91"
  integrity sha1-WV9wlORu7TZME/0j51+VE9Kbr5E=

type-check@^0.4.0, type-check@~0.4.0:
  version "0.4.0"
  resolved "http://registry.m.jd.com/type-check/download/type-check-0.4.0.tgz#07b8203bfa7056c0657050e3ccd2c37730bab8f1"
  integrity sha1-B7ggO/pwVsBlcFDjzNLDdzC6uPE=
  dependencies:
    prelude-ls "^1.2.1"

type-fest@^0.20.2:
  version "0.20.2"
  resolved "http://registry.m.jd.com/type-fest/download/type-fest-0.20.2.tgz#1bf207f4b28f91583666cb5fbd327887301cd5f4"
  integrity sha1-G/IH9LKPkVg2ZstfvTJ4hzAc1fQ=

uri-js@^4.2.2:
  version "4.4.1"
  resolved "http://registry.m.jd.com/uri-js/download/uri-js-4.4.1.tgz#9b1a52595225859e55f669d928f88c6c57f2a77e"
  integrity sha1-mxpSWVIlhZ5V9mnZKPiMbFfyp34=
  dependencies:
    punycode "^2.1.0"

util-deprecate@^1.0.2:
  version "1.0.2"
  resolved "http://registry.m.jd.com/util-deprecate/download/util-deprecate-1.0.2.tgz#450d4dc9fa70de732762fbd2d4a28981419a0ccf"
  integrity sha1-RQ1Nyfpw3nMnYvvS1KKJgUGaDM8=

which@^1.3.1:
  version "1.3.1"
  resolved "http://registry.m.jd.com/which/download/which-1.3.1.tgz#a45043d54f5805316da8d62f9f50918d3da70b0a"
  integrity sha1-pFBD1U9YBTFtqNYvn1CRjT2nCwo=
  dependencies:
    isexe "^2.0.0"

which@^2.0.1:
  version "2.0.2"
  resolved "http://registry.m.jd.com/which/download/which-2.0.2.tgz#7c6a8dd0a636a0327e10b59c9286eee93f3f51b1"
  integrity sha1-fGqN0KY2oDJ+ELWckobu6T8/UbE=
  dependencies:
    isexe "^2.0.0"

word-wrap@^1.2.5:
  version "1.2.5"
  resolved "http://registry.m.jd.com/word-wrap/download/word-wrap-1.2.5.tgz#d2c45c6dd4fbce621a66f136cbe328afd0410b34"
  integrity sha1-0sRcbdT7zmIaZvE2y+Mor9BBCzQ=

wrappy@1:
  version "1.0.2"
  resolved "http://registry.m.jd.com/wrappy/download/wrappy-1.0.2.tgz#b5243d8f3ec1aa35f1364605bc0d1036e30ab69f"
  integrity sha1-tSQ9jz7BqjXxNkYFvA0QNuMKtp8=

write-file-atomic@^5.0.1:
  version "5.0.1"
  resolved "http://registry.m.jd.com/write-file-atomic/download/write-file-atomic-5.0.1.tgz#68df4717c55c6fa4281a7860b4c2ba0a6d2b11e7"
  integrity sha1-aN9HF8Vcb6QoGnhgtMK6Cm0rEec=
  dependencies:
    imurmurhash "^0.1.4"
    signal-exit "^4.0.1"

yocto-queue@^0.1.0:
  version "0.1.0"
  resolved "http://registry.m.jd.com/yocto-queue/download/yocto-queue-0.1.0.tgz#0294eb3dee05028d31ee1a5fa2c556a6aaf10a1b"
  integrity sha1-ApTrPe4FAo0x7hpfosVWpqrxChs=
