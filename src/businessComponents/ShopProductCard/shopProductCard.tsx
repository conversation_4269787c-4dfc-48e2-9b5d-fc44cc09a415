import React from 'react';
import View from '../../components/basic/View';
import ShopHeader from '../ShopHeader/shopHeader';
import { HorizontalList } from '../HorizontalList';
import { ShopProductCardProps } from './types';
import './shopProductCard.scss';

/**
 * 店铺商品卡片组件 - 将店铺信息和商品列表组合在一个圆角卡片中
 * 同时支持Taro和RN平台
 */
const ShopProductCard: React.FC<ShopProductCardProps> = (props) => {
  const {
    shopInfo,
    productList,
    borderRadius = 12,
    backgroundColor = '#FFFFFF',
    border,
    contentSpacing = 8,
    padding = 12,
    marginBottom = 8,
    className = '',
    style = {},
    containerStyle,
    shopInfoStyle = {},
    productListStyle = {},
    onProductListItemClick,
  } = props;

  // 处理内边距
  const getPadding = () => {
    if (typeof padding === 'number') {
      return {
        paddingTop: padding,
        paddingRight: padding,
        paddingBottom: padding,
        paddingLeft: padding,
      };
    }
    
    return {
      paddingTop: padding?.top,
      paddingRight: padding?.right,
      paddingBottom: padding?.bottom,
      paddingLeft: padding?.left,
    };
  };

  // 生成卡片样式
  const cardStyle = {
    backgroundColor,
    borderRadius,
    border,
    ...getPadding(),
  };

  return (
    <View style={{ paddingBottom: marginBottom }}>
      <View 
        className={`shop-product-card ${className}`} 
        style={cardStyle as any}
      >
        {/* 店铺信息区域 */}
        <View className="shop-product-card__header" style={shopInfoStyle as any}>
          <ShopHeader {...shopInfo} />
        </View>

        {/* 商品列表区域 - 只有在有商品数据时才渲染 */}
        {productList && productList.items && productList.items.length > 0 && (
          <View 
            className="shop-product-card__products" 
            style={{ 
              marginTop: contentSpacing,
              ...productListStyle as any,
            }}
          >
            <HorizontalList {...productList} onItemClick={onProductListItemClick} />
          </View>
        )}
      </View>
    </View>
  );
};

export default ShopProductCard; 