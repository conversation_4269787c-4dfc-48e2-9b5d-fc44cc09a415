import { mixinStyle } from '../../../../components/basic/View/types';
import { CSSProperties } from 'react';
import type { ReactNode } from 'react';

// // 定义本地的 mixinStyle 类型，避免循环依赖
// type mixinStyle = string | Record<string, any> | Array<string | Record<string, any> | undefined> | undefined;

/**
 * 翻转轮播组件属性
 */
export interface FlipCarouselProps {
  /** 轮播数据 */
  data: any[];
  /** 每页显示的数量 */
  itemsPerPage?: number;
  /** 自动轮播间隔（毫秒） */
  autoPlayInterval?: number;
  /** 动画持续时间（毫秒） */
  animationDuration?: number;
  /** 自定义渲染项 */
  renderItem: (item: any, index: number) => ReactNode;
  /** 页面高度 */
  pageHeight?: number;
  /** 容器样式 */
  style?: mixinStyle;
  /** 内容容器样式 */
  contentContainerStyle?: CSSProperties;
  /** 页面内容区域样式 */
  pageContentStyle?: CSSProperties;
  /** 翻页完成回调函数 */
  onPageChange?: (currentPageData: any[]) => void;
}

/**
 * 页面方向
 */
export enum PageDirection {
  /** 向前翻页 */
  Forward = 'forward',
  /** 向后翻页 */
  Backward = 'backward',
}
