.title {
  align-items: center;
  flex-direction: row;
  width: 100%;
  display: flex;
  // border: 1px seagreen solid;

  &__tag-container {
    align-items: center;
    flex-direction: row;
    flex-wrap: nowrap;
    display: flex;
    align-items: center;
    // border: 1px saddlebrown solid;
    position: relative;
    flex: 0 0 auto;

    &--wrap {
      flex-direction: row;
      flex-wrap: wrap;
      width: 120px;
    }
  }

  &__tag {
    margin-top: -2px;
    margin-right: 5px;
    // position: absolute;
    // top: 50%;
    // transition: translate(-50%, -50%);
    
    &--image {
      // display: block;
    }
  }

  &__text {
    color: #333;
    flex: 1;
    font-family: 'PingFang SC';
    font-weight: bold;
    align-items: center;
    // line-height: 1.4;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    // width: 100%;
    // justify-content: space-between;
    // border: 1px red solid;
  }
} 