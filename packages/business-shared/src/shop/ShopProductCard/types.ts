import React from 'react';
import { ViewStyle } from 'react-native';
import { ShopHeaderProps } from '../ShopHeader/types';
import { HorizontalListProps } from '../HorizontalList/types';

export interface ShopProductCardProps {
  /** 店铺信息配置 */
  shopInfo: Omit<ShopHeaderProps, 'className' | 'style' | 'containerStyle'>;
  
  /** 商品列表配置 */
  productList: Omit<HorizontalListProps, 'className' | 'style' | 'containerStyle'>;
  
  /** 卡片圆角大小 */
  borderRadius?: number;
  
  /** 卡片背景色 */
  backgroundColor?: string;
  
  /** 卡片边框 */
  border?: string;
  
  /** 商品列表与店铺信息间的间距 */
  contentSpacing?: number;
  
  /** 卡片内边距 */
  padding?: number | { top?: number; right?: number; bottom?: number; left?: number };
  
  /** 卡片底部外边距 */
  marginBottom?: number;
  
  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
  
  /** 自定义根元素内联样式 (Taro/Web) */
  style?: React.CSSProperties;
  
  /** 自定义根元素样式 (RN) */
  containerStyle?: ViewStyle;
  
  /** 店铺信息区域样式 */
  shopInfoStyle?: React.CSSProperties | ViewStyle;
  
  /** 商品列表区域样式 */
  productListStyle?: React.CSSProperties | ViewStyle;
  
  /** 整个卡片点击事件 */
  onClick?: () => void;
  
  /** 商品列表项点击事件 */
  onProductListItemClick?: (item: any, index: number) => void;
} 