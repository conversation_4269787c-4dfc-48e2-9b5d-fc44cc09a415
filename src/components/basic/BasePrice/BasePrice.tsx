import React from 'react';
import View from '../../basic/View';
import Text from '../../basic/Text';
import { BasePriceProps } from './types';
import { styles } from './style';
import './BasePrice.scss';

const BasePrice: React.FC<BasePriceProps> = ({
  price,
  currencySymbol = '¥',
  showDecimal = true,
  decimalPlaces = 2,
  symbolColor,
  symbolFontSize,
  symbolTextStyle,
  integerColor,
  integerFontSize,
  integerTextStyle,
  decimalColor,
  decimalFontSize,
  decimalTextStyle,
  strikethrough = false,
  className,
  style,
  containerStyle,
}) => {
  // 确保价格是数字类型
  const numericPrice = typeof price === 'number' ? price : Number(price) || 0;
  
  // 检查价格是否为整数
  const isInteger = Number.isInteger(numericPrice);
  
  // 处理价格的整数和小数部分
  const [integerPart, decimalPart_] = numericPrice.toFixed(decimalPlaces).split('.');
  const decimalPart = decimalPart_ || '0'.repeat(decimalPlaces);
  
  // 只有当showDecimal为true，decimalPlaces大于0，且价格不是整数时才显示小数
  const showActualDecimal = showDecimal && decimalPlaces > 0 && !isInteger;

  // 构建类名
  const rootClassName = [
    'base-price',
    strikethrough ? 'base-price--strikethrough' : '',
    className
  ].filter(Boolean).join(' ');

  const symbolClassName = 'base-price__symbol';
  const integerClassName = 'base-price__integer';
  const decimalDotClassName = 'base-price__decimal-dot';
  const decimalClassName = 'base-price__decimal';

  // 构建样式
  const rootStyle = {
    ...containerStyle,
    ...style,
  } as React.CSSProperties;

  // 统一使用数值，不带单位，由基础组件内部处理单位转换
  const symbolStyle = {
    color: symbolColor,
    fontSize: symbolFontSize,
    ...symbolTextStyle,
  } as React.CSSProperties;

  const integerStyle = {
    color: integerColor,
    fontSize: integerFontSize,
    ...({textDecorationLine: strikethrough ? 'line-through' : 'none' as any,}),
    ...integerTextStyle,
  } as React.CSSProperties;

  const decimalStyle = {
    color: decimalColor,
    fontSize: decimalFontSize,
    ...({textDecorationLine: strikethrough ? 'line-through' : 'none' as any,}),
    ...decimalTextStyle,
  } as React.CSSProperties;

  return (
    <View className={rootClassName} style={rootStyle}>
      <Text className={symbolClassName} style={symbolStyle}>
        {currencySymbol}
      </Text>
      <Text className={integerClassName} style={integerStyle}>
        {integerPart}
      </Text>
      {showActualDecimal && (
        <>
          <Text className={decimalDotClassName} style={decimalStyle}>.</Text>
          <Text className={decimalClassName} style={decimalStyle}>
            {decimalPart}
          </Text>
        </>
      )}
    </View>
  );
};

export default BasePrice; 