import React from 'react';
import { View } from '../../../../../../core/src/components/basic/View';
import { ProductGroupProps } from './types';
import './styles.scss';

/**
 * 商品组组件，显示4个商品
 * 通用实现，同时支持Taro和RN平台
 */
const ProductGroup: React.FC<ProductGroupProps> = ({
  products,
  startIndex,
  onProductClick,
  className
}) => {
  // 这里使用style属性设置宽度而不是className，确保在RN平台也能正常工作
  return (
    <View className={`product-group ${className || ''}`}>
      {products.map((product, index) => (
        <View
          key={`product-${startIndex + index}`}
          className="product-group__item"
        >

        </View>
      ))}
    </View>
  );
};

export default ProductGroup; 