import React from 'react';
import View from '../../basic/View';
import { ProductCard } from './index';

const ProductCardDemo: React.FC = () => {
  return (
    <View className="product-card-demo" style={{ padding: 20 }}>
      <View style={{ marginBottom: 20 }}>
        <h3>基本用法 - 与图片一致的促销商品卡</h3>
        <ProductCard 
          imageUrl="https://img.example.com/crab.jpg"
          title="袁记云饺"
          description="帝王蟹畅吃"
          currentPrice={199}
          originalPrice={399}
          priceType="discount"
        />
      </View>

      <View style={{ marginBottom: 20 }}>
        <h3>不带原价</h3>
        <ProductCard 
          imageUrl="https://img.example.com/food.jpg"
          title="美味佳肴"
          description="限时特惠"
          currentPrice={99}
        />
      </View>

      <View style={{ marginBottom: 20 }}>
        <h3>自定义样式</h3>
        <ProductCard 
          imageUrl="https://img.example.com/special.jpg"
          title="特色菜品"
          description="精选食材"
          currentPrice={159}
          originalPrice={259}
          titleColor="#1890FF"
          descriptionColor="#52C41A"
          priceProps={{
            currentPriceColor: '#F5222D',
            originalPriceColor: '#8C8C8C'
          }}
        />
      </View>

      <View style={{ marginBottom: 20 }}>
        <h3>自定义图片尺寸</h3>
        <ProductCard 
          imageUrl="https://img.example.com/large.jpg"
          imageWidth={250}
          imageHeight={180}
          title="大份量美食"
          description="家庭聚餐首选"
          currentPrice={299}
          originalPrice={399}
        />
      </View>

      <View style={{ marginBottom: 20 }}>
        <h3>带点击事件</h3>
        <ProductCard 
          imageUrl="https://img.example.com/clickable.jpg"
          title="点击查看详情"
          description="优惠多多"
          currentPrice={88}
          originalPrice={118}
          onClick={() => alert('点击了商品卡片')}
        />
      </View>
    </View>
  );
};

export default ProductCardDemo; 