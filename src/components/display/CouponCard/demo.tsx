import React from 'react';
import View from '../../basic/View';
import Text from '../../basic/Text';
import { CouponCard } from './index';

const HEADER_HEIGHT = 56;
const COUPON_BG_IMAGE = 'https://storage.360buyimg.com/wximg/LocStore/couponcard_bg.png';

const CouponCardDemo: React.FC = () => {
  return (
    <View style={{ background: '#f7f8fa', minHeight: '100vh' }}>
      {/* 顶部标题栏，fixed定位 */}
      <View style={{ 
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 10,
        background: '#fff',
        padding: '0 20px',
        height: HEADER_HEIGHT,
        display: 'flex',
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)'
      }}>
        <Text style={{ 
          fontSize: 18, 
          fontWeight: 'bold',
          textAlign: 'center',
          lineHeight: HEADER_HEIGHT + 'px'
        }}>CouponCard 优惠券卡片</Text>
      </View>

      {/* 内容区域，paddingTop避免被header遮挡 */}
      <View style={{ padding: '16px 20px', paddingTop: HEADER_HEIGHT + 16 }}>
        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>默认样式</Text>
          <CouponCard 
            title="优惠券优惠券优惠券"
            amount={15}
            width={72}
            height={110}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>

        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>自定义标题和使用条件</Text>
          <CouponCard 
            title="优惠券" 
            amount={50} 
            condition="满100可用优惠券"
            width={72}
            height={110}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>

        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>自定义颜色</Text>
          <CouponCard 
            title="满减券"
            amount={20} 
            titleColor="#1890FF"
            amountColor="#1890FF"
            conditionColor="#1890FF"
            width={72}
            height={110}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>

        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>自定义字体大小</Text>
          <CouponCard 
            title="代金券"
            amount={100} 
            titleFontSize={20}
            amountFontSize={50}
            amountSymbolFontSize={30}
            conditionFontSize={18}
            width={108}
            height={165}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>

        <View style={{ 
          background: '#fff', 
          borderRadius: '12px', 
          padding: '20px',
          marginBottom: '16px'
        }}>
          <Text style={{ fontSize: 14, color: '#999', marginBottom: '12px' }}>美元符号</Text>
          <CouponCard 
            title="国际券"
            amount={10} 
            currencySymbol="$"
            width={72}
            height={110}
            backgroundImage={COUPON_BG_IMAGE}
          />
        </View>
      </View>
    </View>
  );
};

export default CouponCardDemo; 