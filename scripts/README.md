# 📁 Scripts 目录说明

## 🚀 核心脚本

### `quick-release.sh` ⭐⭐⭐ 最常用
**用途**: 日常开发的快速发布脚本
**调用**: `yarn quick:patch`, `yarn quick:minor`, `yarn quick:major`
**功能**:
- 自动提交未保存的更改
- 快速构建和测试
- 自动发布到 npm
- 友好的错误处理

### `release.sh` ⭐⭐ 重要
**用途**: 生产环境的完整发布流程
**调用**: `yarn release:patch`, `yarn release:minor`, `yarn release:major`, `yarn release:beta`
**功能**:
- 完整的 CI/CD 检查
- 严格的发布流程
- 详细的发布报告
- 自动生成变更日志

### `ci-check.sh` ⭐⭐ 重要
**用途**: 完整的持续集成检查
**调用**: `yarn check`, 被 `release.sh` 调用
**功能**:
- 依赖安装和验证
- 代码检查和测试
- 构建验证
- 安全审计
- 包大小检查

### `manage-internal-deps.sh` ⭐ 偶尔使用
**用途**: 内部依赖管理工具
**调用**: `yarn deps:check`, 被 `ci-check.sh` 调用
**功能**:
- 检查内部依赖状态
- 验证依赖配置
- 更新依赖版本
- 同步依赖版本

### `fix-internal-deps.sh` ⭐ 偶尔使用
**用途**: 修复内部依赖配置问题
**调用**: `yarn deps:fix`
**功能**:
- 移除重复的内部依赖
- 修复依赖配置错误
- 验证修复结果

## 📊 脚本依赖关系

```
quick-release.sh (日常发布)
    ↓
    (独立运行)

release.sh (完整发布)
    ↓
    ci-check.sh
        ↓
        manage-internal-deps.sh

fix-internal-deps.sh (独立工具)
```

## 🔧 脚本维护

### 修改脚本时注意
1. **权限**: 确保脚本有执行权限 (`chmod +x`)
2. **路径**: 脚本中的相对路径基于项目根目录
3. **Node.js**: 确保使用正确的 Node.js 版本路径
4. **错误处理**: 保持友好的错误提示

### 添加新脚本
1. 放在 `scripts/` 目录下
2. 添加执行权限
3. 在 `package.json` 中添加对应命令
4. 更新此文档

### 脚本命名规范
- 使用 kebab-case (短横线分隔)
- 功能明确的名称
- 避免过长的名称

## 💡 使用建议

### 日常开发 (90%)
```bash
yarn quick:patch    # 使用 quick-release.sh
```

### 生产发布 (10%)
```bash
yarn release:major  # 使用 release.sh
```

### 问题排查
```bash
yarn check          # 使用 ci-check.sh
yarn deps:check     # 使用 manage-internal-deps.sh
yarn deps:fix       # 使用 fix-internal-deps.sh
```

## 📚 相关文档

- [命令参考](../docs/COMMANDS.md)
- [开发工作流](../docs/DEVELOPMENT_WORKFLOW.md)
- [包管理指南](../docs/PACKAGE_MANAGEMENT.md)
