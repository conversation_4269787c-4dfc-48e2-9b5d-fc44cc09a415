# CouponCard 优惠券卡片

基于BasePrice组件封装的优惠券卡片组件，支持自定义背景图片、文字颜色、字体大小和尺寸等。

## 使用场景

适用于电商场景中优惠券的展示，包含标题、金额和可选的使用条件。支持点击交互，可用于跳转优惠券详情页、领取优惠券等场景。

## 样式示例

### 默认样式
使用背景图片，包含标题、金额和使用条件
![优惠券样式展示](示例图片路径)

## 代码演示

```tsx
import { CouponCard } from '@/components/display';

const COUPON_BG_IMAGE = 'https://storage.360buyimg.com/wximg/LocStore/couponcard_bg.png';

// 默认样式
<CouponCard 
  title="优惠券" 
  amount={15} 
  width={72} 
  height={110}
  backgroundImage={COUPON_BG_IMAGE}
/>

// 自定义标题和使用条件
<CouponCard 
  title="优惠券" 
  amount={50} 
  condition="满100可用"
  width={72}
  height={110}
  backgroundImage={COUPON_BG_IMAGE}
/>

// 无使用条件
<CouponCard 
  title="无门槛券" 
  amount={30}
  condition={undefined}
  width={72}
  height={110}
  backgroundImage={COUPON_BG_IMAGE}
/>

// 自定义颜色
<CouponCard 
  title="满减券"
  amount={20} 
  titleColor="#1890FF"
  amountColor="#1890FF"
  conditionColor="#1890FF"
  width={72}
  height={110}
  backgroundImage={COUPON_BG_IMAGE}
/>

// 自定义字体大小和放大尺寸
<CouponCard 
  title="代金券"
  amount={100} 
  titleFontSize={16}
  amountFontSize={38}
  amountSymbolFontSize={20}
  conditionFontSize={14}
  width={108}
  height={165}
  backgroundImage={COUPON_BG_IMAGE}
/>

// 美元符号
<CouponCard 
  title="国际券"
  amount={10} 
  currencySymbol="$"
  width={72}
  height={110}
  backgroundImage={COUPON_BG_IMAGE}
/>

// 带点击事件的优惠券
<CouponCard 
  title="限时优惠券"
  amount={5} 
  condition="满50元可用"
  width={72}
  height={110}
  backgroundImage={COUPON_BG_IMAGE}
  onClick={() => {
    console.log('优惠券被点击了');
    // 处理点击逻辑，如跳转到详情页或领取优惠券
  }}
/>
```

## API

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| title | 优惠券标题 | `string` | `'优惠券'` |
| amount | 优惠券金额 | `number` | - |
| condition | 使用条件文案 | `string` | `''` |
| currencySymbol | 货币符号 | `string` | `'¥'` |
| backgroundImage | 背景图片URL | `string` | - |
| titleColor | 标题颜色 | `string` | `'#8F4100'` |
| amountColor | 金额颜色 | `string` | `'#8F4100'` |
| conditionColor | 条件文字颜色 | `string` | `'#8F4100'` |
| titleFontSize | 标题字体大小 | `number` | `12` |
| amountFontSize | 金额字体大小 | `number` | `26` |
| amountSymbolFontSize | 金额符号字体大小 | `number` | `14` |
| conditionFontSize | 条件字体大小 | `number` | `10` |
| width | 优惠券宽度 | `number` | `72` |
| height | 优惠券高度 | `number` | `110` |
| onClick | 点击事件回调 | `() => void` | - |
| className | 自定义根元素类名 (Taro/Web) | `string` | - |
| style | 自定义根元素内联样式 (Taro/Web) | `React.CSSProperties` | - |
| containerStyle | 自定义根元素样式 (RN) | `ViewStyle` | - |

## 注意事项

- 组件内部使用了BasePrice组件来显示价格
- 标题字体使用 PingFang SC，字重600，颜色 #8F4100
- 金额使用 JDZhengHT-EN 字体，字重400，颜色 #8F4100
- 使用条件文本使用 PingFang SC，字重400，颜色 #8F4100
- 当没有使用条件时，标题会距离顶部多出5px
- 背景图片尺寸默认是 72×110，如果需要更大的组件，建议等比例放大背景图片尺寸和文字大小
- 组件采用自上而下的布局，标题距顶部8px，金额距离标题6px，条件文本距离金额4px
- 点击事件使用View的onClick方法，兼容Web和React Native平台
- onClick为可选属性，不提供时组件仍可正常显示但不响应点击 