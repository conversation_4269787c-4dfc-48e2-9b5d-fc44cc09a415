{"name": "@jd/lifeui-business-shared", "version": "1.0.0", "description": "本地生活业务共享组件库", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/types/src/index.d.ts", "react-native": "./dist/index.rn.js", "scripts": {"dev": "vite --config vite.config.dev.js", "build": "vite build", "build:multi": "node ../../scripts/build-multiplatform.js .", "build:rn": "node ../../scripts/build-multiplatform.js . rn", "build:h5": "node ../../scripts/build-multiplatform.js . h5", "build:weapp": "node ../../scripts/build-multiplatform.js . weapp", "test": "jest", "lint": "eslint src"}, "files": ["dist", "src"], "dependencies": {"@jd/lifeui-core": "^1.0.0"}, "devDependencies": {"@types/react": "^18.0.0", "@types/react-native": "^0.72.0", "typescript": "^5.0.0"}, "peerDependencies": {"react": "^18.0.0", "react-native": "^0.72.0"}, "exports": {".": {"react-native": "./dist/index.rn.js", "import": "./dist/index.js", "require": "./dist/index.cjs", "default": "./dist/index.js"}, "./package.json": "./package.json", "./rn": {"import": "./dist/index.rn.js", "require": "./dist/index.rn.cjs", "default": "./dist/index.rn.js"}}}