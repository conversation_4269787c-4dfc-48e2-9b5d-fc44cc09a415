import { View as TaroView } from "@tarojs/components";
import { forwardRef, memo } from 'react';
import { useScreen } from '../../../hooks/useScreen';

/**
 * Taro 版本的动画视图组件
 * 由于 Taro 不支持原生动画，这里使用 CSS 动画作为替代
 */
const AnimatedView = forwardRef<any, any>((props, ref) => {
  const { style, ...restProps } = props;
  const { scaleStyle } = useScreen();
  
  // 对style应用自动缩放（只处理对象类型的style）
  const scaledStyle = style && typeof style === 'object' && !Array.isArray(style) 
    ? scaleStyle(style as Record<string, any>) 
    : style;
  
  return <TaroView {...restProps} style={scaledStyle as any} ref={ref} />;
});

export default memo(AnimatedView); 