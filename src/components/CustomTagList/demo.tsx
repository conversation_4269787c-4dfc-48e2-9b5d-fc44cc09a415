import React, { useState } from 'react';
import { View } from '@tarojs/components';
import CustomTagList from './customTagList';
import { TagType } from '../CustomTag/types';

const CustomTagListDemo: React.FC = () => {
  const [showAll, setShowAll] = useState(false);
  
  // 基础标签列表数据
  const basicTags = [
    '热门', '推荐', '特惠', '满减活动', '新品上市', '限时折扣',
    '免运费', '会员专享', '精选商品', '降价', '库存紧张'
  ];

  // 自定义标签列表数据
  const customTags = [
    {
      type: TagType.TEXT,
      text: '新品',
      backgroundColor: '#e6f7ff',
      textStyle: { color: '#1890ff' }
    },
    {
      type: TagType.TEXT,
      text: '热卖',
      backgroundColor: '#fff2e8',
      textStyle: { color: '#fa541c' }
    },
    {
      type: TagType.TEXT,
      text: '促销',
      backgroundColor: '#f6ffed',
      textStyle: { color: '#52c41a' }
    },
    {
      type: TagType.TEXT,
      text: '满减',
      backgroundColor: '#fff0f6',
      textStyle: { color: '#eb2f96' }
    },
    {
      type: TagType.TEXT,
      text: '限时',
      backgroundColor: '#fcf4d6',
      textStyle: { color: '#faad14' },
      showBorder: true
    },
    {
      type: TagType.TEXT,
      text: '会员专享',
      backgroundColor: '#f9f0ff',
      textStyle: { color: '#722ed1' }
    }
  ];

  // 展示所有标签 - 点击按钮模拟
  const toggleShowAll = () => {
    setShowAll(prev => !prev);
  };

  return (
    <View style={{ padding: '20px' }}>
      <View style={{ marginBottom: '20px' }}>
        <h3>基础标签列表（单行）</h3>
        <CustomTagList 
          tags={basicTags}
          maxLines={1}
          truncateMode="ellipsis"
        />
      </View>
      
      <View style={{ marginBottom: '20px' }}>
        <h3>基础标签列表（多行）</h3>
        <CustomTagList 
          tags={basicTags}
          maxLines={2}
          lineHeight={28}
          verticalGap={8}
          truncateMode="ellipsis"
        />
      </View>
      
      <View style={{ marginBottom: '20px' }}>
        <h3>自定义标签列表</h3>
        <CustomTagList 
          tags={customTags}
          maxLines={2}
          width={300}
          truncateMode="ellipsis"
        />
        <View style={{ marginTop: '10px' }}>
          <button 
            onClick={toggleShowAll} 
            style={{
              padding: '5px 10px',
              background: '#1890ff',
              color: '#fff',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            {showAll ? '隐藏部分标签' : '查看全部标签'}
          </button>
        </View>
      </View>
      
      {showAll && (
        <View style={{ marginBottom: '20px' }}>
          <h3>显示所有标签</h3>
          <CustomTagList 
            tags={customTags}
            maxLines={Infinity}
          />
        </View>
      )}
      
      <View style={{ marginBottom: '20px' }}>
        <h3>拉伸填满标签列表</h3>
        <CustomTagList 
          tags={basicTags.slice(0, 3)}
          stretch={true}
          gap={12}
          truncateMode="none"
        />
      </View>
    </View>
  );
};

export default CustomTagListDemo; 