.custom-tag {
  display: inline-flex;
  align-items: center;
  // padding: 2px 4px;
  // border-radius: 4px;
  background-color: #f0f0f0;
  box-sizing: border-box;
  width: auto;
  max-width: 100%;
  align-self: flex-start;
  flex-direction: row;

  &__icon {
    width: 12px;
    height: 12px;
    margin-right: 4px;
  }

  &__text {
    font-size: 12px;
    color: #333;
    line-height: 1;
    white-space: nowrap;
  }
  
  // 带边框样式
  &--with-border {
    border-width: 1px;
    border-style: solid;
    border-color: #e93b3d;
    border-radius: 4px;
    padding: 2px 4px;
  }
}
