.diamond-scroller {
  width: 100%;

  &__scroll-view {
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    
    &::-webkit-scrollbar {
      display: none;
    }
  }
  
  &__content {
    padding: 0 12px;
    display: flex;
    flex-direction: column;
  }
  
  &__row-container {
    display: flex;
    flex-direction: row;
    margin: 2px 0;
  }
  
  &__item-wrapper {
    padding: 6px 10px;
  }
  
  // 指示器相关样式
  &__indicator-container {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 4px;
    position: relative; // 添加相对定位，用于激活指示器的绝对定位
  }
  
  &__indicator {
    width: 6px;
    height: 6px;
    border-radius: 3px;
    background-color: #E0E0E0;
    margin: 0 3px;
    z-index: 1;
  }

  &__indicator-active {
    position: absolute;
    width: 12px;
    height: 6px;
    border-radius: 3px;
    background-color: #FF4142;
    z-index: 2;
    transition: left 0.3s ease; // 添加过渡效果
    top: 50%;
    margin-top: -3px; // 替代transform，将元素向上移动自身高度的一半
  }
}
