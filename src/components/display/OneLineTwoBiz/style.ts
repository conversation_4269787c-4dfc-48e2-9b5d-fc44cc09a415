import { CSSProperties } from 'react';

export default {
  container: {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    borderRadius: '8px',
    overflow: 'hidden',
    backgroundColor: '#ffffff',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.05)',
    position: 'relative',
  },
  imageContainer: {
    position: 'relative',
    width: '100%',
    borderRadius: '8px 8px 0 0',
    overflow: 'hidden',
  },
  distance: {
    position: 'absolute',
    left: '8px',
    bottom: '8px',
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    color: '#ffffff',
    fontSize: '12px',
    padding: '2px 6px',
    borderRadius: '4px',
  },
  content: {
    padding: '8px',
    display: 'flex',
    flexDirection: 'column',
    gap: '6px',
  },
  titleContainer: {
    marginBottom: '4px',
  },
  priceSalesContainer: {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceContainer: {
    display: 'flex',
    alignItems: 'baseline',
  },
  monthlySales: {
    fontSize: '12px',
    color: '#999999',
  },
  discountLabel: {
    position: 'absolute',
    top: '0',
    right: '0',
    backgroundColor: '#FF1530',
    color: '#ffffff',
    fontSize: '12px',
    padding: '2px 8px',
    borderRadius: '0 0 0 8px',
  },
} as Record<string, CSSProperties>;
