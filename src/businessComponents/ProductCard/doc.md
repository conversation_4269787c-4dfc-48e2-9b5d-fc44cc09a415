# ProductCard 促销商品卡

商品展示卡片组件，用于展示商品图片、标题、描述信息和价格，适用于促销商品展示场景。

## 使用场景

适用于电商场景中商品的展示，包含商品图片、标题、描述和价格信息。

## 样式示例

### 基本样式
含商品图片、标题、描述和价格（当前价格和原价）
![促销商品卡展示](示例图片路径)

## 代码演示

```tsx
import { ProductCard } from '@/components/businessComponents';

// 基本用法 - 与图片一致的促销商品卡
<ProductCard 
  imageUrl="https://img.example.com/crab.jpg"
  title="袁记云饺"
  description="帝王蟹畅吃"
  currentPrice={199}
  originalPrice={399}
  priceType="discount"
/>

// 不带原价
<ProductCard 
  imageUrl="https://img.example.com/food.jpg"
  title="美味佳肴"
  description="限时特惠"
  currentPrice={99}
/>

// 自定义样式
<ProductCard 
  imageUrl="https://img.example.com/special.jpg"
  title="特色菜品"
  description="精选食材"
  currentPrice={159}
  originalPrice={259}
  titleColor="#1890FF"
  descriptionColor="#52C41A"
  priceProps={{
    currentPriceColor: '#F5222D',
    originalPriceColor: '#8C8C8C'
  }}
/>

// 带点击事件
<ProductCard 
  imageUrl="https://img.example.com/clickable.jpg"
  title="点击查看详情"
  description="优惠多多"
  currentPrice={88}
  originalPrice={118}
  onClick={() => console.log('点击了商品卡片')}
/>
```

## API

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| imageUrl | 商品图片地址（必填） | `string` | - |
| imageWidth | 商品图片宽度 | `number` | `200` |
| imageHeight | 商品图片高度 | `number` | `200` |
| imageRadius | 图片圆角 | `number` | `8` |
| title | 商品标题（必填） | `string` | - |
| description | 商品描述 | `string` | - |
| currentPrice | 当前价格（必填） | `number` | - |
| originalPrice | 原价 | `number` | - |
| priceType | 价格展示类型 | `'default' \| 'discount'` | `'discount'` |
| priceProps | 价格组件的额外props | `Partial<PriceProps>` | `{}` |
| titleColor | 标题颜色 | `string` | `'#333333'` |
| descriptionColor | 描述文字颜色 | `string` | `'#666666'` |
| titleFontSize | 标题字体大小 | `number` | `16` |
| descriptionFontSize | 描述字体大小 | `number` | `14` |
| className | 自定义根元素类名 (Taro/Web) | `string` | - |
| style | 自定义根元素内联样式 (Taro/Web) | `React.CSSProperties` | - |
| containerStyle | 自定义根元素样式 (RN) | `ViewStyle` | - |
| imageStyle | 自定义图片样式 (RN) | `ImageStyle` | - |
| onClick | 卡片点击事件 | `() => void` | - |

## 注意事项

- 组件内部使用了Price组件来显示价格
- 默认为折扣样式展示价格
- 商品图片、标题和当前价格为必填项
- 组件具有悬停效果（提升、阴影增强）和图片略微放大的交互效果
- 标题和描述默认单行显示，超出部分会被截断并显示省略号 