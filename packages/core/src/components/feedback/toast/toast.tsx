import React, { useEffect, useState, memo } from 'react';
import { View } from '../../basic/View';
import { Text } from '../../basic/Text';

interface ToastProps {
  visible: boolean;
  text?: string;
  duration?: number; // ms
  onClose?: () => void;
  style?: any;
}

const Toast: React.FC<ToastProps> = ({ visible, text = '', duration = 2000, onClose, style }) => {
  const [show, setShow] = useState(visible);

  useEffect(() => {
    setShow(visible);
    if (visible && duration > 0) {
      const timer = setTimeout(() => {
        setShow(false);
        onClose?.();
      }, duration);
      return () => clearTimeout(timer);
    }
  }, [visible, duration, onClose]);

  if (!show) return null;

  return (
    <View
      style={{
        position: 'fixed',
        left: 0,
        right: 0,
        bottom: '20%',
        zIndex: 9999,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        ...style,
      }}
    >
      <View
        style={{
          background: 'rgba(0,0,0,0.7)',
          borderRadius: 8,
          padding: '12px 20px',
          maxWidth: 300,
        }}
      >
        <Text style={{ color: '#fff', fontSize: 16 }}>{text}</Text>
      </View>
    </View>
  );
};

export default memo(Toast);
