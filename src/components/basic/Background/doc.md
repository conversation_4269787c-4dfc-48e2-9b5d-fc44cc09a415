# 背景组件

背景图片传入，背景颜色，上下左右渐变，可设置渐变比例（两颜色过渡比例）

## 使用示例
```tsx
import { Background } from '@/components/basic'; // 请根据实际导出路径调整

// 基础用法
<Background backgroundWidth={130} backgroundHeight={40} />

// 自定义背景颜色
<Background backgroundWidth={130} backgroundHeight={40} backgroundColor={['#E1251B']} />

// 使用图片作为背景
<Background backgroundWidth={130} backgroundHeight={40} backgroundImageURL='https://img30.360buyimg.com/img/jfs/t1/301190/25/8889/652/682ee8f7F17feb63d/c33d3db673a40a68.png' />

// 渐变颜色
<Background backgroundWidth={130} backgroundHeight={40} colorGroupStartPosition={'TOP'} backgroundColor={['#E1251B', '#FF5252']} />

```

## 参数列表
| 属性名                   | 说明                          | 类型                        | 默认值 | 备注                   |
|--------------------------|-------------------------------|-----------------------------|--------|------------------------|
| `backgroundWidth`        | 背景宽度                      | `number`                    | -      | 必填                   |
| `backgroundHeight`       | 背景高度                      | `number`                    | -      | 必填                   |
| `backgroundColor`        | 背景颜色                      | `string[]`                  | ['#E1251B']      | 可选，支持多颜色，最多两个，多填不生效       |
| `backgroundImageURL`     | 背景图片 URL                  | `string`                    | -      | 可选                   |
| `gradientConnectionEffects`        | 拼接模式                    | `string`                    | -      | 可选                   |
| `backgroundBorderRadius`              | 组件边框   | `bunber`                    | -      | 可选 |
| `colorGroupStartPosition`| 色组起始位置                  | `colorGroupStartPositionType`| LEFT      | 可选                   |
| `gradientPercent`        | 渐变百分比                    | `number`                    | -      | 可选                   |
| `className`              | 自定义根元素类名 (Taro/Web)   | `string`                    | -      | 可选，仅 Taro/Web 可用 |


## 注意事项
- 组件长宽为必选项
- 背景图优先级高于颜色，传入后默认指定图片
- 由于H5与RN渲染上有差别，请注意区分
  - H5下：传入``` gradientConnectionEffects ```设置拼色模式。
  - RN下：只有渐变效果，传入``` gradientPercent ```控制渐变比例，若想用拼色模式建议使用两个背景组件拼接