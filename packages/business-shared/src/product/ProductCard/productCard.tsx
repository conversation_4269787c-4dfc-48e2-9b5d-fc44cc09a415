import React, { useEffect } from 'react';
import * as R from 'ramda';
import { View } from '../../../../core/src/components/basic/View';
import { ShopImageTag } from '../../../../core/src/components/display/ShopImageTag';
import { Title } from '../../../../core/src/components/display/title';
import { Price } from '../../../../core/src/components/display/price';
import { ProductCardProps } from './types';
import './productCard.scss';
import { CustomTag } from '../../../../core/src/components/display/CustomTag';
import { TagType } from '../../../../core/src/components/display/CustomTag/types';


/**
 * 商品卡片组件
 * 整合了商品图片、门店名称、商品名称和价格，适用于各种商品展示场景
 * 
 * @param {object} props - 组件属性
 * @param {string} [props.className] - 自定义类名
 * @param {CSSProperties} [props.style] - 自定义样式
 * @param {function} [props.onClick] - 点击卡片时的回调函数
 * 
 * @param {number} [props.discountCurrentPriceLeftOffset] - [Discount Type Only] 当前价格（左侧价格）距离背景左边的间距
 * @param {number} [props.discountOriginalPriceRightOffset] - [Discount Type Only] 原价（右侧价格）距离背景右边的间距
 * 
 * @param {string} props.imageSrc - 商品图片链接
 * @param {number} [props.imageSize=142] - 商品图片大小，单位px
 * @param {TagProps|TagProps[]} [props.imageTag] - 商品图片标签，可为单个标签或标签数组
 * 
 * @param {string} props.shopName - 门店名称
 * @param {number} [props.shopNameFontSize] - 门店名称字体大小
 * @param {number} [props.shopNameLines=1] - 门店名称显示行数
 * @param {'ellipsis'|'fade'} [props.shopNameEllipsis='ellipsis'] - 门店名称超出显示方式
 * @param {TagProps|TagProps[]} [props.shopNameTag] - 门店名称标签
 * @param {CSSProperties} [props.shopNameStyle] - 门店名称样式
 * 
 * @param {string} props.productName - 商品名称
 * @param {number} [props.productNameFontSize] - 商品名称字体大小
 * @param {number} [props.productNameLines=2] - 商品名称显示行数
 * @param {'ellipsis'|'fade'} [props.productNameEllipsis='ellipsis'] - 商品名称超出显示方式
 * @param {CSSProperties} [props.productNameStyle] - 商品名称样式
 * 
 * @param {number} [props.currentPrice] - 当前价格
 * @param {number} [props.originalPrice] - 原价
 * @param {'default'|'discount'} [props.priceType='default'] - 价格类型，default为常规显示，discount为折扣背景图片显示
 * @param {string} [props.discountBackgroundImageSrc] - 折扣背景图片链接，仅在priceType为discount时生效
 * @param {number} [props.discountBackgroundImageWidth] - 折扣背景图片宽度
 * @param {number} [props.discountBackgroundImageHeight] - 折扣背景图片高度
 * @param {string} [props.currentPriceColor] - 当前价格颜色
 * @param {string} [props.originalPriceColor] - 原价颜色
 * @param {number} [props.currentPriceFontSize] - 当前价格字体大小
 * @param {number} [props.originalPriceFontSize] - 原价字体大小
 */
const ProductCard: React.FC<ProductCardProps> = (props) => {
  const {
    // 整体商卡参数
    className,
    style,
    onClick,

    // 商品图片参数
    imageSrc,
    imageSize = 72,
    imageTag,

    // 价格间距参数
    discountCurrentPriceLeftOffset,
    discountOriginalPriceRightOffset,

    // 门店名称参数
    shopName,
    shopNameFontSize,
    shopNameLines,
    shopNameEllipsis,
    shopNameStyle,

    // 商品名称参数
    productName,
    productNameFontSize = 12,
    productNameLines = 1,
    productNameEllipsis = 'ellipsis',
    productNameStyle,

    // 商品价格参数
    currentPrice,
    originalPrice,
    priceType,
    discountBackgroundImageSrc,
    discountBackgroundImageWidth,
    discountBackgroundImageHeight,
    currentPriceColor,
    originalPriceColor,
    currentPriceFontSize,
    originalPriceFontSize,
    currencySymbol = '¥',
    currentPriceSymbolFontSize,
    originalPriceSymbolFontSize,
    currentPriceDecimalFontSize,
    originalPriceDecimalFontSize,
    strikethrough,

    // 价格折扣信息
    discountPrefixIcon,
    discountText


  } = props;

  useEffect(() => {
    console.log('ProductCard 初始化了', props);
  }, []);

  // 构建类名
  const rootClassName = [
    'product-card',
    className
  ].filter(Boolean).join(' ');

  // 处理点击事件
  const handleClick = R.defaultTo(() => { }, onClick);

  // 处理标签数据，确保是数组类型
  const formatTags = R.ifElse(
    R.isNil,
    R.always([]),
    R.ifElse(
      Array.isArray,
      R.identity,
      R.of
    )
  );


  // 拼接默认门店标题样式
  const defaultShopNameStyle = {
    color: '#CA1010',
    fontSize: 11,
    ...shopNameStyle,
  }

  // 拼接默认商品名称样式
  const defaultProductNameStyle = {
    fontSize: 11,
    color: '#505259',
    ...productNameStyle,
  }

  console.log(discountPrefixIcon, discountText, '==========>discountText')

  // 渲染视图
  return (
    <View
      className={rootClassName}
      style={{
        width: imageSize,
        ...style
      }}
      onClick={handleClick}
    >
      <View className="product-card__image-container">
        {/* 商品图 */}
        <ShopImageTag
          src={imageSrc}
          size={imageSize}
          tag={imageTag}
        />
      </View>

      <View className="product-card__content">

        {/* 门店名称 */}
        {
          shopName && <View className="product-card__shop-name-container">
            <Title
              fontSize={shopNameFontSize}
              lines={shopNameLines}
              ellipsis={shopNameEllipsis}
              style={defaultShopNameStyle}
              textAlign="center"
            >
              {shopName}
            </Title>
          </View>
        }

        {/* 商品名称 */}
        {
          !!productName && <View className="product-card__product-name-container">
            <Title
              fontSize={productNameFontSize}
              lines={productNameLines}
              ellipsis={productNameEllipsis}
              style={defaultProductNameStyle}
              textAlign="center"
            >
              {productName}
            </Title>
          </View>
        }

        {/* 商品价格 - 仅当存在currentPrice时才渲染 */}
        {!!currentPrice && (
          <View className="product-card__price-container">
            <Price
              currentPrice={currentPrice}
              originalPrice={originalPrice}
              type={priceType}
              discountBackgroundImageSrc={discountBackgroundImageSrc}
              discountBackgroundImageWidth={discountBackgroundImageWidth}
              discountBackgroundImageHeight={discountBackgroundImageHeight}
              discountCurrentPriceLeftOffset={discountCurrentPriceLeftOffset}
              discountOriginalPriceRightOffset={discountOriginalPriceRightOffset}
              currentPriceColor={currentPriceColor}
              originalPriceColor={originalPriceColor}
              currentPriceFontSize={currentPriceFontSize}
              originalPriceFontSize={originalPriceFontSize}
              currencySymbol={currencySymbol}
              currentPriceSymbolFontSize={currentPriceSymbolFontSize}
              originalPriceSymbolFontSize={originalPriceSymbolFontSize}
              currentPriceDecimalFontSize={currentPriceDecimalFontSize}
              originalPriceDecimalFontSize={originalPriceDecimalFontSize}
              strikethrough={strikethrough}
            />
          </View>
        )}


        {/* 价格折扣标 */}
        {discountText && <CustomTag
          type={TagType.ICON_PREFIX}
          prefixIcon={discountPrefixIcon?.imgUrl}
          iconStyle={{
            width: discountPrefixIcon?.width,
            height: discountPrefixIcon?.height
          }}
          text={discountText}
        />}
      </View>
    </View>
  );
};

export default ProductCard;
