# 📦 包管理最佳实践

## 🏗️ 项目架构

这是一个基于 **Lerna + Yarn Workspaces** 的 Monorepo 项目，包含以下包：

- `@jd/lifeui-core` - 核心 UI 组件库
- `@jd/lifeui-business-shared` - 共享业务组件
- `@jd/lifeui-business-groupon` - 团购业务组件
- `@jd/lifeui-business-instant` - 即时业务组件
- `@jd/lifeui-business-takeout` - 外卖业务组件
- `@jd/lifeui-business-travel` - 旅游业务组件

## 🚀 快速命令

### 日常开发
```bash
# 安装依赖
yarn install

# 构建所有包
yarn build

# 构建特定包
yarn build:core

# 开发模式
yarn dev:core

# 运行测试
yarn test

# 代码检查
yarn lint
```

### 版本管理
```bash
# 查看变更的包
yarn check:changed

# 升级版本
yarn version:patch    # 1.0.0 -> 1.0.1
yarn version:minor    # 1.0.0 -> 1.1.0
yarn version:major    # 1.0.0 -> 2.0.0
yarn version:prerelease # 1.0.0 -> 1.0.1-beta.0
```

### 发布管理
```bash
# 一键发布 (推荐)
yarn release:patch
yarn release:minor
yarn release:major
yarn release:beta

# 手动发布
yarn publish:all
yarn publish:core
```

### 依赖管理
```bash
# 检查依赖
yarn check:deps

# 安全审计
yarn audit:security

# 清理重置
yarn reset
```

## 📋 开发工作流

### 1. 添加新功能
```bash
# 1. 创建功能分支
git checkout -b feature/new-component

# 2. 开发组件
cd packages/core/src/components
# ... 开发代码

# 3. 构建测试
yarn build:core
yarn test

# 4. 提交代码
git add .
git commit -m "feat: add new component"

# 5. 推送并创建 PR
git push origin feature/new-component
```

### 2. 发布新版本
```bash
# 1. 确保在主分支
git checkout main
git pull origin main

# 2. 检查变更
yarn check:changed

# 3. 运行完整测试
yarn test
yarn lint

# 4. 发布 (自动处理版本、构建、发布)
yarn release:patch
```

## 🔧 依赖管理策略

### 内部包依赖
- 业务包依赖核心包：`@jd/lifeui-core`
- 使用 `^` 版本范围，确保兼容性
- 定期更新内部依赖版本

### 外部依赖
- React/React Native 作为 `peerDependencies`
- 工具库作为 `dependencies`
- 构建工具作为 `devDependencies`

### 版本策略
- 使用 **Independent** 模式，每个包独立版本
- 遵循 **Semantic Versioning**
- 使用 **Conventional Commits** 自动生成版本

## 📤 发布策略

### 发布渠道
- **稳定版本**: 发布到 `latest` tag
- **预发布版本**: 发布到 `beta` tag
- **内部版本**: 发布到 JD 内部 npm 仓库

### 发布检查清单
- [ ] 代码已合并到主分支
- [ ] 所有测试通过
- [ ] 代码检查通过
- [ ] 构建成功
- [ ] 版本号正确
- [ ] 变更日志已更新

## 🔒 安全管理

### 依赖安全
```bash
# 定期运行安全审计
yarn audit:security

# 更新有安全问题的依赖
yarn upgrade package-name
```

### 发布安全
- 使用 2FA 保护 npm 账户
- 限制发布权限
- 定期轮换访问令牌

## 📊 监控和分析

### 包大小监控
```bash
# 检查包大小
yarn check:deps

# 分析包内容
npx bundlephobia @jd/lifeui-core
```

### 依赖分析
```bash
# 查看依赖树
yarn list --depth=0

# 查找重复依赖
yarn list --pattern "react"
```

## 🚨 常见问题解决

### 1. 依赖冲突
```bash
# 查看冲突
yarn list --pattern "package-name"

# 解决方案：在根 package.json 添加 resolutions
{
  "resolutions": {
    "package-name": "^1.0.0"
  }
}
```

### 2. 构建失败
```bash
# 清理并重新构建
yarn clean:all
yarn install
yarn build
```

### 3. 发布权限问题
```bash
# 检查登录状态
npm whoami

# 重新登录
npm login --registry=http://npm.jd.com/

# 检查包权限
npm access list packages @jd
```

### 4. 版本冲突
```bash
# 查看当前版本
npx lerna list

# 手动设置版本
npx lerna version --no-git-tag-version
```

## 📚 相关文档

- [Lerna 官方文档](https://lerna.js.org/)
- [Yarn Workspaces 文档](https://yarnpkg.com/features/workspaces)
- [Semantic Versioning](https://semver.org/)
- [Conventional Commits](https://conventionalcommits.org/)
