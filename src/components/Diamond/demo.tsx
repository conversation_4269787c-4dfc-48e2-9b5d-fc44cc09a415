import React from 'react';
import { View } from '@tarojs/components';
import { Diamond } from './index';

const DiamondDemo: React.FC = () => {
  return (
    <View style={{ padding: '20px' }}>
      <View style={{ marginBottom: '16px' }}>
        <h3>基础钻石选项</h3>
        <View style={{ display: 'flex', flexDirection: 'row' }}>
          <View style={{ marginRight: '16px' }}>
            <Diamond 
              width={80}
              imageSize={60}
              imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
              text="钻石1"
              selected={true}
            />
          </View>
          <View style={{ marginRight: '16px' }}>
            <Diamond 
              width={80}
              imageSize={60}
              imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
              text="钻石2"
              selected={false}
            />
          </View>
        </View>
      </View>
      
      <View style={{ marginBottom: '16px' }}>
        <h3>带渐变背景的钻石选项</h3>
        <View style={{ display: 'flex', flexDirection: 'row' }}>
          <View style={{ marginRight: '16px' }}>
            <Diamond 
              width={80}
              imageSize={60}
              imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
              text="带渐变1"
              selected={true}
              textSelectedBackgroundStartColor="#FF0000"
              textSelectedBackgroundEndColor="#FF9500"
              imageSelectedBorder={true}
              imageSelectedBorderColor="#FF0000"
            />
          </View>
          <View style={{ marginRight: '16px' }}>
            <Diamond 
              width={80}
              imageSize={60}
              imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
              text="带渐变2"
              selected={false}
              textSelectedBackgroundColor="#4285F4"
              imageSelectedBorderRadius={8}
            />
          </View>
        </View>
      </View>
      
      <View style={{ marginBottom: '16px' }}>
        <h3>禁用状态的钻石选项</h3>
        <Diamond 
          width={80}
          imageSize={60}
          imageSrc="https://img13.360buyimg.com/imagetools/jfs/t1/223219/37/10344/3469/61def716Ebf699349/d519ca7275cc2bcf.png"
          text="禁用选项"
          disabled={true}
        />
      </View>
    </View>
  );
};

export default DiamondDemo;
