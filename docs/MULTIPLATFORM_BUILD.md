# 🎯 多端构建指南

## 📦 构建产物结构

经过多端构建后，`dist` 目录结构如下：

```
packages/core/dist/
├── rn/                          # React Native 平台
│   ├── index.rn.es.js          # ES 模块
│   ├── index.rn.cjs.js         # CommonJS 模块
│   └── style.css               # 样式文件
├── h5/                          # H5 平台
│   ├── index.h5.es.js          # ES 模块
│   ├── index.h5.cjs.js         # CommonJS 模块
│   └── style.css               # 样式文件
├── weapp/                       # 微信小程序平台
│   ├── index.weapp.es.js       # ES 模块
│   ├── index.weapp.cjs.js      # CommonJS 模块
│   └── style.css               # 样式文件
├── index.js                     # 主入口 (默认 h5)
├── index.cjs                    # CommonJS 主入口
├── index.rn.js                  # RN 平台入口
├── index.h5.js                  # H5 平台入口
└── index.weapp.js               # 微信小程序入口
```

## 🚀 构建命令

### 构建所有平台
```bash
# 构建所有支持的平台 (rn, h5, weapp, alipay, swan, tt, qq, jd)
yarn build:core:multi

# 或者在 core 包目录下
cd packages/core
yarn build:multi
```

### 构建特定平台
```bash
# 只构建 React Native
yarn build:core:rn

# 只构建 H5
yarn build:core:h5

# 只构建微信小程序
yarn build:core:weapp

# 构建多个指定平台
node scripts/build-multiplatform.js packages/core rn,h5,weapp
```

## 📱 平台支持

| 平台 | 标识 | 文件扩展名优先级 | 说明 |
|------|------|------------------|------|
| **React Native** | `rn` | `.rn.tsx` → `.tsx` | 原生移动应用 |
| **H5** | `h5` | `.h5.tsx` → `.tsx` | 移动端网页 |
| **微信小程序** | `weapp` | `.weapp.tsx` → `.tsx` | 微信小程序 |
| **支付宝小程序** | `alipay` | `.alipay.tsx` → `.tsx` | 支付宝小程序 |
| **百度小程序** | `swan` | `.swan.tsx` → `.tsx` | 百度智能小程序 |
| **字节小程序** | `tt` | `.tt.tsx` → `.tsx` | 抖音/今日头条小程序 |
| **QQ小程序** | `qq` | `.qq.tsx` → `.tsx` | QQ 小程序 |
| **京东小程序** | `jd` | `.jd.tsx` → `.tsx` | 京东小程序 |

## 🔧 使用方式

### 1. 自动平台选择 (推荐)
```javascript
// 会根据环境自动选择合适的平台版本
import { Button } from '@jd/lifeui-core';
```

### 2. 显式平台选择
```javascript
// React Native
import { Button } from '@jd/lifeui-core/rn';

// H5
import { Button } from '@jd/lifeui-core/h5';

// 微信小程序
import { Button } from '@jd/lifeui-core/weapp';
```

### 3. 条件导入
```javascript
// 根据运行环境动态导入
let Button;
if (process.env.TARO_ENV === 'rn') {
  Button = require('@jd/lifeui-core/rn').Button;
} else if (process.env.TARO_ENV === 'h5') {
  Button = require('@jd/lifeui-core/h5').Button;
} else {
  Button = require('@jd/lifeui-core/weapp').Button;
}
```

## 📝 开发规范

### 1. 文件命名规范
```
components/Button/
├── index.ts                     # 通用入口
├── Button.tsx                   # 通用实现
├── Button.rn.tsx                # React Native 特定实现
├── Button.h5.tsx                # H5 特定实现
├── Button.weapp.tsx             # 微信小程序特定实现
└── types.ts                     # 类型定义
```

### 2. 平台特定代码
```typescript
// Button.rn.tsx - React Native 版本
import { TouchableOpacity, Text } from 'react-native';

export const Button = ({ children, onPress }) => (
  <TouchableOpacity onPress={onPress}>
    <Text>{children}</Text>
  </TouchableOpacity>
);

// Button.h5.tsx - H5 版本  
import { View } from '@tarojs/components';

export const Button = ({ children, onClick }) => (
  <View onClick={onClick} className="button">
    {children}
  </View>
);
```

### 3. 环境变量
构建时会自动注入 `process.env.TARO_ENV`：
- `rn` - React Native
- `h5` - H5
- `weapp` - 微信小程序
- 其他小程序平台对应的标识

## 🔍 构建原理

### 1. 文件解析优先级
构建系统会按以下优先级查找文件：
1. 平台特定文件 (如 `.rn.tsx`)
2. 通用文件 (如 `.tsx`)

### 2. 外部依赖处理
不同平台的外部依赖：
- **RN**: `react-native`
- **H5**: `react-dom`  
- **小程序**: `@tarojs/components`, `@tarojs/taro`

### 3. 模块解析条件
- **RN**: `['react-native', 'import', 'module', 'default']`
- **H5**: `['browser', 'import', 'module', 'default']`
- **小程序**: `[platform, 'import', 'module', 'default']`

## 🚨 注意事项

### 1. 样式处理
- 每个平台都会生成独立的 `style.css`
- 样式文件需要在使用时单独引入

### 2. 类型定义
- 类型定义只生成一份，位于 `dist/types/`
- 所有平台共享相同的类型定义

### 3. 包大小
- 不同平台的包大小可能不同
- RN 版本通常最大 (包含更多原生组件)
- 小程序版本相对较小

### 4. 兼容性
- 确保平台特定的代码只使用对应平台支持的 API
- 通用代码应该在所有平台都能正常运行

## 📚 相关文档

- [Taro 多端开发](https://docs.taro.zone/docs/platform-plugin)
- [React Native 组件](https://reactnative.dev/docs/components-and-apis)
- [Taro 组件库](https://docs.taro.zone/docs/components/about)

## 🔧 故障排除

### 构建失败
```bash
# 清理并重新构建
rm -rf packages/core/dist
yarn build:core:multi
```

### 依赖问题
```bash
# 检查外部依赖配置
# 在 scripts/build-multiplatform.js 中添加缺失的依赖到 external 数组
```

### 平台特定问题
```bash
# 只构建有问题的平台进行调试
yarn build:core:rn
yarn build:core:h5
yarn build:core:weapp
```
