import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import CustomTagList from './customTagList';
import { TagType } from '../CustomTag/types';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    padding: 16,
  },
  section: {
    marginBottom: 20,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
});

const CustomTagListDemo: React.FC = () => {
  const [showAll, setShowAll] = useState(false);

  // 基础标签列表数据 - 使用CustomTagProps类型
  const basicTags = [
    { type: TagType.TEXT, text: '热门' },
    { type: TagType.TEXT, text: '推荐' },
    { type: TagType.TEXT, text: '特惠' },
    { type: TagType.TEXT, text: '满减活动' },
    { type: TagType.TEXT, text: '新品上市' },
    { type: TagType.TEXT, text: '限时折扣' },
    { type: TagType.TEXT, text: '免运费' },
    { type: TagType.TEXT, text: '会员专享' },
    { type: TagType.TEXT, text: '精选商品' },
    { type: TagType.TEXT, text: '降价' },
    { type: TagType.TEXT, text: '库存紧张' }
  ];

  // 自定义标签列表数据
  const customTags = [
    {
      type: TagType.TEXT,
      text: '新品',
      backgroundColor: '#e6f7ff',
      textStyle: { color: '#1890ff' }
    },
    {
      type: TagType.TEXT,
      text: '热卖',
      backgroundColor: '#fff2e8',
      textStyle: { color: '#fa541c' }
    },
    {
      type: TagType.TEXT,
      text: '促销',
      backgroundColor: '#f6ffed',
      textStyle: { color: '#52c41a' }
    },
    {
      type: TagType.TEXT,
      text: '满减',
      backgroundColor: '#fff0f6',
      textStyle: { color: '#eb2f96' }
    },
    {
      type: TagType.TEXT,
      text: '限时',
      backgroundColor: '#fcf4d6',
      textStyle: { color: '#faad14' },
      showBorder: true
    },
    {
      type: TagType.TEXT,
      text: '会员专享',
      backgroundColor: '#f9f0ff',
      textStyle: { color: '#722ed1' }
    }
  ];

  // 展示所有标签
  const toggleShowAll = () => {
    setShowAll(prev => !prev);
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.content}>
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>基础标签列表（单行）</Text>
        <CustomTagList 
          tags={basicTags}
          maxLines={1}
          truncateMode="ellipsis"
        />
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>基础标签列表（多行）</Text>
        <CustomTagList 
          tags={basicTags}
          maxLines={2}
          lineHeight={28}
          verticalGap={8}
          truncateMode="ellipsis"
        />
      </View>
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>自定义标签列表</Text>
        <CustomTagList 
          tags={customTags}
          maxLines={2}
          width={300}
          truncateMode="ellipsis"
        />
        <TouchableOpacity 
          onPress={toggleShowAll}
          style={{
            marginTop: 10,
            padding: 8,
            backgroundColor: '#1890ff',
            borderRadius: 4,
            alignItems: 'center'
          }}
        >
          <Text style={{ color: '#fff' }}>
            {showAll ? '隐藏部分标签' : '查看全部标签'}
          </Text>
        </TouchableOpacity>
      </View>
      
      {showAll && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>显示所有标签</Text>
          <CustomTagList 
            tags={customTags}
            maxLines={Infinity}
          />
        </View>
      )}
      
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>拉伸填满标签列表</Text>
        <CustomTagList 
          tags={basicTags.slice(0, 3)}
          stretch={true}
          gap={12}
          truncateMode="none"
        />
      </View>
    </ScrollView>
  );
};

export default CustomTagListDemo; 