#!/bin/bash

# 内部依赖管理脚本
# 用于管理 monorepo 中包之间的依赖关系

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 显示帮助信息
show_help() {
  echo -e "${BLUE}📦 内部依赖管理工具${NC}"
  echo ""
  echo "用法: $0 [命令] [选项]"
  echo ""
  echo "命令:"
  echo "  check     检查内部依赖状态"
  echo "  update    更新内部依赖版本"
  echo "  sync      同步所有包的内部依赖版本"
  echo "  validate  验证依赖配置正确性"
  echo ""
  echo "示例:"
  echo "  $0 check"
  echo "  $0 update @jd/lifeui-core 1.2.0"
  echo "  $0 sync"
}

# 检查内部依赖状态
check_internal_deps() {
  echo -e "${GREEN}🔍 检查内部依赖状态...${NC}"
  
  for package in packages/*/package.json; do
    package_dir=$(dirname "$package")
    package_name=$(basename "$package_dir")
    
    echo -e "${YELLOW}📦 $package_name:${NC}"
    
    # 检查 dependencies 中的内部依赖
    if grep -q "@jd/lifeui-" "$package"; then
      echo "  内部依赖:"
      grep "@jd/lifeui-" "$package" | sed 's/^/    /'
    else
      echo "  无内部依赖"
    fi
    
    # 检查是否有重复依赖
    if grep -A 20 '"devDependencies"' "$package" | grep -q "@jd/lifeui-"; then
      echo -e "  ${RED}⚠️  devDependencies 中存在内部依赖 (应该移除)${NC}"
    fi
    
    echo ""
  done
}

# 更新特定内部依赖的版本
update_internal_dep() {
  local dep_name="$1"
  local new_version="$2"
  
  if [ -z "$dep_name" ] || [ -z "$new_version" ]; then
    echo -e "${RED}❌ 请提供依赖名称和版本号${NC}"
    echo "用法: $0 update @jd/lifeui-core 1.2.0"
    exit 1
  fi
  
  echo -e "${GREEN}🔄 更新 $dep_name 到版本 $new_version...${NC}"
  
  for package in packages/*/package.json; do
    package_dir=$(dirname "$package")
    package_name=$(basename "$package_dir")
    
    if grep -q "\"$dep_name\":" "$package"; then
      echo -e "${YELLOW}📦 更新 $package_name...${NC}"
      
      # 使用 node 脚本精确更新版本
      node -e "
        const fs = require('fs');
        const path = '$package';
        const pkg = JSON.parse(fs.readFileSync(path, 'utf8'));
        
        if (pkg.dependencies && pkg.dependencies['$dep_name']) {
          pkg.dependencies['$dep_name'] = '^$new_version';
          console.log('  ✅ 更新 dependencies');
        }
        
        fs.writeFileSync(path, JSON.stringify(pkg, null, 2) + '\n');
      "
    fi
  done
  
  echo -e "${GREEN}✅ 更新完成！${NC}"
}

# 同步所有包的内部依赖版本
sync_internal_deps() {
  echo -e "${GREEN}🔄 同步内部依赖版本...${NC}"
  
  # 获取各包的当前版本
  declare -A package_versions
  for package in packages/*/package.json; do
    package_dir=$(dirname "$package")
    pkg_name=$(node -e "console.log(JSON.parse(require('fs').readFileSync('$package', 'utf8')).name)")
    pkg_version=$(node -e "console.log(JSON.parse(require('fs').readFileSync('$package', 'utf8')).version)")
    package_versions["$pkg_name"]="$pkg_version"
  done
  
  # 更新所有内部依赖
  for package in packages/*/package.json; do
    package_dir=$(dirname "$package")
    package_name=$(basename "$package_dir")
    
    echo -e "${YELLOW}📦 同步 $package_name...${NC}"
    
    node -e "
      const fs = require('fs');
      const path = '$package';
      const pkg = JSON.parse(fs.readFileSync(path, 'utf8'));
      let updated = false;
      
      if (pkg.dependencies) {
        Object.keys(pkg.dependencies).forEach(dep => {
          if (dep.startsWith('@jd/lifeui-')) {
            const versions = $(declare -p package_versions);
            // 这里需要传递版本信息，简化处理
            console.log('  检查依赖:', dep);
          }
        });
      }
    "
  done
  
  echo -e "${GREEN}✅ 同步完成！${NC}"
}

# 验证依赖配置
validate_deps() {
  echo -e "${GREEN}🔍 验证依赖配置...${NC}"
  
  local has_errors=false
  
  for package in packages/*/package.json; do
    package_dir=$(dirname "$package")
    package_name=$(basename "$package_dir")
    
    # 检查是否在 devDependencies 中有内部依赖
    if grep -A 20 '"devDependencies"' "$package" | grep -q "@jd/lifeui-"; then
      echo -e "${RED}❌ $package_name: devDependencies 中不应包含内部依赖${NC}"
      has_errors=true
    fi
    
    # 检查版本范围是否合理
    if grep "@jd/lifeui-" "$package" | grep -q '".*".*"'; then
      while IFS= read -r line; do
        if echo "$line" | grep -q '"[0-9]'; then
          echo -e "${YELLOW}⚠️  $package_name: 建议使用 ^ 版本范围${NC}"
        fi
      done < <(grep "@jd/lifeui-" "$package")
    fi
  done
  
  if [ "$has_errors" = false ]; then
    echo -e "${GREEN}✅ 依赖配置验证通过！${NC}"
  else
    echo -e "${RED}❌ 发现配置问题，请修复后重试${NC}"
    exit 1
  fi
}

# 主函数
main() {
  case "${1:-check}" in
    "check")
      check_internal_deps
      ;;
    "update")
      update_internal_dep "$2" "$3"
      ;;
    "sync")
      sync_internal_deps
      ;;
    "validate")
      validate_deps
      ;;
    "help"|"-h"|"--help")
      show_help
      ;;
    *)
      echo -e "${RED}❌ 未知命令: $1${NC}"
      show_help
      exit 1
      ;;
  esac
}

main "$@"
