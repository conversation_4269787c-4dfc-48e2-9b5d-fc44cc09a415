import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import HorizontalList from './index';
import { ListItemType } from './types';

const couponSample = {
  title: '优惠券示例',
  amount: 20,
  condition: '满200可用',
  backgroundImage: 'https://img10.360buyimg.com/img/jfs/t1/301991/36/7727/2719/682d74a6Fc9e341eb/a6456d94c59ba582.png',
};

const productSample = {
  title: '商品示例',
  imageSrc: 'https://img14.360buyimg.com/img/jfs/t1/308260/18/2740/31575/682d74bcF0cc9debb/20b692af72bccb3a.png',
  currentPrice: 99.9,
  originalPrice: 129.9,
  imageSize: 72,
};

const couponItems: ListItemType[] = Array(5)
  .fill(null)
  .map((_, index) => ({
    type: 'coupon',
    data: {
      ...couponSample,
      title: `优惠券${index + 1}`,
      amount: (index + 1) * 10,
    },
  }));

const productItems: ListItemType[] = Array(5)
  .fill(null)
  .map((_, index) => ({
    type: 'product',
    data: {
      ...productSample,
      title: `商品${index + 1}`,
      currentPrice: 99.9 + index * 10,
    },
  }));

/**
 * HorizontalList组件演示 - React Native版
 */
const HorizontalListDemo: React.FC = () => {
  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.title}>优惠券横向列表</Text>
        <Text style={styles.description}>默认样式展示多张优惠券</Text>
        <HorizontalList
          items={couponItems}
          horizontalPadding={16}
          itemSpacing={12}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.title}>商品横向列表</Text>
        <Text style={styles.description}>默认样式展示多个商品卡片</Text>
        <HorizontalList
          items={productItems}
          horizontalPadding={16}
          itemSpacing={12}
          itemWidth={120}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.title}>混合内容列表</Text>
        <Text style={styles.description}>同时展示优惠券和商品卡片</Text>
        <HorizontalList
          items={[...couponItems.slice(0, 2), ...productItems.slice(0, 2)]}
          horizontalPadding={16}
          itemSpacing={12}
          showScrollbar={true}
        />
      </View>

      <View style={styles.section}>
        <Text style={styles.title}>自定义渲染</Text>
        <Text style={styles.description}>通过renderItem自定义渲染列表项</Text>
        <HorizontalList
          items={couponItems}
          horizontalPadding={16}
          itemSpacing={12}
          renderItem={(item, index) => {
            // 确保返回React元素
            if (item.type === 'coupon') {
              return (
                <View key={index} style={styles.customItem}>
                  <View style={styles.customTag}>
                    <Text style={styles.customTagText}>优惠</Text>
                  </View>
                  <View>
                    <Text style={styles.customTitle}>{item.data.title}</Text>
                    <Text style={styles.customAmount}>¥{item.data.amount}</Text>
                  </View>
                </View>
              );
            }
            return null;
          }}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 24,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  customItem: {
    width: 100,
    height: 100,
    backgroundColor: '#f0f0f0',
    borderRadius: 8,
    padding: 8,
    position: 'relative',
  },
  customTag: {
    position: 'absolute',
    top: 0,
    right: 0,
    backgroundColor: '#FF5000',
    borderTopRightRadius: 8,
    borderBottomLeftRadius: 8,
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  customTagText: {
    color: 'white',
    fontSize: 10,
  },
  customTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    marginTop: 24,
  },
  customAmount: {
    fontSize: 16,
    color: '#FF5000',
    marginTop: 8,
  },
});

export default HorizontalListDemo; 