import React from 'react';
import {View} from '../../../../core/src/components/basic/View';
import HorizontalList from './index';
import { ListItemType } from './types';

const couponSample = {
  title: '优惠券示例',
  amount: 20,
  condition: '满200可用',
  backgroundImage: 'https://img10.360buyimg.com/img/jfs/t1/301991/36/7727/2719/682d74a6Fc9e341eb/a6456d94c59ba582.png',
};

const productSample = {
  title: '商品示例',
  imageSrc: 'https://img14.360buyimg.com/img/jfs/t1/308260/18/2740/31575/682d74bcF0cc9debb/20b692af72bccb3a.png',
  currentPrice: 99.9,
  originalPrice: 129.9,
  imageSize: 72,
};

const couponItems: ListItemType[] = Array(5)
  .fill(null)
  .map((_, index) => ({
    type: 'coupon',
    data: {
      ...couponSample,
      title: `优惠券${index + 1}`,
      amount: (index + 1) * 10,
    },
  }));

const productItems: ListItemType[] = Array(5)
  .fill(null)
  .map((_, index) => ({
    type: 'product',
    data: {
      ...productSample,
      title: `商品${index + 1}`,
      currentPrice: 99.9 + index * 10,
    },
  }));

/**
 * HorizontalList组件演示
 */
const HorizontalListDemo: React.FC = () => {
  return (
    <View className="demo-container">
      <View className="demo-section">
        <View className="demo-title">优惠券横向列表</View>
        <View className="demo-description">默认样式展示多张优惠券</View>
        <HorizontalList
          items={couponItems}
          horizontalPadding={16}
          itemSpacing={12}
        />
      </View>

      <View className="demo-section">
        <View className="demo-title">商品横向列表</View>
        <View className="demo-description">默认样式展示多个商品卡片</View>
        <HorizontalList
          items={productItems}
          horizontalPadding={16}
          itemSpacing={12}
          itemWidth={120}
        />
      </View>

      <View className="demo-section">
        <View className="demo-title">混合内容列表</View>
        <View className="demo-description">同时展示优惠券和商品卡片</View>
        <HorizontalList
          items={[...couponItems.slice(0, 2), ...productItems.slice(0, 2)]}
          horizontalPadding={16}
          itemSpacing={12}
          showScrollbar={true}
        />
      </View>

      <View className="demo-section">
        <View className="demo-title">自定义渲染</View>
        <View className="demo-description">通过renderItem自定义渲染列表项</View>
        <HorizontalList
          items={couponItems}
          horizontalPadding={16}
          itemSpacing={12}
          renderItem={(item, index) => (
            <View key={index} className="custom-item">
              {item.type === 'coupon' && (
                <View className="custom-tag">优惠</View>
              )}
              {item.type === 'product' && (
                <View className="custom-tag">商品</View>
              )}
              {item.type === 'coupon' && (
                <View>
                  <View className="custom-title">{item.data.title}</View>
                  <View className="custom-amount">¥{item.data.amount}</View>
                </View>
              )}
            </View>
          )}
        />
      </View>
    </View>
  );
};

export default HorizontalListDemo; 