import { Animated } from "react-native";
import withClassName from "../utils/withClassName.rn";
import { useScreen } from '../../../hooks/useScreen';

const AnimatedView = (props) => {
  const { style, ...restProps } = props;
  const { scaleStyle } = useScreen();
  
  // 对style应用自动缩放（只处理对象类型的style）
  const scaledStyle = style && typeof style === 'object' && !Array.isArray(style) 
    ? scaleStyle(style as Record<string, any>) 
    : style;

  return <Animated.View {...restProps} style={scaledStyle as any} />;
};

export default withClassName()(AnimatedView); 