import React from 'react';
import { CustomTagProps } from '../../CustomTag';
/**
 * 标题组件Props接口
 */
export interface TitleProps {
  /** 标题内容 */
  children: React.ReactNode;
  /** 标题字号大小 */
  fontSize?: number | TitleLevel;
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 标签图片列表 */
  tags?: CustomTagProps[];
  /** 标签图片间距 */
  tagGap?: number;
  /** 标签与标题的间距 */
  tagTitleGap?: number;
  /** 标题显示的行数，超出部分会被截断 */
  lines?: number;
  /** 标题超出行数限制时的省略方式 */
  // clip: 直接截断 |  ellipsis: 显示省略号 | fade: 渐变省略 visible: 文本不截断
  ellipsis?: 'clip' | 'ellipsis' | 'fade';
  /** 标题对齐方式 */
  textAlign?: 'left' | 'center' | 'right';
  /** 其他属性 */
  [key: string]: any;
} 

/**
 * 标题组件的默认样式
 * 24 20 18 16 14 12
 */
export enum TitleLevel {
  'one' = 24,
  'two' = 20,
  'three' = 18,
  'four' = 16,
  'five' = 14,
  'six' = 12
}; 