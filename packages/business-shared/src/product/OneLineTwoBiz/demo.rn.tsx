import React from 'react';
import { View } from 'react-native';
import OneLineTwoBiz from './oneLineTwoBiz';

/**
 * OneLineTwoBiz组件示例（React Native版本）
 */
const Demo: React.FC = () => {
  return (
    <View style={{ padding: 100, marginTop: 100, backgroundColor: '#f5f5f5' }}>
      <View style={{ marginBottom: 16 }}>
        <OneLineTwoBiz
          imageSrc="https://img11.360buyimg.com/img/jfs/t1/307887/33/3516/316584/68306e33F245b2649/eb2c953236db7a78.png"
          distance="3.5km"
          productName="壹品白鹅 品牌纯色品牌纯色+跳"
          currentPrice={48}
          originalPrice={98}
          imageSize={176}
          imageTag={{
            position: 'bottomLeft',
            text: '热卖',
            backgroundColor: '#ff4d4f',
            textStyle: { color: '#fff' }
          }}
          monthlySales="200+"
          onClick={() => { console.log('点击了美甲卡片'); }}
        />
      </View>

      <View style={{ marginBottom: 16 }}>
        <OneLineTwoBiz
          imageSrc="https://img11.360buyimg.com/img/jfs/t1/307887/33/3516/316584/68306e33F245b2649/eb2c953236db7a78.png"
          distance="1.2km"
          productName="法式光疗美甲 清新春夏款"
          currentPrice={88}
          imageSize={176}
          imageTag={{
            position: 'topLeft',
            text: '热卖',
            backgroundColor: '#ff4d4f',
            textStyle: { color: '#fff' }
          }}
          originalPrice={168}
          monthlySales="150+"
          onClick={() => { console.log('点击了美甲卡片'); }}
        />
      </View>
    </View>
  );
};

export default Demo;
