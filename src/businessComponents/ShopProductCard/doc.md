# ShopProductCard 店铺商品卡片

店铺商品卡片组件是一个集成了店铺信息头部和横向商品列表的复合组件，支持圆角、阴影等卡片样式，适用于电商、外卖等场景的店铺展示。

## 组件特性

- 上半部分使用 ShopHeader 组件展示店铺基本信息
- 下半部分使用 HorizontalList 组件展示商品列表
- 支持卡片圆角、阴影、边框等样式自定义
- 支持内容间距和内边距调整
- 跨平台兼容（Taro/Web、React Native）

## 使用方法

```tsx
import { ShopProductCard } from '@uicomponent/businessComponents';
import { ContentType } from '@uicomponent/businessComponents/ShopHeader/types';

// 基础用法
const shopInfo = {
  imageUrl: "https://img.example.com/shop/logo.png",
  imageWidth: 72,
  imageHeight: 72,
  lines: [
    {
      left: [
        {
          type: ContentType.TITLE,
          text: "下酒 (林肯公园店)",
          props: {
            fontSize: 20,
            style: { color: '#333', fontWeight: 'bold' }
          }
        }
      ],
      right: [
        {
          type: ContentType.TAG,
          text: '已收藏',
          props: {
            backgroundColor: '#fff9f0',
            textStyle: { color: '#996633' }
          }
        }
      ]
    },
    {
      left: [
        {
          type: ContentType.TAG,
          text: '4.9分',  
          props: {
            backgroundColor: '#fff0f0',
            textStyle: { color: '#e93b3d' }
          }
        }
      ]
    }
  ]
};

const productList = {
  items: [
    {
      type: 'product',
      data: {
        title: '下班快乐必点',
        imageSrc: 'https://img.example.com/product1.jpg',
        currentPrice: 128,
        originalPrice: 512
      }
    },
    {
      type: 'product',
      data: {
        title: '下班快乐3-4人套餐',
        imageSrc: 'https://img.example.com/product2.jpg',
        currentPrice: 302,
        originalPrice: 630
      }
    }
  ],
  itemWidth: 120,
  itemSpacing: 12,
  horizontalPadding: 0
};

<ShopProductCard 
  shopInfo={shopInfo}
  productList={productList}
  borderRadius={12}
  showShadow={true}
  padding={16}
  contentSpacing={16}
  backgroundColor="#FFFFFF"
  onClick={() => console.log('卡片被点击')}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| shopInfo | 店铺信息配置 | `Omit<ShopHeaderProps, 'className' \| 'style' \| 'containerStyle'>` | - |
| productList | 商品列表配置 | `Omit<HorizontalListProps, 'className' \| 'style' \| 'containerStyle'>` | - |
| borderRadius | 卡片圆角大小 | `number` | `12` |
| backgroundColor | 卡片背景色 | `string` | `'#FFFFFF'` |
| showShadow | 卡片阴影效果 | `boolean` | `true` |
| border | 卡片边框 | `string` | - |
| contentSpacing | 商品列表与店铺信息间的间距 | `number` | `16` |
| padding | 卡片内边距 | `number \| { top?: number; right?: number; bottom?: number; left?: number }` | `16` |
| className | 自定义根元素类名 (Taro/Web) | `string` | - |
| style | 自定义根元素内联样式 (Taro/Web) | `React.CSSProperties` | - |
| containerStyle | 自定义根元素样式 (RN) | `ViewStyle` | - |
| shopInfoStyle | 店铺信息区域样式 | `React.CSSProperties \| ViewStyle` | - |
| productListStyle | 商品列表区域样式 | `React.CSSProperties \| ViewStyle` | - |
| onClick | 整个卡片点击事件 | `() => void` | - |

## 子组件类型参考

### ShopHeaderProps
请参考 [ShopHeader 文档](../ShopHeader/doc.md)

### HorizontalListProps
请参考 [HorizontalList 文档](../HorizontalList/doc.md)

## 平台差异

### Taro/Web 平台
- 使用 CSS 类名控制样式
- 使用 box-shadow 实现阴影效果
- 支持自定义 className 属性

### React Native 平台
- 使用 StyleSheet 控制样式
- 使用 shadow 相关属性和 elevation 实现阴影效果
- 使用 containerStyle 替代 className

## 更新日志

### 1.0.0
- 首次发布
- 集成 ShopHeader 和 HorizontalList 组件
- 支持卡片样式自定义
- 支持Taro/RN跨平台 