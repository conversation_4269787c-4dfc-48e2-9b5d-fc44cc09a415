import { useEffect, useRef, CSSProperties, forwardRef, useCallback, useMemo } from 'react';
import { View } from '@tarojs/components';
import withClassName from '../utils/withClassName.rn';
import { AnimatedValue } from './AnimatedValue';

type AnimatedStyle = {
  [key: string]: number | string | AnimatedValue | AnimatedStyle | undefined;
};

// 将自定义的动画值映射到 Web 的 CSS 属性
const mapAnimatedStyle = (style: AnimatedStyle | null | undefined): CSSProperties => {
  if (!style) return {};

  const result: CSSProperties = {};
  
  // 处理透明度
  if (style.opacity && style.opacity instanceof AnimatedValue) {
    result.opacity = style.opacity.getValue();
  } else if (style.opacity !== undefined) {
    result.opacity = style.opacity as number;
  }

  // 处理变换
  const transform: string[] = [];
  
  if (style.transform && Array.isArray(style.transform)) {
    style.transform.forEach((t: any) => {
      if (!t) return;
      
      Object.entries(t).forEach(([key, value]) => {
        if (value instanceof AnimatedValue) {
          const val = value.getValue();
          switch (key) {
            case 'translateX': transform.push(`translateX(${val}px)`); break;
            case 'translateY': transform.push(`translateY(${val}px)`); break;
            case 'scale': transform.push(`scale(${val})`); break;
            case 'scaleX': transform.push(`scaleX(${val})`); break;
            case 'scaleY': transform.push(`scaleY(${val})`); break;
            case 'rotate': transform.push(`rotate(${val}deg)`); break;
            case 'rotateX': transform.push(`rotateX(${val}deg)`); break;
            case 'rotateY': transform.push(`rotateY(${val}deg)`); break;
            case 'rotateZ': transform.push(`rotateZ(${val}deg)`); break;
            case 'perspective': transform.push(`perspective(${val}px)`); break;
            default: break;
          }
        } else if (typeof value === 'number' || typeof value === 'string') {
          // 静态值
          switch (key) {
            case 'translateX': transform.push(`translateX(${value}px)`); break;
            case 'translateY': transform.push(`translateY(${value}px)`); break;
            case 'scale': transform.push(`scale(${value})`); break;
            case 'scaleX': transform.push(`scaleX(${value})`); break;
            case 'scaleY': transform.push(`scaleY(${value})`); break;
            case 'rotate': transform.push(`rotate(${value}deg)`); break;
            case 'rotateX': transform.push(`rotateX(${value}deg)`); break;
            case 'rotateY': transform.push(`rotateY(${value}deg)`); break;
            case 'rotateZ': transform.push(`rotateZ(${value}deg)`); break;
            case 'perspective': transform.push(`perspective(${value}px)`); break;
            default: break;
          }
        }
      });
    });
  }

  if (transform.length > 0) {
    result.transform = transform.join(' ');
  }

  // 合并其他样式
  Object.entries(style).forEach(([key, value]) => {
    if (key !== 'transform' && !(value instanceof AnimatedValue)) {
      (result as any)[key] = value;
    }
  });

  return result;
};

type AnimatedViewProps = {
  style?: AnimatedStyle;
  children?: React.ReactNode;
  [key: string]: any;
};

const AnimatedView = forwardRef<HTMLDivElement, AnimatedViewProps>((props) => {
  const { style, ...restProps } = props;
  const viewRef = useRef<HTMLDivElement>(null);
  const animationFrame = useRef<number>();
  
  // 收集所有动画值
  const collectAnimatedValues = useCallback((styleObj: any): AnimatedValue[] => {
    if (!styleObj) return [];
    
    const values: AnimatedValue[] = [];
    
    // 检查当前层级的 AnimatedValue
    Object.values(styleObj).forEach(value => {
      if (value instanceof AnimatedValue) {
        values.push(value);
      } else if (Array.isArray(value)) {
        // 处理 transform 数组
        value.forEach(item => {
          if (item && typeof item === 'object') {
            Object.values(item).forEach(v => {
              if (v instanceof AnimatedValue) {
                values.push(v);
              }
            });
          }
        });
      }
    });
    
    return values;
  }, []);
  
  // 监听动画值变化
  useEffect(() => {
    if (!viewRef.current) return;
    
    const animatedValues = collectAnimatedValues(style);
    if (animatedValues.length === 0) return;
    
    const updateStyle = () => {
      if (viewRef.current) {
        const webStyle = mapAnimatedStyle(style);
        Object.assign(viewRef.current.style, webStyle);
      }
      animationFrame.current = requestAnimationFrame(updateStyle);
    };
    
    updateStyle();
    
    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
      }
    };
  }, [style, collectAnimatedValues]);
  
  // 初始样式
  const initialStyle = useMemo(() => {
    return mapAnimatedStyle(style);
  }, []); // 只在初始渲染时计算一次

  return <View ref={viewRef} style={initialStyle} {...restProps} />;
});

// 导出 Animated 对象，提供与 React Native Animated 相似的 API
export const Animated = {
  View: withClassName()(AnimatedView),
  Value: AnimatedValue,
  timing: (
    value: AnimatedValue,
    config: { toValue: number; duration: number; useNativeDriver?: boolean }
  ) => {
    return {
      start: (callback?: () => void) => {
        const startValue = value.getValue();
        const diff = config.toValue - startValue;
        const startTime = Date.now();
        
        const animate = () => {
          const now = Date.now();
          const progress = Math.min(1, (now - startTime) / config.duration);
          
          // 使用缓动函数（这里使用线性）
          const currentValue = startValue + diff * progress;
          value.setValue(currentValue);
          
          if (progress < 1) {
            requestAnimationFrame(animate);
          } else if (callback) {
            callback();
          }
        };
        
        requestAnimationFrame(animate);
      },
    };
  },
};

export default Animated.View;