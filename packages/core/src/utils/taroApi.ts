
// 直接导入原始 Taro
import OriginTaro from '@tarojs/taro';

// 手动绑定方法
const getCurrentInstance = OriginTaro.getCurrentInstance?.bind(OriginTaro);
const getCurrentPages = OriginTaro.getCurrentPages?.bind(OriginTaro);
const useDidShow = OriginTaro.useDidShow;
const navigateTo = OriginTaro.navigateTo?.bind(OriginTaro);
const redirectTo = OriginTaro.redirectTo?.bind(OriginTaro);
const navigateBack = OriginTaro.navigateBack?.bind(OriginTaro);
const switchTab = OriginTaro.switchTab?.bind(OriginTaro);
const reLaunch = OriginTaro.reLaunch?.bind(OriginTaro);
const showToast = OriginTaro.showToast?.bind(OriginTaro);
const showLoading = OriginTaro.showLoading?.bind(OriginTaro);
const hideLoading = OriginTaro.hideLoading?.bind(OriginTaro);
const showModal = OriginTaro.showModal?.bind(OriginTaro);
const getSystemInfoSync = OriginTaro.getSystemInfoSync?.bind(OriginTaro);
const setStorageSync = OriginTaro.setStorageSync?.bind(OriginTaro);
const getStorageSync = OriginTaro.getStorageSync?.bind(OriginTaro);
const removeStorageSync = OriginTaro.removeStorageSync?.bind(OriginTaro);
const clearStorageSync = OriginTaro.clearStorageSync?.bind(OriginTaro);
const request = OriginTaro.request?.bind(OriginTaro);
const eventCenter = OriginTaro.eventCenter;
const nextTick = OriginTaro.nextTick?.bind(OriginTaro);

// 导出这些方法
export {
  getCurrentInstance,
  getCurrentPages,
  useDidShow,
  navigateTo,
  redirectTo,
  navigateBack,
  switchTab,
  reLaunch,
  showToast,
  showLoading,
  hideLoading,
  showModal,
  getSystemInfoSync,
  setStorageSync,
  getStorageSync,
  removeStorageSync,
  clearStorageSync,
  request,
  eventCenter,
  nextTick
};

export const taroEnv = OriginTaro.getEnv()

export const hideKeyboard = () => {
  OriginTaro.hideKeyboard()
}

// 创建增强版 Taro
const Taro = {
  ...OriginTaro,
  getCurrentInstance,
  getCurrentPages,
  useDidShow,
  navigateTo,
  redirectTo,
  navigateBack,
  switchTab,
  reLaunch,
  showToast,
  showLoading,
  hideLoading,
  showModal,
  getSystemInfoSync,
  setStorageSync,
  getStorageSync,
  removeStorageSync,
  clearStorageSync,
  request,
  eventCenter,
  nextTick,
  taroEnv
};

// 导出增强版 Taro
export default Taro;