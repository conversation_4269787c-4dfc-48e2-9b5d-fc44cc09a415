import React from 'react';
import View from '../../../components/basic/View';
import ProductCard from './productCard';
import { TagProps } from '../../ShopImageTag/types';

/**
 * ProductCard组件示例
 * 展示了不同配置下的商品卡片效果
 */
const Demo: React.FC = () => {
  // 定义图片标签
  const imageTag: TagProps[] = [
    { text: "促销", position: "topLeft", backgroundColor: "#FF5B00" }
  ];

  return (
    <View style={{ flex: 1, padding: 100, backgroundColor: '#f5f5f5' }}>
      {/* 基础商品卡片：门店名称+商品名称+价格 */}
      <View style={{ marginBottom: 20 }}>
        <ProductCard
          shopName="优选门店"
          productName="镇店豪华6人餐"
          imageSrc="https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png"
          imageSize={142}
          imageTag={imageTag}
          currentPrice={240}
          originalPrice={1080}
          onClick={() => { console.log('点击了商品卡片1') }}
        />
      </View>

      {/* 折扣价格背景样式 */}
      <View style={{ marginBottom: 20 }}>
        <ProductCard
          shopName="精品门店"
          productName="超值家庭套餐"
          imageSrc="https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png"
          imageSize={142}
          imageTag={imageTag}
          currentPrice={24}
          originalPrice={108}
          priceType="discount"
          discountBackgroundImageSrc="https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png"
          discountBackgroundImageWidth={130}
          discountBackgroundImageHeight={40}
          currentPriceColor="#FFFFFF"
          currentPriceFontSize={22}
          originalPriceColor="#FF5252"
          originalPriceFontSize={16}
        />
      </View>

      {/* 自定义样式的商品卡片 */}
      <View style={{ marginBottom: 20 }}>
        <ProductCard
          style={{ borderRadius: 8, backgroundColor: '#fff', padding: 8 }}
          shopName="品质门店"
          shopNameTag={{ text: "品牌", backgroundColor: "#007AFF" }}
          productName="限时特价豪华双人餐"
          productNameLines={1}
          productNameEllipsis="fade"
          imageSrc="https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png"
          imageSize={142}
          imageTag={imageTag}
          currentPrice={240}
          originalPrice={1080}
          currentPriceColor="#FF0000"
        />
      </View>
    </View>
  );
};

export default Demo;
