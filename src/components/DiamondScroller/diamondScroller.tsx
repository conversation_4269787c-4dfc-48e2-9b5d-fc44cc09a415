import React, { useMemo, useState, useEffect, useRef } from 'react';
import ScrollView from '../../components/basic/ScrollView';
import View from '../../components/basic/View';
import { Diamond } from '../Diamond';
import { DiamondItem, RNDiamondScrollerProps } from './types';
import { splitItemsByRows } from './core';
import './diamondScroller.scss';

/**
 * Diamond滚动分类导航组件 - 支持横向滚动和一行/两行切换
 */
const DiamondScroller: React.FC<RNDiamondScrollerProps> = (props) => {
  const { 
    items, 
    selectedId, 
    multiple = false, 
    itemWidth = 80, 
    imageSize = 60,
    rows = 1,
    style,
    diamondStyle,
    onItemClick,
    showIndicator = true
  } = props;

  // 内部选中状态管理
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  // 滚动指示器状态 - 使用浮点数以支持平滑过渡
  const [scrollPosition, setScrollPosition] = useState(0);
  // 滚动视图引用
  const scrollViewRef = useRef<any>(null);
  // 每页可见项目数
  const [visibleItemsCount, setVisibleItemsCount] = useState(4);
  // 计算指示器数量
  const indicatorCount = Math.ceil(items.length / visibleItemsCount) || 1;

  // 同步外部selectedId到内部状态
  useEffect(() => {
    if (selectedId) {
      setSelectedIds(multiple ? 
        (selectedId.split(',') || []) : 
        [selectedId]);
    } else {
      setSelectedIds([]);
    }
  }, [selectedId, multiple]);

  // 根据设备宽度估计可见项目数量
  useEffect(() => {
    // 估算屏幕上可见的项目数量，默认为4个
    // 实际应用中可能需要根据屏幕宽度动态计算
    const count = Math.floor(360 / (itemWidth + 12)); // 假设屏幕宽度为360dp，加上内边距
    setVisibleItemsCount(count > 0 ? count : 1);
  }, [itemWidth]);

  // 处理滚动事件，更新当前激活的指示器
  const handleScroll = (event) => {
    try {
      // 先检查事件对象结构
      const scrollInfo = event.detail || event.nativeEvent || event.target || {};
      const scrollLeft = scrollInfo.scrollLeft || (scrollInfo.contentOffset && scrollInfo.contentOffset.x) || 0;
      const scrollWidth = scrollInfo.scrollWidth || (scrollInfo.contentSize && scrollInfo.contentSize.width) || 0;
      const clientWidth = scrollInfo.clientWidth || (scrollInfo.layoutMeasurement && scrollInfo.layoutMeasurement.width) || 0;
      
      // 计算当前滑动的百分比位置（浮点数）
      if (scrollWidth > clientWidth) {
        const maxScroll = scrollWidth - clientWidth;
        const position = maxScroll > 0 ? scrollLeft / maxScroll : 0;
        // 使用浮点数保存精确位置，范围从0到1
        setScrollPosition(Math.min(Math.max(position, 0), 1));
      }
    } catch (error) {
      console.log('Scroll event error:', error);
    }
  };

  // 点击指示器项，滚动到对应位置
  const scrollToIndicator = (index) => {
    if (scrollViewRef.current) {
      try {
        // 计算大约需要滚动到的位置
        const position = index / (indicatorCount - 1 || 1);
        
        // 先尝试Taro的ScrollView方式滚动
        if (typeof scrollViewRef.current.scrollTo === 'function') {
          // Taro或原生的scrollTo方法
          try {
            // 先尝试访问属性
            const scrollEl = scrollViewRef.current;
            const scrollWidth = scrollEl.scrollWidth || 0;
            const clientWidth = scrollEl.clientWidth || 0;
            
            if (scrollWidth > clientWidth) {
              const maxScroll = scrollWidth - clientWidth;
              scrollViewRef.current.scrollTo({
                left: position * maxScroll,
                behavior: 'smooth'
              });
            }
          } catch (e) {
            // 如果上面的方式失败，使用比例滚动
            const scrollDistance = position * (items.length * itemWidth * 1.2);
            scrollViewRef.current.scrollTo({ x: scrollDistance, animated: true });
          }
        } else if (scrollViewRef.current.scrollLeft !== undefined) {
          // DOM元素方式
          const scrollEl = scrollViewRef.current;
          const maxScroll = scrollEl.scrollWidth - scrollEl.clientWidth;
          scrollEl.scrollLeft = position * maxScroll;
        } else {
          // 其他情况，使用比例滚动
          setScrollPosition(position);
          // 使用估计值计算滚动距离
          const scrollDistance = position * (items.length * itemWidth * 1.2);
          
          if (typeof scrollViewRef.current.scrollToOffset === 'function') {
            scrollViewRef.current.scrollToOffset({ offset: scrollDistance, animated: true });
          } else if (typeof scrollViewRef.current.scrollTo === 'function') {
            scrollViewRef.current.scrollTo({ x: scrollDistance, animated: true });
          }
        }
        
        // 无论如何，更新滑动位置状态
        setScrollPosition(position);
      } catch (error) {
        console.log('Scroll to indicator error:', error);
      }
    }
  };

  // 点击处理函数
  const handleItemClick = (item: DiamondItem, index: number) => {
    if (item.disabled) return;
    
    let newSelectedIds: string[] = [];
    const isSelected = selectedIds.includes(item.id);
    
    if (multiple) {
      // 多选模式
      if (isSelected) {
        // 如果已选中，则取消选中
        newSelectedIds = selectedIds.filter(id => id !== item.id);
      } else {
        // 如果未选中，则添加选中
        newSelectedIds = [...selectedIds, item.id];
      }
    } else {
      // 单选模式
      newSelectedIds = isSelected ? [] : [item.id];
    }
    
    setSelectedIds(newSelectedIds);
    onItemClick?.(item, index, !isSelected);
  };

  // 计算行数据 - 根据项目数量自动调整布局
  const rowsData = useMemo(() => {
    // 使用core.ts中的分组逻辑，传入rows作为强制布局模式（如果指定了）
    return splitItemsByRows(items, rows);
  }, [items, rows]);

  // 构建容器类名
  const containerClassName = 'diamond-scroller';
  const rowClassName = 'diamond-scroller__row';
  
  return (
    <View style={style} className={containerClassName}>
      <ScrollView
        ref={scrollViewRef}
        scrollX
        className={`${rowClassName} diamond-scroller__scroll-view`}
        onScroll={handleScroll}
      >
        <View className='diamond-scroller__content'>
          {rowsData.map((rowItems, rowIndex) => (
            <View key={`diamond-row-${rowIndex}`} className='diamond-scroller__row-container'>
              {rowItems.map((item, index) => (
                <View 
                  key={item.id} 
                  className='diamond-scroller__item-wrapper'
                  onClick={() => handleItemClick(item, index)}
                >
                  <Diamond
                    width={itemWidth}
                    imageSize={imageSize}
                    imageSrc={item.imageSrc}
                    imageSelectedSrc={item.imageSelectedSrc}
                    text={item.text}
                    selected={selectedIds.includes(item.id)}
                    disabled={item.disabled}
                    style={diamondStyle}
                    imageSelectedBorder={item.imageSelectedBorder}
                    imageSelectedBorderColor={item.imageSelectedBorderColor}
                    imageSelectedBorderRadius={item.imageSelectedBorderRadius}
                    textColor={item.textColor}
                    textSelectedColor={item.textSelectedColor}
                    textHeight={item.textHeight}
                    textFontSize={item.textFontSize}
                    textSelectedFontSize={item.textSelectedFontSize}
                    textSelectedBackgroundColor={item.textSelectedBackgroundColor}
                    textSelectedBackgroundRadius={item.textSelectedBackgroundRadius}
                  />
                </View>
              ))}
            </View>
          ))}
        </View>
      </ScrollView>

      {/* 分页指示器 - 平滑过渡版本 */}
      {showIndicator && indicatorCount > 1 && (
        <View className='diamond-scroller__indicator-container'>
          {/* 背景指示器点 */}
          {Array.from({ length: indicatorCount }).map((_, index) => (
            <View
              key={`indicator-${index}`}
              className='diamond-scroller__indicator'
              onClick={() => scrollToIndicator(index)}
            />
          ))}
          
          {/* 激活指示器 - 使用transform实现平滑过渡 */}
          <View 
            className='diamond-scroller__indicator-active'
            style={{
              left: `calc(${Math.min(Math.max(scrollPosition * (indicatorCount - 1), 0), indicatorCount - 1) * 12}px + ${6 + Math.min(Math.max(scrollPosition * (indicatorCount - 1), 0), indicatorCount - 1) * 6}px)`
            }}
          />
        </View>
      )}
    </View>
  );
};

// 样式已移至diamondScroller.scss文件中

export default DiamondScroller;
