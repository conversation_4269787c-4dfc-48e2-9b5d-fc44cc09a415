import React from 'react';
import * as R from 'ramda';
import { View } from '../../../../core/src/components/basic/View';
import { ShopImageTag } from '../../../../core/src/components/display/ShopImageTag';
import { Title } from '../../../../core/src/components/display/title';
import { Price } from '../../../../core/src/components/display/price';
import { OneLineTwoBizProps } from './types';
import './oneLineTwoBiz.scss';

/**
 * 一行两业务卡片组件
 * 展示产品信息，包括图片、名称、价格、距离、月销量和折扣信息
 * 
 * @param {object} props - 组件属性
 * @param {string} [props.className] - 自定义类名
 * @param {CSSProperties} [props.style] - 自定义样式
 * @param {function} [props.onClick] - 点击卡片时的回调函数
 * 
 * @param {string} props.imageSrc - 商品图片链接
 * @param {number} [props.imageSize=142] - 商品图片大小，单位px
 * @param {TagProps|TagProps[]} [props.imageTag] - 商品图片标签
 * @param {string} [props.distance] - 距离信息
 * 
 * @param {string} props.productName - 产品名称
 * @param {number} [props.productNameFontSize] - 产品名称u5b57u4f53u5927u5c0f
 * @param {number} [props.productNameLines=1] - 产品名称u663eu793au884cu6570
 * @param {'ellipsis'|'fade'} [props.productNameEllipsis='ellipsis'] - 产品名称u8d85u51fau663eu793au65b9u5f0f
 * @param {TagProps|TagProps[]} [props.productNameTag] - 产品名称u6807u7b7e
 * @param {CSSProperties} [props.productNameStyle] - 产品名称u6837u5f0f
 * 
 * @param {number} props.currentPrice - 当前价格
 * @param {number} [props.originalPrice] - 原价
 * @param {'default'|'discount'} [props.priceType='default'] - 价格类型
 * @param {string} [props.currentPriceColor] - 当前价格u989cu8272
 * @param {string} [props.originalPriceColor] - 原价u989cu8272
 * @param {number} [props.currentPriceFontSize] - 当前价格u5b57u4f53u5927u5c0f
 * @param {number} [props.originalPriceFontSize] - 原价u5b57u4f53u5927u5c0f
 * 
 * @param {number|string} [props.monthlySales] - u6708u9500u91cf
 * @param {string} [props.monthlySalesLabel='月售'] - 月销量标签文本
 * 
 * @param {string} [props.discountLabel] - 折扣标签，例如"3.5折热销中"
 * @param {string} [props.discountLabelBgColor='#FF1530'] - 折扣标签背景颜色
 */
const OneLineTwoBiz: React.FC<OneLineTwoBizProps> = (props) => {
  const {
    // 整体商卡参数
    className,
    style,
    onClick,

    // 商品图片参数
    imageSrc,
    imageSize = 142,
    imageTag,

    // 产品名称u53c2u6570
    productName,
    productNameFontSize = 14,
    productNameLines = 1,
    productNameEllipsis = 'ellipsis',
    productNameTag,
    productNameStyle,

    // 商品价格参数
    currentPrice,
    originalPrice,
    priceType = 'default',
    currentPriceColor = '#FF1530',
    originalPriceColor = '#999999',
    currentPriceFontSize = 20,
    originalPriceFontSize = 14,
    currencySymbol = '¥',
    currentPriceSymbolFontSize,
    originalPriceSymbolFontSize,
    currentPriceDecimalFontSize,
    originalPriceDecimalFontSize,
    strikethrough = true,

    // 价格折扣信息
    discountPrefixIcon,
    discountText,

    // 销量信息
    monthlySales,
    monthlySalesLabel = '月售',

  } = props;

  // 构建类名
  const rootClassName = [
    'one-line-two-biz',
    className
  ].filter(Boolean).join(' ');

  // 处理点击事件
  const handleClick = R.defaultTo(() => { }, onClick);

  // 处理标签数据，确保是数组类型
  const formatTags = R.ifElse(
    R.isNil,
    R.always([]),
    R.ifElse(
      Array.isArray,
      R.identity,
      R.of
    )
  );

  // 渲染视图
  return (
    <View
      className={rootClassName}
      style={style}
      onClick={handleClick}
    >
      <View className="one-line-two-biz__image-container">
        {/* 商品图 */}
        <ShopImageTag
          src={imageSrc}
          size={imageSize}
          tag={imageTag}
        />
      </View>

      <View className="one-line-two-biz__content">
        {/* 产品名称 */}
        {productName && (
          <View className="one-line-two-biz__title-container">
            <Title
              fontSize={productNameFontSize}
              lines={productNameLines}
              ellipsis={productNameEllipsis}
              tags={formatTags(productNameTag)}
              style={productNameStyle}
            >
              {productName}
            </Title>
          </View>
        )}

        {/* 价格和销量信息 */}
        <View className="one-line-two-biz__price-sales-container">
          {/* 价格信息 */}
          <View className="one-line-two-biz__price-container">
            <Price
              currentPrice={currentPrice}
              originalPrice={originalPrice}
              type={priceType}
              currentPriceColor={currentPriceColor}
              originalPriceColor={originalPriceColor}
              currentPriceFontSize={currentPriceFontSize}
              originalPriceFontSize={originalPriceFontSize}
              currencySymbol={currencySymbol}
              currentPriceSymbolFontSize={currentPriceSymbolFontSize}
              originalPriceSymbolFontSize={originalPriceSymbolFontSize}
              currentPriceDecimalFontSize={currentPriceDecimalFontSize}
              originalPriceDecimalFontSize={originalPriceDecimalFontSize}
              strikethrough={strikethrough}
            />
          </View>

          {/* 月销量信息 */}
          {monthlySales && (
            <View className="one-line-two-biz__monthly-sales">
              {monthlySalesLabel}{monthlySales}
            </View>
          )}
        </View>
      </View>
    </View>
  );
};

export default OneLineTwoBiz;
