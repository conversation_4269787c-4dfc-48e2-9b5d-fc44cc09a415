import React from 'react';
import View from '../../basic/View';
import Text from '../../basic/Text';
import { Price } from './index';

export default function PriceDemo() {
  return (
    <View style={{ background: '#f5f5f5', minHeight: '100vh', padding: '16px' }}>
      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>默认样式 - 红色当前价格和灰色原价</Text>
        <Price 
          currentPrice={240} 
          originalPrice={1080} 
          type="default"
          priceTagWidth={90}
        />
      </View>

      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>默认样式 - 仅显示当前价格</Text>
        <Price 
          currentPrice={240} 
          type="default"
        />
      </View>

      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8 }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>默认样式 - 自定义颜色和字体大小</Text>
        <Price 
          currentPrice={240} 
          originalPrice={1080} 
          type="default"
          currentPriceColor="#0066cc"
          currentPriceFontSize={30}
          currentPriceSymbolFontSize={20}
          originalPriceColor="#666666"
          originalPriceFontSize={16}
        />
      </View>

      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8, alignItems: 'flex-start' }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>促销样式 - 背景图</Text>
        <Price 
          currentPrice={39} 
          originalPrice={118} 
          type="discount"
          discountBackgroundImageSrc="https://img30.360buyimg.com/img/jfs/t1/301190/25/8889/652/682ee8f7F17feb63d/c33d3db673a40a68.png"
          discountBackgroundImageWidth={130} 
          discountBackgroundImageHeight={40} 
          currentPriceColor="#FF5252" 
          currentPriceFontSize={22} 
          currentPriceSymbolFontSize={14}
          originalPriceColor="#0066cc"
          originalPriceFontSize={16}
          originalPriceSymbolFontSize={12}
          discountCurrentPriceLeftOffset={15}
          discountOriginalPriceRightOffset={15}
        />
      </View>

      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8, alignItems: 'flex-start' }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>促销样式 - 无原价（使用背景图）</Text>
        <Price 
          currentPrice={199} 
          type="discount"
          discountBackgroundImageSrc="https://img30.360buyimg.com/img/jfs/t1/301190/25/8889/652/682ee8f7F17feb63d/c33d3db673a40a68.png"
          discountBackgroundImageWidth={100} 
          discountBackgroundImageHeight={35}
          currentPriceColor="#FF5252"
          currentPriceFontSize={20}
          currentPriceSymbolFontSize={14}
          discountCurrentPriceLeftOffset={30}
        />
      </View>

      <View style={{ marginBottom: 20, background: '#fff', padding: 16, borderRadius: 8, alignItems: 'flex-start' }}>
        <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 10, color: '#333' }}>促销样式 - 自定义前景颜色（使用背景图）</Text>
        <Price 
          currentPrice={88} 
          originalPrice={288} 
          type="discount"
          discountBackgroundImageSrc="https://img30.360buyimg.com/img/jfs/t1/301190/25/8889/652/682ee8f7F17feb63d/c33d3db673a40a68.png"
          discountBackgroundImageWidth={130}
          discountBackgroundImageHeight={40}
          currentPriceColor="#FF5252" 
          originalPriceColor="#0066cc"
          currentPriceFontSize={22}
          originalPriceFontSize={16}
          discountCurrentPriceLeftOffset={15}
          discountOriginalPriceRightOffset={15}
        />
      </View>
    </View>
  );
} 