import React, { useState } from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { <PERSON>Scroller } from './index';

/**
 * DiamondScroller u7ec4u4ef6React Nativeu793au4f8b
 */
const DiamondScrollerDemo: React.FC = () => {
  const [selectedId, setSelectedId] = useState<string>('');
  
  // u793au4f8bu6570u636e
  const items = [
    {
      id: '1',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: 'u9644u8fd1u7f8eu98df',
    },
    {
      id: '2',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: 'u4f11u95f2u73a9u4e50',
    },
    {
      id: '3',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: 'u4e3du4ebau7f8eu53d1',
    },
    {
      id: '4',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: 'u533bu7597u533bu7f8e',
    },
    {
      id: '5',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: 'u8fd0u52a8u5065u8eab',
    },
    {
      id: '6',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: 'u996eu54c1u751cu70b9',
    },
    {
      id: '7',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: 'u8db3u7597u6309u6469',
    },
    {
      id: '8',
      imageSrc: 'https://img11.360buyimg.com/imagetools/jfs/t1/314307/24/1416/73836/68270333F23385c16/f6f8779366dec7f4.png',
      text: 'u6d17u6d74u6c57u84b8',
    }
  ];

  // u70b9u51fbu5904u7406u51fdu6570
  const handleItemClick = (item, index, selected) => {
    console.log('u70b9u51fbu4e86', item.text, 'u7d22u5f15:', index, 'u9009u4e2du72b6u6001:', selected);
    setSelectedId(selected ? item.id : '');
  };

  return (
    <View style={styles.container}>
      <View style={styles.section}>
        <Text style={styles.title}>u5355u884cu6a21u5f0f</Text>
        <DiamondScroller 
          items={items} 
          selectedId={selectedId}
          rows={1}
          onItemClick={handleItemClick}
        />
      </View>
      
      <View style={styles.section}>
        <Text style={styles.title}>u53ccu884cu6a21u5f0f</Text>
        <DiamondScroller 
          items={items} 
          selectedId={selectedId}
          rows={2}
          onItemClick={handleItemClick}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingTop: 10,
    paddingBottom: 10,
  },
  section: {
    marginBottom: 20,
  },
  title: {
    marginBottom: 10,
    paddingLeft: 10,
    fontWeight: 'bold',
  }
});

export default DiamondScrollerDemo;
