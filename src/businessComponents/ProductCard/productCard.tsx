import React from 'react';
import View from '../../components/basic/View';
import Text from '../../components/basic/Text';
import Image from '../../components/basic/Image';
import { Price } from '../../components/display/price';
import { ProductCardProps } from './types';
import { styles } from './style';
import './productCard.scss';

const ProductCard: React.FC<ProductCardProps> = ({
  imageUrl,
  imageWidth = 200,
  imageHeight = 200,
  imageRadius = 8,
  title,
  description,
  currentPrice,
  originalPrice,
  priceType = 'discount',
  priceProps = {},
  titleColor = '#333333',
  descriptionColor = '#666666',
  titleFontSize = 16,
  descriptionFontSize = 14,
  className,
  style,
  containerStyle,
  imageStyle,
  onClick,
}) => {
  // 构建类名
  const rootClassName = [
    'product-card',
    className
  ].filter(Boolean).join(' ');

  // 构建样式
  const rootStyle = {
    ...containerStyle,
    ...style,
  };

  // 点击处理函数
  const handleClick = () => {
    onClick && onClick();
  };

  return (
    <View 
      className={rootClassName} 
      style={rootStyle}
      onClick={handleClick}
    >
      <View className="product-card__image-container">
        <Image
          className="product-card__image"
          src={imageUrl}
          width={imageWidth}
          height={imageHeight}
          borderRadius={imageRadius}
          style={imageStyle}
        />
      </View>
      
      <View className="product-card__content">
        <Text 
          className="product-card__title" 
          style={{ 
            color: titleColor,
            fontSize: titleFontSize
          }}
        >
          {title}
        </Text>
        
        {description && (
          <Text 
            className="product-card__description" 
            style={{ 
              color: descriptionColor,
              fontSize: descriptionFontSize
            }}
          >
            {description}
          </Text>
        )}
        
        <View className="product-card__price">
          <Price
            currentPrice={currentPrice}
            originalPrice={originalPrice}
            type={priceType}
            {...priceProps}
          />
        </View>
      </View>
    </View>
  );
};

export default ProductCard; 