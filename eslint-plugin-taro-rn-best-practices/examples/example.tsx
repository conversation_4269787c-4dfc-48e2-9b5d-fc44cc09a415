import React from 'react';
import { View, Text, Image, StyleSheet } from 'react-native';

// 错误示例
const BadExample = () => {
  return (
    <View>
      Hello World {/* 错误: 文本应该在 Text 组件中 */}
      
      <View style={{ position: 'fixed' }}> {/* 错误: position: fixed 不支持 */}
        <Image source={{ uri: 'https://example.com/image.png' }} /> {/* 错误: 没有指定尺寸 */}
      </View>
      
      <Text style={{ color: 'red' }}>
        Parent text
        <Text>Child text</Text> {/* 错误: 样式不会继承 */}
      </Text>
      
      <View style={{ animation: '1s ease' }} /> {/* 错误: 不支持的样式属性 */}
      
      <View style={{ display: 'flex' }} /> {/* 警告: 未明确指定 flexDirection */}
    </View>
  );
};

// 正确示例
const GoodExample = () => {
  return (
    <View>
      <Text>Hello World</Text>
      
      <View style={styles.container}>
        <Image 
          source={{ uri: 'https://example.com/image.png' }}
          style={styles.image}
        />
      </View>
      
      <Text style={styles.parentText}>
        Parent text
        <Text style={styles.childText}>Child text</Text>
      </Text>
      
      <View style={styles.flexContainer} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  image: {
    width: 100,
    height: 100,
  },
  parentText: {
    color: 'red',
  },
  childText: {
    color: 'red',
  },
  flexContainer: {
    display: 'flex',
    flexDirection: 'column',
  },
});

export default GoodExample;