
import { View, Text, Image } from "@/BaseComponents/atoms";

import styles from "./index.module.scss";

const Loading = (props: any) => {
  const { text = "加载中", isLoading = true } = props || {};
  return (
    <View className={styles.bottom}>
      {isLoading == true ? (
        <Image
          style={{ width: 24, height: 24, marginRight: 5 }}
          src="https://img11.360buyimg.com/imagetools/jfs/t1/238167/1/4795/20667/6569adb3Fbc6fc5c7/bb4ff583963e202b.gif"
        ></Image>
      ) : (
        <Image src="https://storage.360buyimg.com/home/<USER>/activity/channel/loading.png?Expires=3856425071&AccessKey=YJmfJbFn0QMHPQTc&Signature=8Nqk5hb0YpXQWNMh7LwpuOqpmC0%3D"></Image>
      )}
      <Text style={styles.bottomText}>{text}</Text>
    </View>
  );
};

export default Loading;