# Exposure 组件说明文档

## 组件概述

本目录下的 `Exposure` 组件用于在 React Native 应用中精准检测元素在多层嵌套滚动容器中的曝光（可见性）状态。支持任意层级容器嵌套，支持可见性偏移修正，适用于复杂页面曝光统计、广告曝光、内容运营等场景。

---

## 主要导出

- `TrackedScrollView`：增强型 ScrollView，自动追踪滚动和容器布局
- `TrackedFlatList`：增强型 FlatList，自动追踪滚动和容器布局
- `ExposureTracker`：曝光检测组件，包裹需要检测曝光的元素

---

## 使用方式

### 1. 基本用法
```tsx
import { TrackedScrollView, ExposureTracker } from '@/components/Exposure';

<TrackedScrollView>
  <ExposureTracker onExposure={() => { /* 曝光回调 */ }} onDisappear={() => { /* 消失回调 */ }}>
    <View>内容</View>
  </ExposureTracker>
</TrackedScrollView>
```

### 2. 支持嵌套容器
```tsx
<TrackedScrollView visibilityOffset={{ top: 80, bottom: 0, left: 0, right: 0 }}>
  <TrackedFlatList visibilityOffset={{ top: 20, bottom: 20, left: 0, right: 0 }}>
    <ExposureTracker
      visibilityOffset={{ top: 10, bottom: 10, left: 0, right: 0 }}
      onExposure={...}
      onDisappear={...}
    >
      <View>内容</View>
    </ExposureTracker>
  </TrackedFlatList>
</TrackedScrollView>
```

### 3. visibilityOffset 说明
- `visibilityOffset` 可在任意容器和 `ExposureTracker` 上设置，用于修正可见区域（如顶部导航栏、底部Tab、吸顶等遮挡）。
- 所有父容器和自身的 `visibilityOffset` 会自动累加，影响最终曝光判断。

| 属性   | 说明         | 类型   | 默认值 |
|--------|--------------|--------|--------|
| top    | 顶部偏移     | number | 0      |
| bottom | 底部偏移     | number | 0      |
| left   | 左侧偏移     | number | 0      |
| right  | 右侧偏移     | number | 0      |

---

## 关键实现说明

1. **多层嵌套支持**：通过 React Context 递归收集所有父容器的滚动偏移、布局信息、可见性偏移，实现任意层级的曝光计算。
2. **曝光判定**：只有当元素在屏幕和所有父容器的可见区域内均有露出时，才判定为曝光。
3. **性能优化**：滚动事件采用订阅-发布模式，减少无效重算。
4. **布局测量**：自动监听容器和屏幕尺寸变化，确保曝光计算准确。
5. **回调说明**：
   - `onExposure`：元素进入可见区域并满足曝光条件时触发
   - `onDisappear`：元素离开可见区域时触发
   - `exposureThreshold`：可选，曝光延迟判定阈值（毫秒）

---

## 注意事项
- 请务必用 `TrackedScrollView` / `TrackedFlatList` 替换原生组件，保证曝光链路完整。
- 容器和元素的 `visibilityOffset` 合理设置，确保曝光判定准确。
- 组件已兼容 RN 0.68+，如有兼容性问题请反馈。

---

## 典型场景举例
- Banner/广告曝光统计
- 商品卡片曝光埋点
- 复杂嵌套列表、Tab、吸顶场景下的曝光

---

## 联系与反馈
如有问题或建议，请联系前端基础团队。
