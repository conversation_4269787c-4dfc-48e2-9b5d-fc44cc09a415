# ProductCard 商品卡片

## 介绍

商品卡片组件，用于展示商品信息，包括商品图片、门店名称、商品名称和价格。该组件整合了ShopImageTag、Title和Price组件，适用于商品列表、推荐商品等场景。

## 引入

```js
import { ProductCard } from '@/uicomponents/src/components/display/ProductCard';
```

## 代码演示

### 基础用法

```jsx
<ProductCard
  shopName="优选门店"
  productName="商品名称"
  imageSrc="商品图片地址"
  currentPrice={240}
  originalPrice={1080}
/>
```

### 自定义图片大小

```jsx
<ProductCard
  shopName="优选门店"
  productName="商品名称"
  imageSrc="商品图片地址"
  currentPrice={88}
  imageSize={160}
/>
```

### 带标签的商品卡片

```jsx
<ProductCard
  shopName="优选门店"
  productName="商品名称"
  imageSrc="商品图片地址"
  currentPrice={299}
  originalPrice={599}
  imageTag={[{ text: "促销", position: "topLeft", backgroundColor: "#FF5B00" }]}
/>
```

### 折扣价格样式

```jsx
<ProductCard
  shopName="优选门店"
  productName="商品名称"
  imageSrc="商品图片地址"
  currentPrice={299}
  originalPrice={599}
  priceType="discount"
  discountBackgroundImageSrc="https://img10.360buyimg.com/img/jfs/t1/300408/6/8539/653/682d6fcfF88f3db14/49b29b72936379d2.png"
  discountBackgroundImageWidth={130}
  discountBackgroundImageHeight={40}
  currentPriceColor="#FFFFFF"
  currentPriceFontSize={22}
  originalPriceColor="#FF5252"
  originalPriceFontSize={16}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| className | 自定义类名 | `string` | - |
| style | 容器样式 | `CSSProperties` | - |
| onClick | 点击卡片回调 | `() => void` | - |
| imageSrc | 商品图片URL | `string` | - |
| imageSize | 图片大小 | `number` | `142` |
| imageTag | 图片标签 | `TagProps \| TagProps[]` | - |
| shopName | 门店名称 | `string` | - |
| shopNameFontSize | 门店名称字体大小 | `number` | - |
| shopNameLines | 门店名称显示行数 | `number` | `1` |
| shopNameEllipsis | 门店名称超出显示方式 | `'ellipsis' \| 'fade'` | `'ellipsis'` |
| shopNameTag | 门店名称标签 | `TagProps \| TagProps[]` | - |
| productName | 商品名称 | `string` | - |
| productNameFontSize | 商品名称字体大小 | `number` | - |
| productNameLines | 商品名称显示行数 | `number` | `2` |
| productNameEllipsis | 商品名称超出显示方式 | `'ellipsis' \| 'fade'` | `'ellipsis'` |
| productNameTag | 商品名称标签 | `TagProps \| TagProps[]` | - |
| currentPrice | 当前价格 | `number` | - |
| originalPrice | 原价格 | `number` | - |
| priceType | 价格类型 | `'default' \| 'discount'` | `'default'` |
| discountBackgroundImageSrc | 折扣背景图片URL | `string` | - |
| discountBackgroundImageWidth | 折扣背景图片宽度 | `number` | - |
| discountBackgroundImageHeight | 折扣背景图片高度 | `number` | - |
| currentPriceColor | 当前价格颜色 | `string` | - |
| originalPriceColor | 原价颜色 | `string` | - |
| currentPriceFontSize | 当前价格字体大小 | `number` | - |
| originalPriceFontSize | 原价字体大小 | `number` | - |

### TagProps属性

imageTags、shopNameTag和productNameTag属性都使用TagProps类型，支持以下属性：

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| text | 标签文本 | `string` | - |
| position | 标签位置 | `'topLeft' \| 'topRight' \| 'bottomLeft' \| 'bottomRight'` | `'topLeft'` |
| backgroundColor | 标签背景色 | `string` | `'#FF5B00'` |
| textColor | 标签文本颜色 | `string` | `'#FFFFFF'` |
