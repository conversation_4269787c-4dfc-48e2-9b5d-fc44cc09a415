import React from 'react';
import { Platform } from 'react-native';
import View from '../../basic/View';
import Text from '../../basic/Text';
import Image from '../../basic/Image';
import { BasePrice } from '../../basic/BasePrice';
import { CouponCardProps } from './types';
import './couponCard.scss';

const CouponCard: React.FC<CouponCardProps> = ({
  title="优惠券",
  amount,
  condition = '',
  currencySymbol = '¥',
  backgroundImage,
  titleColor = '#8F4100',
  amountColor = '#8F4100',
  conditionColor = '#8F4100',
  titleFontSize = 12,
  amountFontSize = 26,
  amountSymbolFontSize = 14,
  conditionFontSize = 10,
  width = 72,
  height = 110,
  onClick,
  className,
  style,
  containerStyle,
}) => {
  // 构建类名
  const rootClassName = [
    'coupon-card',
    className
  ].filter(Boolean).join(' ');

  // 构建样式
  const rootStyle = {
    width,
    height,
    ...containerStyle,
    ...style,
  };

  // 构建价格组件属性
  const priceProps = {
    price: amount,
    currencySymbol,
    decimalPlaces: 0 as 0,
    showDecimal: false,
    integerColor: amountColor,
    integerFontSize: amountFontSize,
    symbolColor: amountColor,
    symbolFontSize: amountSymbolFontSize,
    fontWeight: '400',
    lineHeight: amountFontSize,
    symbolLineHeight: amountSymbolFontSize,
    letterSpacing: 0,
  };

  return (
    <View className={rootClassName} style={rootStyle} onClick={onClick}>
      {/* 背景图片 */}
      {backgroundImage && (
        <Image 
          className="coupon-card__background" 
          src={backgroundImage}
          style={{
            width: width ? width : '100%',
            height: height ? height : '100%',
          }}
        />
      )}
      
      {/* 内容区域 */}
      <View className="coupon-card__content">
        {/* 标题 */}
        <Text 
          className={condition ? "coupon-card__title" : "coupon-card__title coupon-card__title--no-condition"}
          style={{ 
            fontSize: titleFontSize,
            color: titleColor,
            ...(Platform.OS === 'web' && condition ? { marginTop: 5} : {marginTop: 10})
          }}
          numberOfLines={1}
        >
          {title}
        </Text>
        
        {/* 价格 */}
        <View className="coupon-card__amount">
            <BasePrice {...priceProps} />
        </View>
        
        {/* 使用条件 */}
        {condition && (
          <Text 
            className="coupon-card__condition" 
            style={{
              fontSize: conditionFontSize,
              color: conditionColor
            }}
            numberOfLines={1}
          >
            {condition}
          </Text>
        )}
      </View>
    </View>
  );
};

export default CouponCard; 