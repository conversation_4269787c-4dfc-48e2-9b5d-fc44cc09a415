import React from 'react';
import { ViewStyle, TextStyle, ImageStyle } from 'react-native';
import { TitleProps } from '../../components/display/title/types';
import { TaroCustomTagProps, RNCustomTagProps } from '../../components/CustomTag';

// 内容类型定义
export enum ContentType {
  TITLE = 'title',
  TAG = 'tag'
}

// 基础内容接口
export interface BaseContent {
  /** 内容类型 */
  type: ContentType;
  /** 文本内容 */
  text: string;
  /** 自定义类名 */
  className?: string;
}

// 标题内容
export interface TitleContent extends BaseContent {
  type: ContentType.TITLE;
  /** 字体大小 */
  fontSize?: number;
  /** 文本对齐方式 */
  textAlign?: 'left' | 'center' | 'right';
  /** 行数限制 */
  lines?: number;
  /** 标签列表 */
  tags?: Omit<TagContent, 'type'>[];
  /** 样式对象 */
  style?: {
    /** 文本颜色 */
    color?: string;
    /** 字体权重 */
    fontWeight?: string | number;
    /** 字体族 */
    fontFamily?: string;
    /** 左边距 */
    marginLeft?: number;
    /** 其他样式属性 */
    [key: string]: any;
  };
  /** 其他Title组件属性 */
  props?: Omit<Partial<TitleProps>, 'fontSize' | 'style' | 'className' | 'textAlign' | 'lines' | 'tags'>;
}

// 标签内容
export interface TagContent extends BaseContent {
  type: ContentType.TAG;
  /** 标签背景色 */
  backgroundColor?: string;
  /** 是否显示边框 */
  showBorder?: boolean;
  /** 边框颜色 */
  borderColor?: string;
  /** 图标URL */
  icon?: string;
  /** 标签图片URL */
  tagImage?: string;
  /** 文本样式 */
  textStyle?: {
    /** 文本颜色 */
    color?: string;
    /** 字体大小 */
    fontSize?: number;
    /** 字体权重 */
    fontWeight?: string | number;
    /** 字体族 */
    fontFamily?: string;
    /** 其他文本样式属性 */
    [key: string]: any;
  };
  /** 容器样式 */
  style?: {
    /** 左边距 */
    marginLeft?: number;
    /** 其他样式属性 */
    [key: string]: any;
  };
  /** 其他标签组件属性 */
  props?: Omit<Partial<TaroCustomTagProps>, 'text' | 'style' | 'textStyle' | 'className' | 'backgroundColor' | 'showBorder' | 'borderColor' | 'icon' | 'tagImage'>;
}

// 统一内容类型
export type Content = TitleContent | TagContent | React.ReactNode;

// 行内容配置
export interface LineConfig {
  /** 左侧内容数组 */
  left?: Content[];
  /** 右侧内容数组 */
  right?: Content[];
  /** 行高 */
  height?: number;
}

export interface ShopHeaderProps {
  /** 店铺logo/图片地址 */
  imageUrl: string;
  /** 图片宽度 */
  imageWidth?: number;
  /** 图片高度 */
  imageHeight?: number;
  
  /** 信息行配置列表 */
  lines?: LineConfig[];
  
  /** 通用行间距 */
  lineSpacing?: number;
  /** 通用行高 */
  lineHeight?: number;
  
  /** 自定义根元素类名 (Taro/Web) */
  className?: string;
  /** 自定义根元素内联样式 (Taro/Web & RN) */
  style?: React.CSSProperties | ViewStyle;
  /** 自定义根元素样式 (RN) */
  containerStyle?: ViewStyle;
  /** 自定义图片样式 */
  imageStyle?: ImageStyle;
  /** 点击事件 */
  onClick?: () => void;
} 