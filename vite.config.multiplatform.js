import { resolve } from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';
import libCss from 'vite-plugin-libcss';

// 多端构建配置
export default function createMultiPlatformConfig(packageDir, packageJson) {
  const { name, dependencies, peerDependencies } = packageJson;

  // 外部依赖
  const external = [
    ...Object.keys(dependencies || {}),
    ...Object.keys(peerDependencies || {}),
    'react/jsx-runtime'
  ];

  // 平台特定的外部依赖
  const platformExternals = {
    rn: [...external, 'react-native'],
    h5: [...external, 'react-dom', '@tarojs/components', '@tarojs/taro'],
    weapp: [...external, '@tarojs/components', '@tarojs/taro'],
    alipay: [...external, '@tarojs/components', '@tarojs/taro'],
    swan: [...external, '@tarojs/components', '@tarojs/taro'],
    tt: [...external, '@tarojs/components', '@tarojs/taro'],
    qq: [...external, '@tarojs/components', '@tarojs/taro'],
    jd: [...external, '@tarojs/components', '@tarojs/taro']
  };

  // 平台特定的解析条件
  const platformConditions = {
    rn: ['react-native', 'import', 'module', 'default'],
    h5: ['browser', 'import', 'module', 'default'],
    weapp: ['weapp', 'import', 'module', 'default'],
    alipay: ['alipay', 'import', 'module', 'default'],
    swan: ['swan', 'import', 'module', 'default'],
    tt: ['tt', 'import', 'module', 'default'],
    qq: ['qq', 'import', 'module', 'default'],
    jd: ['jd', 'import', 'module', 'default']
  };

  // 平台特定的文件扩展名
  const platformExtensions = {
    rn: ['.rn.tsx', '.rn.ts', '.rn.jsx', '.rn.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    h5: ['.h5.tsx', '.h5.ts', '.h5.jsx', '.h5.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    weapp: ['.weapp.tsx', '.weapp.ts', '.weapp.jsx', '.weapp.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    alipay: ['.alipay.tsx', '.alipay.ts', '.alipay.jsx', '.alipay.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    swan: ['.swan.tsx', '.swan.ts', '.swan.jsx', '.swan.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    tt: ['.tt.tsx', '.tt.ts', '.tt.jsx', '.tt.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    qq: ['.qq.tsx', '.qq.ts', '.qq.jsx', '.qq.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    jd: ['.jd.tsx', '.jd.ts', '.jd.jsx', '.jd.js', '.tsx', '.ts', '.jsx', '.js', '.json']
  };

  // 创建平台特定的构建配置
  function createPlatformConfig(platform) {
    return defineConfig({
      build: {
        lib: {
          entry: resolve(packageDir, 'src/index.ts'),
          name: name.replace('@jd/', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase()),
          fileName: (format) => `index.${platform}.${format}.js`,
          formats: ['es', 'cjs']
        },
        rollupOptions: {
          external: platformExternals[platform],
          output: {
            globals: {
              react: 'React',
              'react-dom': 'ReactDOM',
              'react-native': 'ReactNative',
              '@tarojs/taro': 'Taro',
              '@tarojs/components': 'TaroComponents'
            }
          }
        },
        outDir: resolve(packageDir, `dist/${platform}`),
        emptyOutDir: false,
        sourcemap: true
      },
      plugins: [
        react(),
        // 只为第一个平台生成类型定义
        ...(platform === 'rn' ? [dts({
          include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.d.ts'],
          outDir: resolve(packageDir, 'dist/types')
        })] : []),
        libCss()
      ],
      resolve: {
        extensions: platformExtensions[platform],
        conditions: platformConditions[platform]
      },
      define: {
        'process.env.TARO_ENV': JSON.stringify(platform === 'h5' ? 'h5' : platform === 'rn' ? 'rn' : platform)
      }
    });
  }

  return {
    rn: createPlatformConfig('rn'),
    h5: createPlatformConfig('h5'),
    weapp: createPlatformConfig('weapp'),
    alipay: createPlatformConfig('alipay'),
    swan: createPlatformConfig('swan'),
    tt: createPlatformConfig('tt'),
    qq: createPlatformConfig('qq'),
    jd: createPlatformConfig('jd')
  };
}
