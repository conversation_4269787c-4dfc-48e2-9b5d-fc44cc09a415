import { Input } from '@tarojs/components'
import { forwardRef, memo } from 'react'
import withClassName from '../../basic/utils/withClassName'
import { useScreen } from '../../../hooks/useScreen';

const TextInput = (props: any, ref: any) => {
    const { className, onChangeText, value, autoFocus, placeHolder, onSubmitEditing, style, ...others } = props
    const { scaleStyle } = useScreen();

    const onInput = (e) => {
        onChangeText?.(e.detail.value)
    }

    const scaledStyle = style && typeof style === 'object' && !Array.isArray(style)
        ? scaleStyle(style as Record<string, any>)
        : style;

    return (
        <Input ref={ref} onInput={onInput} focus={autoFocus} className={className} type='text' placeholder={placeHolder} value={value} onConfirm={onSubmitEditing} style={scaledStyle} {...others}/>
    )
}

export default memo(withClassName()(forwardRef(TextInput)));

