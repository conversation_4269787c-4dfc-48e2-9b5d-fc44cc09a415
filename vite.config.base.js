import { resolve } from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';
import libCss from 'vite-plugin-libcss';
import rnResolverPlugin from './vite-plugin-rn-resolver';


export default function createViteConfig(packageDir, packageJson) {
  const { name, dependencies, peerDependencies } = packageJson;
  
  // 外部依赖
  const external = [
    ...Object.keys(dependencies || {}),
    ...Object.keys(peerDependencies || {}),
    'react/jsx-runtime',
    '@tarojs/components',
    'react-native'
  ];

  return defineConfig({
    build: {
      lib: {
        entry: resolve(packageDir, 'src/index.ts'),
        name: name.replace('@jd/', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase()),
        fileName: (format) => `index.${format}.js`,
        formats: ['es', 'cjs']
      },
      rollupOptions: {
        external,
        output: {
          globals: {
            react: 'React',
            'react-dom': 'ReactDOM',
            '@tarojs/taro': 'Taro'
          }
        }
      },
      outDir: resolve(packageDir, 'dist'),
      emptyOutDir: true,
      sourcemap: true
    },
    plugins: [
      rnResolverPlugin(),
      react(),
      dts({
        include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.d.ts'],
        outDir: resolve(packageDir, 'dist/types')
      }),
      libCss()
    ],
    resolve: {
      extensions: ['.js', '.jsx', '.ts', '.tsx', '.json', '.rn.js', '.rn.jsx', '.rn.ts', '.rn.tsx'],
      conditions: ['react-native', 'import', 'module', 'default']
    }
  });
}
