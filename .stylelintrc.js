module.exports = {
  extends: [
    'stylelint-config-standard-scss'
  ],
  plugins: [
    'stylelint-scss',
    'stylelint-order',
    './stylelint-rn-rules/index.js' // 加入自定义插件
  ],
  rules: {
    // 只允许类选择器，且必须 kebab-case 或 BEM 风格
    'selector-class-pattern': '^[a-z0-9\\-]+(__[a-z0-9\\-]+)?(--[a-z0-9\\-]+)?$',

    // 禁止标签选择器
    'selector-type-no-unknown': [true, { ignore: ['custom-elements'] }],
    'selector-type-case': 'lower',
    'selector-max-type': 0,

    // 禁止 id 选择器
    'selector-max-id': 0,

    // 禁止组合器
    'selector-max-compound-selectors': 1,
    'selector-max-combinators': 0,
    'selector-max-universal': 0,

    // 禁止伪类和伪元素
    'selector-pseudo-class-no-unknown': [true, { ignorePseudoClasses: [] }],
    'selector-pseudo-element-no-unknown': [true, { ignorePseudoElements: [] }],

    // 禁止 !important
    'declaration-no-important': true,

    // 禁止空块
    'block-no-empty': true,

    // 限制嵌套深度
    'max-nesting-depth': 3,

    // 要求每个规则前有空行
    'rule-empty-line-before': ['always', { except: ['first-nested'] }],

    // 属性名按字母顺序
    'order/properties-alphabetical-order': true,

    // 启用自定义 RN 规则
    'rn/no-unsupported-styles': true,
  }
}; 