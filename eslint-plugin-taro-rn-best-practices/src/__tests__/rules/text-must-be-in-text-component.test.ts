import { RuleTester } from '@typescript-eslint/utils/dist/eslint-utils';
import rule from '../../rules/text-must-be-in-text-component';

const ruleTester = new RuleTester({
  parser: require.resolve('@typescript-eslint/parser'),
  parserOptions: {
    ecmaVersion: 2018,
    sourceType: 'module',
    ecmaFeatures: {
      jsx: true,
    },
  },
});

ruleTester.run('text-must-be-in-text-component', rule, {
  valid: [
    {
      code: `
        <View>
          <Text>Hello World</Text>
        </View>
      `,
    },
    {
      code: `
        <Text>
          Hello World
        </Text>
      `,
    },
    {
      code: `
        <View>
          {/* This is a comment */}
        </View>
      `,
    },
  ],
  invalid: [
    {
      code: `
        <View>
          Hello World
        </View>
      `,
      errors: [{ messageId: 'textMustBeInTextComponent' }],
      output: `
        <View>
          <Text>Hello World</Text>
        </View>
      `,
    },
    {
      code: `
        <View>
          <View />
          Some text here
        </View>
      `,
      errors: [{ messageId: 'textMustBeInTextComponent' }],
      output: `
        <View>
          <View />
          <Text>Some text here</Text>
        </View>
      `,
    },
  ],
});