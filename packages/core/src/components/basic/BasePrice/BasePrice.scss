.base-price {
  display: inline-flex; // 横向布局
  flex-direction: row;
  align-items: baseline; // 文本基线对齐
  line-height: 1;        // 避免默认行高影响对齐
  overflow: hidden;
  // 删除线修饰符
  &--strikethrough {
    // Web 侧，子 Text 元素会继承 text-decoration
    text-decoration: line-through;
    // 注意：对于 RN，text-decorationLine 需要直接应用在 Text 组件的 style 上
    // style.rn.ts 将处理此情况
  }
}

// 货币符号
.base-price__symbol {
  // 默认样式可以在这里定义，或完全由 props 通过 style.rn.ts 或内联 style (Web) 控制
  // 例如: margin-right: 2px; // 如果需要符号和数字间有固定间距
  font-family: 'JDZhengHT-Regular'; // 设置统一字体
}

// 整数部分
.base-price__integer {
  font-family: 'JDZhengHT-Regular'; // 设置统一字体
  // 默认样式
}

// 小数点 (如果显示)
.base-price__decimal-dot {
  // 默认样式
  font-family: 'JDZhengHT-Regular'; // 设置统一字体
}

// 小数部分 (如果显示)
.base-price__decimal {
  // 默认样式
  font-family: 'JDZhengHT-Regular'; // 设置统一字体
} 