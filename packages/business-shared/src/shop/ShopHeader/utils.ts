import { ShopHeaderProps, LineConfig, ContentType, Content, TitleContent, TagContent } from './types';

/**
 * 计算ShopHeader组件的高度
 * @param props ShopHeader的props
 * @returns 计算后的高度（像素）
 */
export const calculateShopHeaderHeight = (props: ShopHeaderProps): number => {
  const {
    imageHeight = 72,
    lines = [],
    lineSpacing = 0,
    lineHeight,
  } = props;

  const containerPadding = 0;
  
  // 如果没有行配置，只计算图片高度 + padding
  if (!lines || lines.length === 0) {
    return imageHeight + containerPadding;
  }
  
  // 计算所有行的总高度
  let totalLinesHeight = 0;
  
//   lines.forEach((lineConfig: LineConfig, index: number) => {
//     // 计算当前行的高度
//     let currentLineHeight = 0;
    
//     // 如果指定了行高，直接使用
//     if (lineConfig.height) {
//       currentLineHeight = lineConfig.height;
//     } else if (lineHeight) {
//       currentLineHeight = lineHeight;
//     } else {
//       // 根据内容类型计算高度
//       currentLineHeight = calculateLineContentHeight(lineConfig);
//     }
    
//     // 累加行高
//     totalLinesHeight += currentLineHeight;
    
//     // 添加行间距（除了第一行）
//     if (index > 0) {
//       totalLinesHeight += lineSpacing;
//     }
//   });

  // 返回图片高度和行内容高度的最大值 + padding
  return Math.max(imageHeight, totalLinesHeight) + containerPadding;
};

/**
 * 计算单行内容的高度
 * @param lineConfig 行配置
 * @returns 行高度
 */
function calculateLineContentHeight(lineConfig: LineConfig): number {
  let maxHeight = 0;
  
  // 检查左侧内容
  if (lineConfig.left) {
    const leftHeight = calculateContentArrayHeight(lineConfig.left);
    maxHeight = Math.max(maxHeight, leftHeight);
  }
  
  // 检查右侧内容
  if (lineConfig.right) {
    const rightHeight = calculateContentArrayHeight(lineConfig.right);
    maxHeight = Math.max(maxHeight, rightHeight);
  }
  
  // 如果没有计算到高度，使用默认值
  return maxHeight || 20;
}

/**
 * 计算内容数组的高度
 * @param contents 内容数组
 * @returns 最大高度
 */
function calculateContentArrayHeight(contents: Content[]): number {
  let maxHeight = 0;
  
  contents.forEach(content => {
    const contentHeight = calculateSingleContentHeight(content);
    maxHeight = Math.max(maxHeight, contentHeight);
  });
  
  return maxHeight;
}

/**
 * 计算单个内容的高度
 * @param content 内容
 * @returns 内容高度
 */
function calculateSingleContentHeight(content: Content): number {
  if (!content) return 0;
  
  // 如果是React节点，使用默认高度
  if (typeof content !== 'object' || !('type' in content)) {
    return 20; // 默认高度
  }
  
  const typedContent = content as (TitleContent | TagContent);
  
  switch (typedContent.type) {
    case ContentType.TITLE:
      const titleContent = typedContent as TitleContent;
      const fontSize = titleContent.fontSize || 15; // 默认字体大小
      const lines = titleContent.lines || 1;
      // 标题高度 = 字体大小 * 行数 * 行高倍数(1.2)
      return fontSize * lines * 1.2;
      
    case ContentType.TAG:
      const tagContent = typedContent as TagContent;
      const tagFontSize = tagContent.textStyle?.fontSize || 10; // 默认标签字体大小
      // 标签高度 = 字体大小 + 内边距(上下各4px)
      return tagFontSize + 8;
      
    default:
      return 20; // 默认高度
  }
}

/**
 * 计算ShopHeader在不同屏幕密度下的高度
 * @param props ShopHeader的props
 * @param screenScale 屏幕缩放比例，默认为1
 * @returns 缩放后的高度
 */
export const calculateShopHeaderScaledHeight = (props: ShopHeaderProps, screenScale: number = 1): number => {
  const baseHeight = calculateShopHeaderHeight(props);
  return Math.round(baseHeight * screenScale);
}; 