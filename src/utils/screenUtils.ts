/**
 * 屏幕工具函数 - Web和小程序版本
 * 
 * 专门为Web和小程序环境提供的屏幕工具函数
 * React Native环境请使用 screenUtils.rn.ts
 */
import type { DesignValue } from '../types/types';
import * as Platform from '../../../ToolLibs/platform/index';
import * as Device from '../../../ToolLibs/device/index';

/**
 * 获取屏幕尺寸
 */
export function getScreenSize(): { width: number; height: number } {
  // Web环境
  if (typeof window !== 'undefined') {
    return {
      width: window.innerWidth || document.documentElement.clientWidth,
      height: window.innerHeight || document.documentElement.clientHeight
    };
  }
  
  // 小程序环境
  try {
    const Taro = require('@tarojs/taro');
    const systemInfo = Taro.getSystemInfoSync();
    return {
      width: systemInfo.windowWidth || systemInfo.screenWidth,
      height: systemInfo.windowHeight || systemInfo.screenHeight
    };
  } catch (e) {
    console.warn('Failed to get screen size from <PERSON><PERSON>, using default:', e);
  }
  
  // 默认尺寸
  return { width: 375, height: 667 };
}

/**
 * 判断是否为平板设备
 */
export function getIsPad(): boolean {
  return (Platform as any).isPad || false;
}

/**
 * 获取项目的scaleByScreen方法
 */
export function getScaleByScreenFunction(): (value: DesignValue, forceScale?: boolean) => number {
  // 优先使用项目的device工具
  if (Device.scaleByScreen && typeof Device.scaleByScreen === 'function') {
    return Device.scaleByScreen;
  }
  
  // Web/小程序环境的降级实现
  return (value: DesignValue, forceScale = false): number => {
    if (typeof value === 'number') {
      const { width } = getScreenSize();
      const result = value * width / 375; // 基于375px设计稿的缩放
      return result;
    }
    
    if (typeof value === 'string') {
      return parseFloat(value) || 0;
    }
    
    // 对象类型处理
    if (typeof value === 'object' && value !== null) {
      return getScaleByScreenFunction()(value.mobile, forceScale);
    }
    
    return 0;
  };
}

/**
 * 监听屏幕尺寸变化
 */
export function addScreenChangeListener(callback: () => void): () => void {
  // Web环境
  if (typeof window !== 'undefined') {
    const handleResize = () => callback();
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
    };
  }
  
  // 小程序环境暂时不支持监听，返回空的清理函数
  return () => {};
}

// 需要缩放的CSS属性列表
const SCALABLE_STYLE_PROPS = [
  // 尺寸属性
  'width', 'height', 'minWidth', 'maxWidth', 'minHeight', 'maxHeight',
  // 内边距
  'padding', 'paddingTop', 'paddingRight', 'paddingBottom', 'paddingLeft',
  'paddingHorizontal', 'paddingVertical',
  // 外边距  
  'margin', 'marginTop', 'marginRight', 'marginBottom', 'marginLeft',
  'marginHorizontal', 'marginVertical',
  // 边框
  'borderRadius', 'borderWidth', 'borderTopWidth', 'borderRightWidth', 
  'borderBottomWidth', 'borderLeftWidth',
  'borderTopLeftRadius', 'borderTopRightRadius', 'borderBottomLeftRadius', 'borderBottomRightRadius',
  // 位置
  'top', 'right', 'bottom', 'left',
  // 字体
  'fontSize', 'lineHeight',
  // 其他
  'gap', 'rowGap', 'columnGap'
];

/**
 * 创建样式缩放函数
 */
export function createScaleStyleFunction(): (style: Record<string, any>) => Record<string, any> {
  const scaleByScreenFn = getScaleByScreenFunction();
  
  return (style: Record<string, any>): Record<string, any> => {
    if (!style || typeof style !== 'object') {
      return style || {};
    }
    
    const scaledStyle: Record<string, any> = {};
    
    // 遍历样式属性
    Object.keys(style).forEach(key => {
      const value = style[key];
      
      // 如果是需要缩放的属性且值为数字
      if (SCALABLE_STYLE_PROPS.includes(key) && typeof value === 'number') {
        const scaledValue = scaleByScreenFn(value);
        scaledStyle[key] = scaledValue;
      } else {
        // 其他属性直接复制
        scaledStyle[key] = value;
      }
    });
    
    return scaledStyle;
  };
} 