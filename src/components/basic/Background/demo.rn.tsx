import React from 'react';
import { <PERSON><PERSON>reaView, Sc<PERSON>View, StyleSheet } from 'react-native';
import View from '../../basic/View';
import Text from '../../basic/Text';
import { Background } from './index';

const BackgroundDemoRN: React.FC = () => {
  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.demoItem}>
          <Text style={styles.title}>默认样式 - 指定单一颜色，默认红</Text>
          <Background 
            backgroundWidth={130} 
            backgroundHeight={40}
            backgroundColor={['#E1251B']}
          />
        </View>
        <View style={styles.demoItem}>
          <Text style={styles.title}>默认样式 - 指定背景图，默认填充</Text>
          <Background 
            backgroundWidth={130} 
            backgroundHeight={40}
            backgroundBorderRadius={10}
            backgroundImageURL='https://img30.360buyimg.com/img/jfs/t1/301190/25/8889/652/682ee8f7F17feb63d/c33d3db673a40a68.png'
          />
        </View>
        <View style={styles.demoItem}>
          <Text style={styles.title}>自定义样式 - 指定渐变</Text>
          <Background 
            backgroundWidth={130} 
            backgroundHeight={40}
            backgroundColor={['#E1251B', '#FF5252']}
            gradientPercent={30}
          />
        </View>
         <View style={styles.demoItem}>
          <Text style={styles.title}>自定义样式 - 指定渐变</Text>
          <Background 
            backgroundWidth={130} 
            backgroundHeight={40}
            colorGroupStartPosition={'TOP'}
            backgroundColor={['#E1251B', '#FF5252']}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollContent: {
    padding: 16,
  },
  demoItem: {
    marginBottom: 20,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 8,
    alignItems: 'flex-start',
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#333333',
  }
});

export default BackgroundDemoRN; 