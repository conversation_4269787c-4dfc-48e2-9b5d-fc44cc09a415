import { resolve } from 'path';
import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import dts from 'vite-plugin-dts';
import libCss from 'vite-plugin-libcss';

// 创建平台特定的依赖重写插件
function createPlatformDependencyPlugin(platform) {
  return {
    name: 'platform-dependency-rewrite',
    generateBundle(options, bundle) {
      // 只在非 h5 平台处理依赖重写
      if (platform === 'h5') return;

      Object.keys(bundle).forEach(fileName => {
        const chunk = bundle[fileName];
        if (chunk.type === 'chunk' && chunk.code) {
          // 重写对 @jd/lifeui-core 的导入
          chunk.code = chunk.code.replace(
            /from\s+['"]@jd\/lifeui-core['"]/g,
            `from '@jd/lifeui-core/${platform}'`
          );
          chunk.code = chunk.code.replace(
            /require\(['"]@jd\/lifeui-core['"]\)/g,
            `require('@jd/lifeui-core/${platform}')`
          );
        }
      });
    }
  };
}

// 多端构建配置
export default function createMultiPlatformConfig(packageDir, packageJson) {
  const { name, dependencies, peerDependencies } = packageJson;

  // 外部依赖
  const external = [
    ...Object.keys(dependencies || {}),
    ...Object.keys(peerDependencies || {}),
    'react/jsx-runtime'
  ];

  // 为不同平台创建特定的外部依赖配置
  function createPlatformExternals(platform) {
    // 对于非 h5 平台，我们内联 @jd/lifeui-core 以确保使用正确的平台特定代码
    if (platform !== 'h5') {
      return external.filter(dep => dep !== '@jd/lifeui-core');
    }
    return external;
  }

  // 平台特定的外部依赖
  const platformExternals = {
    rn: [...createPlatformExternals('rn'), 'react-native'],
    h5: [...createPlatformExternals('h5'), 'react-dom', '@tarojs/components', '@tarojs/taro'],
    weapp: [...createPlatformExternals('weapp'), '@tarojs/components', '@tarojs/taro'],
    alipay: [...createPlatformExternals('alipay'), '@tarojs/components', '@tarojs/taro'],
    swan: [...createPlatformExternals('swan'), '@tarojs/components', '@tarojs/taro'],
    tt: [...createPlatformExternals('tt'), '@tarojs/components', '@tarojs/taro'],
    qq: [...createPlatformExternals('qq'), '@tarojs/components', '@tarojs/taro'],
    jd: [...createPlatformExternals('jd'), '@tarojs/components', '@tarojs/taro']
  };

  // 平台特定的解析条件
  const platformConditions = {
    rn: ['react-native', 'import', 'module', 'default'],
    h5: ['browser', 'import', 'module', 'default'],
    weapp: ['weapp', 'import', 'module', 'default'],
    alipay: ['alipay', 'import', 'module', 'default'],
    swan: ['swan', 'import', 'module', 'default'],
    tt: ['tt', 'import', 'module', 'default'],
    qq: ['qq', 'import', 'module', 'default'],
    jd: ['jd', 'import', 'module', 'default']
  };

  // 平台特定的文件扩展名
  const platformExtensions = {
    rn: ['.rn.tsx', '.rn.ts', '.rn.jsx', '.rn.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    h5: ['.h5.tsx', '.h5.ts', '.h5.jsx', '.h5.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    weapp: ['.weapp.tsx', '.weapp.ts', '.weapp.jsx', '.weapp.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    alipay: ['.alipay.tsx', '.alipay.ts', '.alipay.jsx', '.alipay.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    swan: ['.swan.tsx', '.swan.ts', '.swan.jsx', '.swan.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    tt: ['.tt.tsx', '.tt.ts', '.tt.jsx', '.tt.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    qq: ['.qq.tsx', '.qq.ts', '.qq.jsx', '.qq.js', '.tsx', '.ts', '.jsx', '.js', '.json'],
    jd: ['.jd.tsx', '.jd.ts', '.jd.jsx', '.jd.js', '.tsx', '.ts', '.jsx', '.js', '.json']
  };

  // 创建平台特定的构建配置
  function createPlatformConfig(platform) {
    return defineConfig({
      build: {
        lib: {
          entry: resolve(packageDir, 'src/index.ts'),
          name: name.replace('@jd/', '').replace(/-([a-z])/g, (_, letter) => letter.toUpperCase()),
          fileName: (format) => `index.${platform}.${format}.js`,
          formats: ['es', 'cjs']
        },
        rollupOptions: {
          external: platformExternals[platform],
          output: {
            globals: {
              react: 'React',
              'react-dom': 'ReactDOM',
              'react-native': 'ReactNative',
              '@tarojs/taro': 'Taro',
              '@tarojs/components': 'TaroComponents'
            }
          }
        },
        outDir: resolve(packageDir, `dist/${platform}`),
        emptyOutDir: false,
        sourcemap: true
      },
      plugins: [
        react(),
        // 只为第一个平台生成类型定义
        ...(platform === 'rn' ? [dts({
          include: ['src/**/*.ts', 'src/**/*.tsx', 'src/**/*.d.ts'],
          outDir: resolve(packageDir, 'dist/types')
        })] : []),
        libCss(),
        // 添加平台特定的依赖重写插件
        createPlatformDependencyPlugin(platform)
      ],
      resolve: {
        extensions: platformExtensions[platform],
        conditions: platformConditions[platform],
        alias: platform !== 'h5' ? {
          '@jd/lifeui-core': resolve(packageDir, '../core/src'),
          '@jd/lifeui-core/utils/screenUtils': resolve(packageDir, '../core/src/utils/screenUtils.rn.ts')
        } : {}
      },
      define: {
        'process.env.TARO_ENV': JSON.stringify(platform === 'h5' ? 'h5' : platform === 'rn' ? 'rn' : platform)
      }
    });
  }

  return {
    rn: createPlatformConfig('rn'),
    h5: createPlatformConfig('h5'),
    weapp: createPlatformConfig('weapp'),
    alipay: createPlatformConfig('alipay'),
    swan: createPlatformConfig('swan'),
    tt: createPlatformConfig('tt'),
    qq: createPlatformConfig('qq'),
    jd: createPlatformConfig('jd')
  };
}
