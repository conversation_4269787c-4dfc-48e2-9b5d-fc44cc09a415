import { ESLintUtils } from '@typescript-eslint/utils';

const createRule = ESLintUtils.RuleCreator(
  (name) => `https://docs.taro.zone/docs/react-native-remind#${name}`
);

export default createRule({
  name: 'no-fixed-position',
  meta: {
    type: 'problem',
    docs: {
      description: 'Disallow position: fixed as it is not supported in React Native.',
      recommended: 'error',
    },
    messages: {
      noFixedPosition: 'position: fixed is not supported in React Native. Use position: absolute instead.',
    },
    schema: [],
    fixable: 'code',
  },
  defaultOptions: [],
  create(context) {
    return {
      // 检查 style={{ position: 'fixed' }}
      JSXAttribute(node) {
        if (
          node.name && node.name.name === 'style' &&
          node.value &&
          node.value.type === 'JSXExpressionContainer' &&
          node.value.expression &&
          node.value.expression.type === 'ObjectExpression'
        ) {
          for (const prop of node.value.expression.properties) {
            if (
              prop.type === 'Property' &&
              prop.key.type === 'Identifier' &&
              prop.key.name === 'position' &&
              prop.value.type === 'Literal' &&
              prop.value.value === 'fixed'
            ) {
              context.report({
                node: prop.value,
                messageId: 'noFixedPosition',
                fix: (fixer) => fixer.replaceText(prop.value, "'absolute'"),
              });
            }
          }
        }
      },
      // 检查 StyleSheet.create({ ... })
      CallExpression(node) {
        if (
          node.callee &&
          node.callee.type === 'MemberExpression' &&
          node.callee.object.type === 'Identifier' &&
          node.callee.object.name === 'StyleSheet' &&
          node.callee.property.type === 'Identifier' &&
          node.callee.property.name === 'create' &&
          node.arguments &&
          node.arguments.length > 0 &&
          node.arguments[0].type === 'ObjectExpression'
        ) {
          const styleObj = node.arguments[0];
          for (const prop of styleObj.properties) {
            if (prop.type !== 'Property' || prop.value.type !== 'ObjectExpression') continue;
            for (const styleProp of prop.value.properties) {
              if (
                styleProp.type === 'Property' &&
                styleProp.key.type === 'Identifier' &&
                styleProp.key.name === 'position' &&
                styleProp.value.type === 'Literal' &&
                styleProp.value.value === 'fixed'
              ) {
                context.report({
                  node: styleProp.value,
                  messageId: 'noFixedPosition',
                  fix: (fixer) => fixer.replaceText(styleProp.value, "'absolute'"),
                });
              }
            }
          }
        }
      },
    };
  },
});
