import React from 'react';
import { ScrollView } from 'react-native';
import View from '../basic/View';
import Text from '../basic/Text';
import CustomTag from './customTag';
import { TagType } from './types';

const CustomTagDemo: React.FC = () => {
  return (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 20 }}>
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>纯文本标签 (TEXT)</Text>
          <CustomTag 
            type={TagType.TEXT}
            text="基础标签" 
          />
        </View>
        
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>自定义颜色文本标签</Text>
          <CustomTag 
            type={TagType.TEXT}
            text="促销标签" 
            backgroundColor="#ff4d4f"
            textStyle={{ color: '#fff' }}
          />
        </View>
        
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>带边框文本标签</Text>
          <CustomTag 
            type={TagType.TEXT}
            text="会员专享" 
            showBorder
            borderColor="#faad14"
            textStyle={{ color: '#faad14' }}
          />
        </View>

        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>纯图片标签 (IMAGE)</Text>
          <CustomTag 
            type={TagType.IMAGE}
            tagImage="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
          />
        </View>

        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>前置图标标签 (ICON_PREFIX)</Text>
          <CustomTag 
            type={TagType.ICON_PREFIX}
            text="新品" 
            prefixIcon="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
            backgroundColor="#f0f9ff"
            textStyle={{ color: '#1890ff' }}
          />
        </View>

        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>后置图标标签 (ICON_SUFFIX)</Text>
          <CustomTag 
            type={TagType.ICON_SUFFIX}
            text="限时促销" 
            suffixIcon="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
            showBorder
            backgroundColor="#fff0f0"
            textStyle={{ color: '#e93b3d' }}
          />
        </View>

        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>前置图标+图片标签</Text>
          <CustomTag 
            type={TagType.ICON_PREFIX}
            prefixIcon="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
            tagImage="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
            backgroundColor="#f6ffed"
          />
        </View>
      </View>
    </ScrollView>
  );
};

export default CustomTagDemo;
