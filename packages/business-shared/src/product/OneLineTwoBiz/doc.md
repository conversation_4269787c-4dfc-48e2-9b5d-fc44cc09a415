# OneLineTwoBiz 一行两业务卡片组件

## 组件介绍

`OneLineTwoBiz` 是一个用于展示产品信息的卡片组件，适用于电商场景。它展示了产品图片、名称、价格（原价和现价）、距离信息、月销量以及折扣信息。

## 代码演示

```jsx
<OneLineTwoBiz 
  imageSrc="https://img10.360buyimg.com/imagetools/jfs/t1/203818/18/35983/134596/65e47fe8F58e88b44/0917b04b2bd0d6db.jpg"
  distance="3.5km"
  productName="壹品白鹅 品牌纯色+跳"
  currentPrice={48}
  originalPrice={98}
  monthlySales="200+"
  discountLabel="3.5折热销中"
  onClick={() => { console.log('点击了产品卡片'); }}
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| className | 自定义类名 | `string` | - |
| style | 自定义样式 | `CSSProperties` | - |
| onClick | 点击卡片时的回调函数 | `() => void` | - |
| imageSrc | 商品图片链接 | `string` | **必填** |
| imageSize | 商品图片大小，单位px | `number` | `142` |
| imageTag | 商品图片标签 | `TagProps` \| `TagProps[]` | - |
| distance | 距离信息 | `string` | - |
| productName | 产品名称 | `string` | **必填** |
| productNameFontSize | 产品名称字体大小 | `number` | `14` |
| productNameLines | 产品名称显示行数 | `number` | `1` |
| productNameEllipsis | 产品名称超出显示方式 | `'ellipsis'` \| `'fade'` | `'ellipsis'` |
| productNameTag | 产品名称标签 | `TagProps` \| `TagProps[]` | - |
| productNameStyle | 产品名称样式 | `CSSProperties` | - |
| currentPrice | 当前价格 | `number` | **必填** |
| originalPrice | 原价 | `number` | - |
| priceType | 价格类型 | `'default'` \| `'discount'` | `'default'` |
| currentPriceColor | 当前价格颜色 | `string` | `'#FF1530'` |
| originalPriceColor | 原价颜色 | `string` | `'#999999'` |
| currentPriceFontSize | 当前价格字体大小 | `number` | `20` |
| originalPriceFontSize | 原价字体大小 | `number` | `14` |
| currencySymbol | 货币符号 | `string` | `'¥'` |
| currentPriceSymbolFontSize | 当前价格货币符号字体大小 | `number` | - |
| originalPriceSymbolFontSize | 原价货币符号字体大小 | `number` | - |
| currentPriceDecimalFontSize | 当前价格小数部分字体大小 | `number` | - |
| originalPriceDecimalFontSize | 原价小数部分字体大小 | `number` | - |
| strikethrough | 是否给原价添加删除线 | `boolean` | `true` |
| monthlySales | 月销量 | `number` \| `string` | - |
| monthlySalesLabel | 月销量标签文本 | `string` | `'月售'` |
| discountLabel | 折扣标签 | `string` | - |
| discountLabelBgColor | 折扣标签背景颜色 | `string` | `'#FF1530'` |

## 设计思路

组件采用了BEM命名规范的CSS样式，支持多端（Web、React Native等），并使用函数式编程方式实现。主要展示了美甲产品的信息，包括：

1. 产品图片，左下角显示距离信息
2. 产品名称
3. 价格信息（促销价和原价）
4. 月销量
5. 折扣标签（右上角显示）

组件结构清晰，便于维护和扩展。
