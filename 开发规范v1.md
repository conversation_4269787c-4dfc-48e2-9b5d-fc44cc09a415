# 本地生活组件库开发规范 v1.0

## 1. 项目概述

### 1.1 技术选型
- 核心框架：React + TypeScript
- 多端适配：Taro + React Native
- 包管理：Lerna + Yarn Workspaces
- 代码规范：ESLint + Stylelint
- 测试框架：Jest
- 文档工具：Markdown

### 1.2 项目结构
```
uicomponents/
├── packages/                # 所有包目录
│   ├── core/               # 核心组件包
│   │   ├── src/
│   │   │   ├── components/ # 基础组件
│   │   │   ├── hooks/     # 通用 Hooks
│   │   │   ├── utils/     # 工具函数
│   │   │   ├── types/     # 类型定义
│   │   │   ├── constants/ # 常量定义
│   │   │   ├── styles/    # 样式文件
│   │   │   └── theme/     # 主题配置
│   │   └── package.json
│   ├── business-shared/    # 业务共享包
│   ├── business-takeout/   # 外卖业务包
│   ├── business-groupon/   # 团购业务包
│   ├── business-travel/    # 旅游业务包
│   └── business-instant/   # 即时配送业务包
├── lerna.json              # Lerna 配置
├── package.json            # 根目录配置
└── .npmrc                  # NPM 配置
```

## 2. 包管理规范

### 2.1 包命名规范
- 核心包：`@jd/uicomponents-core`
- 业务共享包：`@jd/uicomponents-business-shared`
- 业务包：`@jd/uicomponents-business-{业务名}`
  - 外卖：`@jd/uicomponents-business-takeout`
  - 团购：`@jd/uicomponents-business-groupon`
  - 旅游：`@jd/uicomponents-business-travel`
  - 秒送：`@jd/uicomponents-business-instant`

### 2.2 版本管理
- 使用 Lerna 进行版本管理
- 遵循语义化版本（Semantic Versioning）
- 主版本号：不兼容的 API 修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

## 3. 文件命名规范

### 3.1 文件命名
- 使用 camelCase（驼峰）命名
- 组件目录：`button/`、`basePrice/`
- 样式文件：`button.scss`、`style.rn.ts`
- 类型文件：`types.ts`
- 测试文件：`button.spec.tsx`
- 文档文件：`doc.md`

### 3.2 组件命名
- 使用 PascalCase（大驼峰）命名
- 基础组件：`Button`、`BasePrice`
- 业务组件：`TakeoutCard`、`GrouponList`

## 4. 组件开发规范

### 4.1 目录结构
```
temp-ui/                # 模板组件
├── temp-ui.scss       # Taro 样式
├── style.rn.ts        # RN 样式
├── temp-ui.tsx        # 组件实现
├── temp-ui.rn.tsx     # RN 特殊逻辑
├── types.ts           # 类型定义
├── index.ts           # 导出文件
├── demo.tsx           # Web 示例
├── demo.rn.tsx        # RN 示例
└── doc.md             # 组件文档
```
- 开发组件可复制temp-ui后进行二次开发

### 4.2 组件开发原则
- 单一职责原则
- 接口一致性原则
- 平台差异最小化原则
- 样式与逻辑分离原则
- 类型安全原则

### 4.3 组件实现规范
```typescript
// 1. 类型定义
interface ButtonProps {
  type?: 'primary' | 'default';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
  onClick?: () => void;
  children: React.ReactNode;
}

// 2. 组件实现
const Button: React.FC<ButtonProps> = ({
  type = 'default',
  size = 'medium',
  disabled = false,
  onClick,
  children
}) => {
  return (
    <View className={`button button--${type} button--${size}`}>
      <Text>{children}</Text>
    </View>
  );
};

// 3. 导出
export default Button;
export type { ButtonProps };
```

## 5. 样式规范

### 5.1 样式文件组织
- Taro 平台：使用 SCSS
- RN 平台：使用 StyleSheet
- 主题变量：统一管理
- 工具类：统一管理

### 5.2 BEM 命名规范
```scss
.button {
  &__text {
    &--primary { ... }
  }
  &--large { ... }
}
```

### 5.3 样式差异处理
```typescript
// style.rn.ts
import { StyleSheet } from 'react-native';

export default StyleSheet.create({
  button: {
    // RN 特有样式
  }
});
```

## 6. 文档规范

### 6.1 文档结构
```markdown
# 组件名称

## 组件描述
简要说明组件的用途和特点。

## 使用示例
```tsx
import { Button } from '@jd/uicomponents-core';

export default () => (
  <Button type="primary" size="large">
    按钮
  </Button>
);
```

## Props
| 属性名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| type | 'primary' \| 'default' | 否 | 'default' | 按钮类型 |
| size | 'small' \| 'medium' \| 'large' | 否 | 'medium' | 按钮尺寸 |

## 平台差异
- Web 平台特点
- RN 平台特点

## 注意事项
- 使用建议
- 性能考虑
- 兼容性说明
```

## 7. 测试规范

### 7.1 测试文件组织
- 测试文件放在 `__tests__` 目录
- 测试文件命名为 `组件名.spec.tsx`
- 必须包含基础渲染测试
- 必须包含 Props 测试
- 必须包含事件处理测试
- 必须包含平台差异测试

### 7.2 测试用例示例
```typescript
import React from 'react';
import { render } from '@testing-library/react';
import Button from '../button';

describe('Button', () => {
  it('renders with default props', () => {
    const { getByText } = render(<Button>按钮</Button>);
    expect(getByText('按钮')).toBeTruthy();
  });

  it('handles click event', () => {
    const onClick = jest.fn();
    const { getByText } = render(
      <Button onClick={onClick}>按钮</Button>
    );
    getByText('按钮').click();
    expect(onClick).toHaveBeenCalled();
  });
});
```

## 8. 发布流程

### 8.1 版本发布步骤
1. 更新版本号
2. 更新 CHANGELOG.md
3. 编写发布说明
4. 确保文档完整性
5. 确保测试通过
6. 提交代码
7. 合并主分支
8. 发布新版本

### 8.2 提交规范
提交信息格式：`<type>(<scope>): <subject>`

type 类型：
- feat: 新功能
- fix: 修复
- docs: 文档
- style: 格式
- refactor: 重构
- test: 测试
- chore: 构建过程或辅助工具的变动

## 9. 开发工具配置

### 9.1 VSCode 配置
- ESLint 插件
- Stylelint 插件
- Prettier 插件
- 保存时自动修复

### 9.2 常用命令
```bash
# 安装依赖
yarn install

# 开发模式
yarn dev

# 构建
yarn build

# 测试
yarn test

# 代码检查
yarn lint

# 发布
yarn publish
```

## 10. 注意事项

### 10.1 开发注意事项
- 遵循 TypeScript 类型安全
- 保持组件接口一致性
- 最小化平台差异
- 及时更新文档
- 编写单元测试

### 10.2 性能优化
- 合理使用 memo
- 避免不必要的重渲染
- 优化样式计算
- 控制组件粒度
- 按需加载

### 10.3 兼容性处理
- 统一使用基础组件
- 处理平台差异
- 降级方案
- 错误处理
- 日志记录 