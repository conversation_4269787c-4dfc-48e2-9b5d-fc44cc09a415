import { CSSProperties } from 'react';

// 基础样式配置
export const productCardStyles = {
  container: {
    display: 'flex',
    flexDirection: 'column',
    width: 'auto',
  } as CSSProperties,
  
  imageContainer: {
    position: 'relative',
    width: '100%',
    overflow: 'hidden',
  } as CSSProperties,
  
  content: {
    display: 'flex',
    flexDirection: 'column',
    padding: '8px 0',
  } as CSSProperties,
  
  titleContainer: {
    margin: '8px 0',
  } as CSSProperties,
  
  priceContainer: {
    display: 'flex',
    alignItems: 'center',
  } as CSSProperties,
};

export default productCardStyles;
