import React from 'react';
// import { View } from 'react-native';
import View from '../../components/basic/View';
import Header from './components/Header';
import { MarketingFloorProps } from './types';
import styles from './style.rn';
import FlipCarousel from '../FlipCarousel/index.rn';
import ProductCard from '../../components/display/ProductCard';
import Image from '../../components/basic/Image';
import './marketingFloor.scss'


const MarketingFloor: React.FC<MarketingFloorProps> = ({
  // 顶部左侧标题
  subsidyImageUrl,
  subsidyImageWidth,
  subsidyImageHeight,

  // 背景
  panelBgImage,
  panelBgImageWidth,
  panelBgImageHeight,

  // 更多
  showMoreLink,
  rightIconColor,
  moreText,
  onMoreClick,


  products = [],
  containerStyle,
}) => {


  // 确保产品数量是4的倍数，便于翻页展示
  const validProducts = products.length > 0
    ? products.slice(0, Math.floor(products.length / 4) * 4)
    : [];

  // 自定义渲染商品项
  const renderProductItem = (product: any, index: number) => {
    return (
      <ProductCard
        imageSize={75}
        shopNameFontSize={12}
        shopNameLines={1}
        productNameFontSize={12}
        productNameLines={1}
        {...product}
        onClick={() => product.onProductClick && product.onProductClick(product, index)}
      />
    );
  };


  return (
    <View style={[styles.container, containerStyle]}>

      {/* 背景图片 */}
      <Image
        src={panelBgImage}
        className='marketing-floor__panel-bg'
        style={{
          width: panelBgImageWidth,
          height: panelBgImageHeight,
        }}
      />
  
      <Header
        subsidyImageUrl={subsidyImageUrl}
        subsidyImageWidth={subsidyImageWidth}
        subsidyImageHeight={subsidyImageHeight}
        showMoreLink={showMoreLink}
        moreText={moreText}
        onMoreClick={onMoreClick}
        textColor={rightIconColor}
        className="marketing-floor__header"
      />

      {/* 内容区域 - 使用翻转轮播 */}
      <View style={[styles.content]}>
        <View style={styles.carousel}>
          {validProducts.length > 0 && (
            <FlipCarousel
              data={validProducts}
              autoPlayInterval={3000}
              animationDuration={500}
              renderItem={renderProductItem}
              pageHeight={155} // 根据实际产品卡片高度调整

              style={styles.flipCarousel}
              contentContainerStyle={styles.flipCarouselContent}
              pageContentStyle={styles.pageContent}
            />
          )}
        </View>
      </View>
    </View>
  );
};

export default MarketingFloor; 