import { JDTextInput } from '@jdreact/jdreact-core-lib';
import { forwardRef } from 'react';
import withClassName from '../../basic/utils/withClassName';
import { useScreen } from '../../../hooks/useScreen';

const TextInput = (props: any, ref: any) => {
  const { style, onChangeText, value, clearButtonMode, placeholder, ...others } = props;
  const { scaleStyle } = useScreen();

  const scaledStyle = style && typeof style === 'object' && !Array.isArray(style)
    ? scaleStyle(style as Record<string, any>)
    : style;

  return (
    <JDTextInput
      ref={ref}
      style={scaledStyle}
      onChangeText={onChangeText}
      value={value}
      clearButtonMode={clearButtonMode}
      placeholder={placeholder}
      {...others}
    />
  );
};

export default withClassName()(forwardRef(TextInput)); 