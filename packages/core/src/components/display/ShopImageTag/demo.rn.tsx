import React from 'react';
import { ScrollView } from 'react-native';
import { View } from '../../basic/View';
import { Text } from '../../basic/Text';
import { ShopImageTag } from './index';
import { CustomTag } from '../CustomTag';
import { TagType } from '../CustomTag/types';

const ShopImageTagDemo: React.FC = () => {
  return (
    <ScrollView style={{ flex: 1 }}>
      <View style={{ padding: 20 }}>
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>基础图片标签</Text>
          <ShopImageTag
            src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
            size={100}
            tag={{
              tagComponent: <CustomTag
                type={TagType.TEXT}
                text="热卖"
                backgroundColor="#ff4d4f"
                textStyle={{ color: '#fff' }}
              />,
              position: 'topLeft'
            }}
          />
        </View>

        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>多个标签位置示例</Text>
          <ShopImageTag
            src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
            size={120}
            tag={[
              {
                tagComponent: <CustomTag
                  type={TagType.TEXT}
                  text="限时"
                  backgroundColor="#ff4d4f"
                  textStyle={{ color: '#fff' }}
                />,
                position: 'topLeft'
              },
              {
                tagComponent: <CustomTag
                  type={TagType.TEXT}
                  text="新品"
                  backgroundColor="#52c41a"
                  textStyle={{ color: '#fff' }}
                />,
                position: 'topRight'
              },
              {
                tagComponent: <CustomTag
                  type={TagType.TEXT}
                  text="优惠"
                  backgroundColor="#faad14"
                  textStyle={{ color: '#fff' }}
                />,
                position: 'bottomLeft'
              },
              {
                tagComponent: <CustomTag
                  type={TagType.TEXT}
                  text="爆款"
                  backgroundColor="#1890ff"
                  textStyle={{ color: '#fff' }}
                />,
                position: 'bottomRight'
              }
            ]}
          />
        </View>

        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>图标标签示例</Text>
          <ShopImageTag
            src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
            size={100}
            tag={{
              tagComponent: <CustomTag
                type={TagType.ICON_PREFIX}
                text="官方"
                prefixIcon="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
                backgroundColor="#fff0f0"
                textStyle={{ color: '#e93b3d' }}
              />,
              position: 'bottomLeft'
            }}
          />
        </View>
        <View style={{ marginBottom: 16 }}>
          <Text style={{ fontSize: 16, fontWeight: 'bold', marginBottom: 8 }}>更多标签类型示例</Text>
          <View style={{ flexDirection: 'row', flexWrap: 'wrap', justifyContent: 'space-around' }}>
            <ShopImageTag
              src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
              size={80}
              tag={{
                tagComponent: <CustomTag
                  type={TagType.IMAGE}
                  tagImage="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
                />,
                position: 'topLeft'
              }}
            />
            
            <ShopImageTag
              src="https://img12.360buyimg.com/imagetools/jfs/t1/196430/7/8716/14416/60d3b3baE0c5b3d63/8f0a134a60ff2981.jpg"
              size={80}
              tag={{
                tagComponent: <CustomTag
                  type={TagType.ICON_SUFFIX}
                  text="超值"
                  suffixIcon="https://img13.360buyimg.com/imagetools/jfs/t1/311381/22/2200/1166/682b258eF1b20f7d2/1ee230aa3824021e.png"
                  backgroundColor="#e6f7ff"
                  textStyle={{ color: '#1890ff' }}
                />,
                position: 'topRight'
              }}
            />
          </View>
        </View>
      </View>
    </ScrollView>
  );
};

export default ShopImageTagDemo;
