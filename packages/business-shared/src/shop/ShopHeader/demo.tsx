import React from 'react';
import { View, Text } from '@tarojs/components';
import ShopHeader from './shopHeader';
import { Title } from '../../../../core/src/components/display/title';
import { CustomTag } from '../../../../core/src/components/display/CustomTag';
import { ContentType } from './types';
import './demo.scss';

/**
 * ShopHeader组件示例
 */
const ShopHeaderDemo: React.FC = () => {
  return (
    <View className="shop-header-demo">
      <View className="page-title">店铺信息展示组件</View>
      
      <Text className="section-title">基础用法 - 标题和标签</Text>
      <View className="demo-section">
        <ShopHeader 
          imageUrl="https://img.example.com/shop/logo.png"
          lines={[
            {
              left: [
                {
                  type: ContentType.TITLE,
                  text: "品牌官方旗舰店",
                    fontSize: 18,
                  style: { 
                    color: '#e93b3d' 
                  }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: "官方认证",
                    backgroundColor: '#f0f9ff',
                  textStyle: { 
                    color: '#1890ff' 
                  }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: "优惠券",
                    showBorder: true,
                    borderColor: '#e93b3d',
                  textStyle: { 
                    color: '#e93b3d' 
                  }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: "满减",
                    backgroundColor: '#fff0f0',
                  textStyle: { 
                    color: '#e93b3d' 
                  }
                }
              ]
            }
          ]}
        />
      </View>
      
      <Text className="section-title">下酒店铺卡片示例</Text>
      <View className="demo-section">
        <ShopHeader 
          imageUrl="https://img10.360buyimg.com/img/jfs/t1/313656/11/2760/13502/682d8341Fd74611c4/d647a2c4bcdf2c76.png"
          imageWidth={72}
          imageHeight={72}
          lines={[
            {
              left: [
                {
                  type: ContentType.TITLE,
                  text: "下酒 (林肯公园店)",
                    fontSize: 20,
                  style: { 
                    color: '#333', 
                    fontWeight: 'bold' 
                  }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: '已收藏',
                    backgroundColor: '#fff9f0',
                  textStyle: { 
                    color: '#996633' 
                  }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '4.9分',  
                    backgroundColor: '#fff0f0',
                  textStyle: { 
                    color: '#e93b3d', 
                    fontWeight: 'bold' 
                  }
                },
                {
                  type: ContentType.TAG,
                  text: '2372条评价', 
                    backgroundColor: '#f5f5f5',
                  textStyle: { 
                    color: '#999' 
                  }
                },
                {
                  type: ContentType.TAG,
                  text: '￥95/人', 
                    backgroundColor: '#f5f5f5',
                  textStyle: { 
                    color: '#999' 
                  }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: '营业至1:00',
                    backgroundColor: '#fff9f0',
                  textStyle: { 
                    color: '#996633' 
                  }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '烧烤',  
                    backgroundColor: '#f5f5f5',
                  textStyle: { 
                    color: '#999' 
                  }
                },
                {
                  type: ContentType.TAG,
                  text: '亦庄', 
                    backgroundColor: '#f5f5f5',
                  textStyle: { 
                    color: '#999' 
                  }
                }
              ],
              right: [
                {
                  type: ContentType.TAG,
                  text: '距您步行321m',
                    backgroundColor: '#f5f5f5',
                  textStyle: { 
                    color: '#999' 
                  }
                }
              ],
              height: 24
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '亦庄烧烤销量榜第1名', 
                    backgroundColor: '#fff9f0',
                  textStyle: { 
                    color: '#996633' 
                  }
                },
                {
                  type: ContentType.TAG,
                  text: '当地人推荐', 
                    backgroundColor: '#fff9f0',
                  textStyle: { 
                    color: '#996633' 
                  }
                },
                {
                  type: ContentType.TAG,
                  text: 'PLUS免费停车', 
                    backgroundColor: '#fff9f0',
                  textStyle: { 
                    color: '#996633' 
                  }
                }
              ]
            }
          ]}
          lineSpacing={6}
        />
      </View>
      
      <Text className="section-title">多个标签使用案例</Text>
      <View className="demo-section">
        <ShopHeader 
          imageUrl="https://img.example.com/shop/logo.png"
          lines={[
            {
              left: [
                {
                  type: ContentType.TITLE,
                  text: "京东自营官方旗舰店",
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '新品', 
                    backgroundColor: '#e6f7ff',
                  textStyle: { 
                    color: '#1890ff' 
                  }
                },
                {
                  type: ContentType.TAG,
                  text: '热卖', 
                    backgroundColor: '#fff2e8',
                  textStyle: { 
                    color: '#fa541c' 
                  }
                },
                {
                  type: ContentType.TAG,
                  text: '促销', 
                    backgroundColor: '#f6ffed',
                  textStyle: { 
                    color: '#52c41a' 
                  }
                }
              ]
            },
            {
              left: [
                {
                  type: ContentType.TAG,
                  text: '免运费',
                    backgroundColor: '#f5f5f5',
                  textStyle: { 
                    color: '#999' 
                  }
                }
              ]
            }
          ]}
        />
      </View>
      
      <Text className="section-title">带标签的标题示例</Text>
      <View className="demo-section">
        <ShopHeader 
          imageUrl="https://img.example.com/shop/logo.png"
          lines={[
            {
              left: [
                {
                  type: ContentType.TITLE,
                  text: "品牌连锁店",
                    fontSize: 18,
                  tags: [
                    {
                      text: "品牌",
                      backgroundColor: "#ff4d4f",
                      textStyle: { color: "#fff" }
                    },
                    {
                      text: "连锁",
                      backgroundColor: "#1890ff",
                      textStyle: { color: "#fff" }
                    }
                  ],
                  style: {
                    color: '#333',
                    fontWeight: 'bold'
                  }
                }
              ]
            }
          ]}
        />
      </View>
      
      <Text className="section-title">多行标题示例</Text>
      <View className="demo-section">
        <ShopHeader 
          imageUrl="https://img.example.com/shop/logo.png"
          lines={[
            {
              left: [
                {
                  type: ContentType.TITLE,
                  text: "这是一个标题很长的店铺名称，需要自动换行显示内容",
                  fontSize: 16,
                  lines: 2,
                  style: {
                    color: '#333'
                  }
                }
              ]
            }
          ]}
        />
      </View>
    </View>
  );
};

export default ShopHeaderDemo; 