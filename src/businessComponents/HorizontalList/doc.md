# HorizontalList 横向滚动列表

横向滚动列表组件，支持展示多种类型的卡片，如优惠券和商品卡片。在不同平台上使用不同的实现：Taro/Web平台使用ScrollView，React Native平台使用FlatList。

## 引入

```jsx
import { HorizontalList } from '@/businessComponents/HorizontalList';
```

## 代码演示

### 优惠券列表

```jsx
import React from 'react';
import { HorizontalList } from '@/businessComponents/HorizontalList';

export default () => {
  const couponItems = [
    {
      type: 'coupon',
      data: {
        title: '限时优惠券',
        amount: 15,
        condition: '满100元可用',
        backgroundImage: 'https://img12.360buyimg.com/imagetools/jfs/t1/196301/17/27456/7388/62ed2fa5E6fb2ddd8/86928dcbec9a4521.png',
      }
    },
    {
      type: 'coupon',
      data: {
        title: '新人专享',
        amount: 30,
        condition: '无门槛',
        backgroundImage: 'https://img12.360buyimg.com/imagetools/jfs/t1/196301/17/27456/7388/62ed2fa5E6fb2ddd8/86928dcbec9a4521.png',
      }
    }
  ];

  return (
    <HorizontalList 
      items={couponItems}
      itemSpacing={12}
      horizontalPadding={16}
    />
  );
};
```

### 商品列表

```jsx
import React from 'react';
import { HorizontalList } from '@/businessComponents/HorizontalList';

export default () => {
  const productItems = [
    {
      type: 'product',
      data: {
        title: '商品示例1',
        imageSrc: 'https://img12.360buyimg.com/imagetools/jfs/t1/180774/28/24624/291964/6262d2deE5183a6e2/a6a3c178cfd77ea6.jpg',
        currentPrice: 128,
        originalPrice: 256
      }
    },
    {
      type: 'product',
      data: {
        title: '商品示例2',
        imageSrc: 'https://img12.360buyimg.com/imagetools/jfs/t1/180774/28/24624/291964/6262d2deE5183a6e2/a6a3c178cfd77ea6.jpg',
        currentPrice: 99.9,
        originalPrice: 199
      }
    }
  ];

  return (
    <HorizontalList 
      items={productItems}
      itemWidth={120}
      itemSpacing={12}
      horizontalPadding={16}
    />
  );
};
```

### 混合内容列表

```jsx
import React from 'react';
import { HorizontalList } from '@/businessComponents/HorizontalList';

export default () => {
  const items = [
    {
      type: 'coupon',
      data: {
        title: '限时优惠券',
        amount: 15,
        condition: '满100元可用',
        backgroundImage: 'https://img12.360buyimg.com/imagetools/jfs/t1/196301/17/27456/7388/62ed2fa5E6fb2ddd8/86928dcbec9a4521.png',
      }
    },
    {
      type: 'product',
      data: {
        title: '商品示例',
        imageSrc: 'https://img12.360buyimg.com/imagetools/jfs/t1/180774/28/24624/291964/6262d2deE5183a6e2/a6a3c178cfd77ea6.jpg',
        currentPrice: 128,
        originalPrice: 256
      }
    }
  ];

  return (
    <HorizontalList 
      items={items}
      itemSpacing={12}
      horizontalPadding={16}
      showScrollbar={true}
    />
  );
};
```

### 自定义渲染

```jsx
import React from 'react';
import { HorizontalList } from '@/businessComponents/HorizontalList';
import { View, Text } from '@/components/basic';

export default () => {
  const items = [/* ... */];

  const renderItem = (item, index) => {
    if (item.type === 'coupon') {
      return (
        <View key={index} className="custom-item">
          <View className="custom-tag">优惠</View>
          <View className="custom-title">{item.data.title}</View>
          <View className="custom-amount">¥{item.data.amount}</View>
        </View>
      );
    }
    
    // 默认渲染
    return null;
  };

  return (
    <HorizontalList 
      items={items}
      renderItem={renderItem}
    />
  );
};
```

### 点击和可见性事件

```jsx
import React from 'react';
import { HorizontalList } from '@/businessComponents/HorizontalList';

export default () => {
  const items = [/* ... */];

  // 处理列表项点击
  const handleItemClick = (item, index) => {
    console.log('点击了第', index, '个项目:', item);
    
    if (item.type === 'coupon') {
      // 处理优惠券点击，如领取优惠券
      console.log('领取优惠券:', item.data.title);
    } else if (item.type === 'product') {
      // 处理商品点击，如跳转商品详情
      console.log('查看商品详情:', item.data.title);
    }
  };

  // 处理可见项变化（仅RN）
  const handleViewableItemsChanged = ({ viewableItems, changed }) => {
    console.log('当前可见项目数量:', viewableItems.length);
    console.log('变化的项目:', changed);
    
    // 可用于埋点统计、预加载等
    viewableItems.forEach((viewableItem) => {
      const { item, index } = viewableItem;
      console.log(`项目 ${index} 可见:`, item);
    });
  };

  return (
    <HorizontalList 
      items={items}
      onItemClick={handleItemClick}
      onViewableItemsChanged={handleViewableItemsChanged}
    />
  );
};
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| items | 列表数据 | `ListItemType[]` | `[]` |
| showScrollbar | 是否显示水平滚动条 | `boolean` | `false` |
| itemSpacing | 列表项之间的间距 | `number` | `12` |
| horizontalPadding | 列表左右内边距 | `number` | `16` |
| itemWidth | 列表项宽度 | `number \| string` | `'auto'` |
| renderItem | 自定义渲染某个列表项的方法 | `(item: ListItemType, index: number) => React.ReactNode` | - |
| emptyComponent | 列表为空时的占位内容 | `React.ReactNode` | - |
| onEndReached | 列表滚动到末尾时触发的事件 | `() => void` | - |
| onScroll | 列表滚动事件 | `(event: any) => void` | - |
| onItemClick | 列表项点击事件 | `(item: ListItemType, index: number) => void` | - |
| onViewableItemsChanged | 可见列表项变化事件（仅RN） | `(info: {viewableItems: ViewToken[]; changed: ViewToken[]}) => void` | - |
| className | 自定义根元素类名 (Taro/Web) | `string` | - |
| style | 自定义根元素内联样式 (Taro/Web) | `React.CSSProperties` | - |
| containerStyle | 自定义根元素样式 (RN) | `ViewStyle` | - |
| listStyle | 自定义列表容器样式 (RN) | `ViewStyle` | - |

### ListItemType

列表项类型定义：

```typescript
type ListItemType = 
  | { type: 'coupon'; data: CouponCardProps } 
  | { type: 'product'; data: ProductCardProps };
```

## 注意事项

- 组件内部集成了CouponCard和ProductCard两种组件
- CouponCard默认尺寸为72×110，背景图片尺寸应匹配组件尺寸
- ProductCard默认尺寸受imageSize控制，默认为120
- 列表末尾会自动添加一个空白间距，等于horizontalPadding的值，避免最后一个元素紧贴边缘
- RN版本支持onEndReached触发加载更多功能，可用于分页加载数据
- 自定义渲染时，在RN环境下需确保返回的是有效的React元素
- onItemClick在所有平台都使用View的onClick方法，保持一致的API
- onViewableItemsChanged仅在React Native平台生效，Web平台暂不支持此功能
- 点击事件会传递完整的item对象和索引，便于处理不同类型的点击逻辑 