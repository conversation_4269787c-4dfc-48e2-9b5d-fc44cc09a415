import { StyleSheet, ViewStyle, TextStyle, ImageStyle } from 'react-native';

interface CouponCardStyles {
  container: ViewStyle;
  background: ImageStyle;
  content: ViewStyle;
  title: TextStyle;
  titleNoCondition: TextStyle;
  amount: ViewStyle;
  condition: TextStyle;
  priceText: TextStyle;
  symbolText: TextStyle;
}

const styles = StyleSheet.create<CouponCardStyles>({
  container: {
    borderRadius: 8,
    flexDirection: 'column',
    overflow: 'hidden',
    position: 'relative',
  },
  background: {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  content: {
    position: 'relative',
    zIndex: 1,
    flexDirection: 'column',
    paddingHorizontal: 8,
    height: '100%',
    justifyContent: 'flex-start',
  },
  title: {
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 12,
    lineHeight: 12,
    letterSpacing: 0,
    color: '#8F4100',
    textAlign: 'center',
    marginTop: 8,
    marginBottom: 6,
  },
  titleNoCondition: {
    fontFamily: 'PingFang SC',
    fontWeight: '600',
    fontSize: 12,
    lineHeight: 12,
    letterSpacing: 0,
    color: '#8F4100',
    textAlign: 'center',
    marginTop: 13,
    marginBottom: 6,
  },
  amount: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  condition: {
    fontFamily: 'PingFang SC',
    fontWeight: '400',
    fontSize: 10,
    lineHeight: 9,
    letterSpacing: 0,
    color: '#8F4100',
    textAlign: 'center',
  },
  priceText: {
    fontFamily: 'JDZhengHT-EN',
    fontWeight: '400',
    fontSize: 26,
    lineHeight: 26,
    letterSpacing: 0,
    color: '#8F4100',
  },
  symbolText: {
    fontFamily: 'JDZhengHT-EN',
    fontWeight: '400',
    fontSize: 14,
    lineHeight: 14,
    letterSpacing: 0,
    color: '#8F4100',
  }
});

export default styles; 