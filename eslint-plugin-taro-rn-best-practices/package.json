{"name": "eslint-plugin-taro-rn-best-practices", "version": "1.0.0", "description": "ESLint plugin for enforcing Taro React Native best practices", "main": "lib/index.js", "bin": {"taro-rn-lint": "./bin/cli.js"}, "scripts": {"test": "jest", "build": "tsc", "prepublish": "npm run build"}, "keywords": ["eslint", "eslintplugin", "taro", "react-native"], "author": "Your Name", "license": "MIT", "dependencies": {"@typescript-eslint/utils": "^5.59.0"}, "devDependencies": {"@types/eslint": "^8.37.0", "@types/jest": "^29.5.0", "@types/node": "^18.15.11", "eslint": "^8.38.0", "jest": "^29.5.0", "ts-jest": "^29.1.0", "typescript": "^5.0.4"}, "peerDependencies": {"eslint": ">=7.0.0"}}