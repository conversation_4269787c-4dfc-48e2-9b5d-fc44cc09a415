# 🚀 开发工作流指南

## ⚡ 一键发布 - 最简单的方式

### 日常开发发布 (推荐)
```bash
# 修复版本 (1.0.0 -> 1.0.1) - 最常用
yarn quick:patch

# 功能版本 (1.0.0 -> 1.1.0)
yarn quick:minor

# 重大版本 (1.0.0 -> 2.0.0)
yarn quick:major
```

### 完整发布流程 (生产环境)
```bash
# 修复版本 - 包含完整检查
yarn release:patch

# 功能版本
yarn release:minor

# 重大版本
yarn release:major

# 预发布版本
yarn release:beta
```

## 📋 典型开发场景

### 场景1: 修复 Bug
```bash
# 1. 修改代码
cd packages/core/src/components/Button
# ... 修复 bug

# 2. 一键发布
yarn quick:patch
```

### 场景2: 添加新功能
```bash
# 1. 开发新组件
cd packages/core/src/components
mkdir NewComponent
# ... 开发代码

# 2. 一键发布
yarn quick:minor
```

### 场景3: 重大更新
```bash
# 1. 重构代码
# ... 破坏性更改

# 2. 完整发布流程
yarn release:major
```

## 🔄 发布流程对比

### 快速发布 (`yarn quick:*`)
✅ **适用于**: 日常开发、小修改、快速迭代
- 🚀 速度快 (~2-3分钟)
- 🔍 基础检查 (构建 + 测试)
- 📝 自动提交未保存的更改
- ⚡ 跳过部分检查以提高速度

### 完整发布 (`yarn release:*`)
✅ **适用于**: 生产发布、重要更新、正式版本
- 🛡️ 完整检查 (~5-10分钟)
- 🔒 严格的 CI/CD 检查
- 📊 详细的发布报告
- 🏷️ 自动生成变更日志

## 📝 发布前检查清单

### 自动检查 (脚本会自动执行)
- [x] 代码构建成功
- [x] 测试通过
- [x] 依赖关系正确
- [x] 版本号更新
- [x] Git 标签创建
- [x] npm 发布成功

### 手动检查 (建议)
- [ ] 功能测试完成
- [ ] 文档已更新
- [ ] 破坏性更改已记录
- [ ] 团队成员已通知

## 🚨 发布失败处理

### 常见问题及解决方案

#### 1. 测试失败
```bash
# 查看测试结果
yarn test

# 修复后重新发布
yarn quick:patch
```

#### 2. 构建失败
```bash
# 检查构建错误
yarn build

# 修复后重新发布
yarn quick:patch
```

#### 3. npm 发布权限问题
```bash
# 检查登录状态
npm whoami

# 重新登录
npm login --registry=http://npm.jd.com/

# 重新发布
yarn quick:patch
```

#### 4. 版本冲突
```bash
# 查看当前版本
npx lerna list

# 手动设置版本
npx lerna version --no-git-tag-version

# 重新发布
yarn quick:patch
```

## 🔧 高级用法

### 只发布特定包
```bash
# 只发布核心包
yarn publish:core

# 只发布共享包
yarn publish:shared

# 只发布业务包
yarn publish:groupon
```

### 预发布版本
```bash
# 发布 beta 版本
yarn release:beta

# 发布到特定 tag
npx lerna publish --dist-tag next
```

### 回滚发布
```bash
# 回滚到上一个版本
npm dist-tag add @jd/lifeui-core@1.0.0 latest

# 撤销发布 (24小时内)
npm unpublish @jd/lifeui-core@1.0.1
```

## 📊 发布监控

### 检查发布状态
```bash
# 查看包信息
npm view @jd/lifeui-core

# 查看所有版本
npm view @jd/lifeui-core versions --json

# 查看下载统计
npm view @jd/lifeui-core downloads
```

### 验证发布
```bash
# 在新项目中测试安装
mkdir test-install
cd test-install
npm init -y
npm install @jd/lifeui-core@latest
```

## 💡 最佳实践

### 1. 提交信息规范
```bash
# 使用 Conventional Commits
git commit -m "feat: add new button component"
git commit -m "fix: resolve button click issue"
git commit -m "docs: update README"
```

### 2. 发布频率
- 🐛 **Bug 修复**: 立即发布 patch 版本
- ✨ **新功能**: 每周发布 minor 版本
- 💥 **重大更改**: 每月发布 major 版本

### 3. 版本策略
- `patch`: Bug 修复、文档更新、小优化
- `minor`: 新功能、新组件、向后兼容的更改
- `major`: 破坏性更改、API 重构、架构调整

### 4. 团队协作
- 📢 重大版本发布前通知团队
- 📝 维护详细的变更日志
- 🔄 定期同步依赖版本
- 📋 建立发布审查流程

## 🎯 快速参考

```bash
# 最常用命令
yarn quick:patch        # 日常 bug 修复
yarn quick:minor         # 新功能发布
yarn release:major       # 重大版本发布

# 检查命令
yarn check:changed       # 查看变更的包
yarn deps:check         # 检查依赖状态
yarn ci:check           # 完整 CI 检查

# 维护命令
yarn clean:all          # 清理所有依赖
yarn reset              # 重置项目状态
```

记住：**大部分情况下，您只需要运行 `yarn quick:patch` 就够了！** 🚀
